import Combine
import SwiftData
import SwiftUI

/// 响应式数据管理服务
///
/// 作为新架构中的响应式数据管理器，直接连接SwiftData的@Query，为ViewModel层提供统一的数据接口。
@Observable
final class DataManagement: ObservableObject {

  // MARK: - 数据属性

  /// 所有卡片数据
  var cards: [CardModel]

  /// 主分类数据
  var mainCategories: [TransactionMainCategoryModel]

  /// 子分类数据
  var subCategories: [TransactionSubCategoryModel]

  /// 货币数据
  var currencies: [CurrencyModel]

  /// 最近交易数据（用于首页和常用功能）
  var recentTransactions: [TransactionModel]

  /// 所有交易数据（用于记录页面等需要完整数据的场景）
  var allTransactions: [TransactionModel]

  /// 聊天消息数据
  var chatMessages: [ChatMessageModel]

  // MARK: - 系统服务

  /// 触觉反馈管理器
  let hapticManager = HapticFeedbackManager.shared

  // MARK: - 计算属性

  /// 基础货币
  var baseCurrency: CurrencyModel? {
    currencies.first { $0.isBaseCurrency }
  }

  /// 基础货币代码
  var baseCurrencyCode: String {
    UserDefaults.standard.string(forKey: "baseCurrencyCode") ?? "CNY"
  }

  /// 基础货币符号
  var baseCurrencySymbol: String {
    baseCurrency?.symbol ?? currencies.first { $0.code == baseCurrencyCode }?.symbol ?? "¥"
  }

  /// 支出类别（过滤支出相关的主分类）
  var expenseCategories: [TransactionMainCategoryModel] {
    mainCategories.filter { $0.type == TransactionType.expense.rawValue }.sorted {
      $0.order < $1.order
    }
  }

  /// 收入类别（过滤收入相关的主分类）
  var incomeCategories: [TransactionMainCategoryModel] {
    mainCategories.filter { $0.type == TransactionType.income.rawValue }.sorted {
      $0.order < $1.order
    }
  }

  // MARK: - Initialization

  /// DataManagement 的构造函数，接收数据引用
  ///
  /// **使用方法:**
  /// 在你的主视图层级中，通过@Query获取数据，然后传递给DataManagement
  ///
  /// **示例:**
  /// ```swift
  /// struct ContentView: View {
  ///     @Query private var cards: [CardModel]
  ///     @Query private var transactions: [TransactionModel]
  ///     // ... 其他@Query
  ///
  ///     private var dataManager: DataManagement {
  ///         DataManagement(
  ///             cards: cards,
  ///             mainCategories: mainCategories,
  ///             subCategories: subCategories,
  ///             currencies: currencies,
  ///             recentTransactions: Array(transactions.prefix(100)),
  ///             allTransactions: transactions,
  ///             chatMessages: chatMessages
  ///         )
  ///     }
  ///
  ///     var body: some View {
  ///         HomeTestView(viewModel: HomeVM(dataManager: dataManager))
  ///     }
  /// }
  /// ```
  init(
    cards: [CardModel] = [],
    mainCategories: [TransactionMainCategoryModel] = [],
    subCategories: [TransactionSubCategoryModel] = [],
    currencies: [CurrencyModel] = [],
    recentTransactions: [TransactionModel] = [],
    allTransactions: [TransactionModel] = [],
    chatMessages: [ChatMessageModel] = []
  ) {
    self.cards = cards
    self.mainCategories = mainCategories
    self.subCategories = subCategories
    self.currencies = currencies
    self.recentTransactions = recentTransactions
    self.allTransactions = allTransactions
    self.chatMessages = chatMessages
  }

  // MARK: - Data Update Methods

  /// 更新所有数据（支持响应式更新）
  func updateData(
    cards: [CardModel],
    mainCategories: [TransactionMainCategoryModel],
    subCategories: [TransactionSubCategoryModel],
    currencies: [CurrencyModel],
    recentTransactions: [TransactionModel],
    allTransactions: [TransactionModel],
    chatMessages: [ChatMessageModel]
  ) {
    self.cards = cards
    self.mainCategories = mainCategories
    self.subCategories = subCategories
    self.currencies = currencies
    self.recentTransactions = recentTransactions
    self.allTransactions = allTransactions
    self.chatMessages = chatMessages
  }

  // MARK: - Basic Lookup Methods (常用的基础查找方法)

  /// 根据ID查找卡片
  func findCard(by id: UUID?) -> CardModel? {
    guard let id = id else { return nil }
    return cards.first { $0.id == id }
  }

  /// 根据ID查找主分类（ID是String类型）
  func findMainCategory(by id: String) -> TransactionMainCategoryModel? {
    mainCategories.first { $0.id == id }
  }

  /// 根据ID查找子分类（ID是String类型）
  func findSubCategory(by id: String) -> TransactionSubCategoryModel? {
    subCategories.first { $0.id == id }
  }

  /// 根据代码查找货币
  func findCurrency(by code: String) -> CurrencyModel? {
    currencies.first { $0.code == code }
  }

  /// 根据主分类ID查找所有子分类
  func findSubCategories(for mainCategoryId: String) -> [TransactionSubCategoryModel] {
    subCategories.filter { $0.mainId == mainCategoryId }
      .sorted { $0.order < $1.order }
  }

  /// 创建交易与卡片的配对数据
  func createTransactionCardPairs(
    from transactions: [TransactionModel]
  ) -> [(transaction: TransactionModel, card: CardModel?)] {
    transactions.map { transaction in
      let card =
        findCard(by: transaction.fromCardId)
        ?? findCard(by: transaction.toCardId)
      return (transaction: transaction, card: card)
    }
  }

  /// 获取交易分类信息
  func getCategoryInfo(for categoryId: String?) -> (name: String, icon: IconType?) {
    guard let categoryId = categoryId else {
      return ("未知类别", nil)
    }

    // 先尝试查找子分类
    if let subCategory = findSubCategory(by: categoryId),
      let mainCategory = findMainCategory(by: subCategory.mainId)
    {
      return ("\(mainCategory.name)-\(subCategory.name)", subCategory.icon)
    }

    // 再查找主分类
    if let mainCategory = findMainCategory(by: categoryId) {
      return (mainCategory.name, mainCategory.icon)
    }

    return ("未知类别", nil)
  }
}

// MARK: - Environment Support

/// DataManagement 的 Environment Key
private struct DataManagementKey: EnvironmentKey {
  static let defaultValue = DataManagement()
}

extension EnvironmentValues {
  /// New架构数据管理器
  var dataManager: DataManagement {
    get { self[DataManagementKey.self] }
    set { self[DataManagementKey.self] = newValue }
  }
}

// MARK: - View Extensions

extension View {
  /// 便利方法：注入数据管理器到环境中
  func withDataManager(_ dataManager: DataManagement) -> some View {
    environment(\.dataManager, dataManager)
  }
}
