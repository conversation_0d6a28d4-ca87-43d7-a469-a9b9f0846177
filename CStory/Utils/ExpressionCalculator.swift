import Foundation

/// 表达式计算器
///
/// 支持基本四则运算表达式解析的工具类，专为数字键盘的表达式计算需求设计。
struct ExpressionCalculator {

  // MARK: - Constants

  /// 支持的运算符集合
  private static let operators: Set<Character> = ["+", "-"]

  // MARK: - Public Methods

  /// 计算表达式结果
  /// - Parameter expression: 要计算的数学表达式
  /// - Returns: 计算结果，如果表达式无效则返回nil
  static func evaluate(_ expression: String) -> Double? {
    var expr = expression.trimmingCharacters(in: .whitespaces)

    // 预处理：移除末尾的运算符或小数点
    if let lastChar = expr.last, operators.contains(lastChar) || lastChar == "." {
      expr.removeLast()
    }

    // 处理空表达式或单个负号
    if expr.isEmpty || expr == "-" {
      return 0
    }

    // 如果是纯数字，直接返回
    if let number = Double(expr) {
      return number
    }

    // 处理加法
    if let plusIndex = expr.firstIndex(of: "+") {
      let lhs = String(expr[..<plusIndex])
      let rhs = String(expr[expr.index(after: plusIndex)...])
      if rhs.isEmpty {
        return Double(lhs)
      }
      if let left = Double(lhs), let right = Double(rhs) {
        return left + right
      }
    }

    // 处理减法
    if let minusIndex = expr.lastIndex(of: "-") {
      if minusIndex == expr.startIndex {
        // 负数情况
        let value = String(expr[expr.index(after: minusIndex)...])
        if value.isEmpty {
          return 0
        }
        if let number = Double(value) {
          return -number
        }
      } else {
        // 减法情况
        let lhs = String(expr[..<minusIndex])
        let rhs = String(expr[expr.index(after: minusIndex)...])
        if rhs.isEmpty {
          return Double(lhs)
        }
        if let left = Double(lhs), let right = Double(rhs) {
          return left - right
        }
      }
    }

    return nil
  }

  /// 格式化数字结果
  /// - Parameters:
  ///   - number: 要格式化的数字
  ///   - maxDecimalPlaces: 最大小数位数
  /// - Returns: 格式化后的字符串
  static func formatResult(_ number: Double, maxDecimalPlaces: Int = 2) -> String {
    return NumberFormatService.shared.formatCalculationResult(number, maxDecimals: maxDecimalPlaces)
  }

  /// 预处理表达式
  /// - Parameter expression: 原始表达式
  /// - Returns: 处理后的表达式，确保格式正确
  static func preprocess(_ expression: String) -> String {
    var processedExpr = expression.trimmingCharacters(in: .whitespaces)

    // 处理末尾的运算符或小数点
    if let lastChar = processedExpr.last, operators.contains(lastChar) || lastChar == "." {
      processedExpr.removeLast()
    }

    // 处理空表达式或单个负号
    if processedExpr.isEmpty || processedExpr == "-" {
      return "0"
    }

    // 如果是纯数字，直接返回
    if Double(processedExpr) != nil {
      return processedExpr
    }

    // 计算表达式并返回格式化结果
    if let result = evaluate(processedExpr) {
      return formatResult(result)
    }

    return "错误的公式计算"
  }

  /// 检查表达式是否包含运算符（排除开头的负号）
  /// - Parameter expression: 要检查的表达式
  /// - Returns: 是否包含有效的运算符
  static func containsOperator(_ expression: String) -> Bool {
    let text = expression.trimmingCharacters(in: .whitespaces)

    // 检查是否包含运算符（排除开头的负号）
    if text.hasPrefix("-") && text.count > 1 {
      // 如果以负号开头，检查剩余部分是否包含运算符
      let remainingText = String(text.dropFirst())
      return remainingText.contains(where: { operators.contains($0) })
    } else {
      // 检查整个文本是否包含运算符
      return text.contains(where: { operators.contains($0) })
    }
  }
}
