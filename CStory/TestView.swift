//
//  ContentView.swift
//  CStory
//
//  Created by NZUE on 2025/6/10.
//

import SwiftData
import SwiftUI

struct TestView: View {
  @Query private var transactionModels: [TransactionModel]
  @Query private var transactionCategories: [TransactionMainCategoryModel]
  @Query private var cards: [CardModel]
  @Query(sort: \CurrencyModel.order) private var currencies: [CurrencyModel]
  @Environment(\.modelContext) private var modelContext

  @State private var promptText = ""
  @State private var selectedModule = 0
  @State private var isCardsExpanded = false
  @State private var isCategoriesExpanded = false
  @State private var isCurrenciesExpanded = false
  @State private var isTransactionsExpanded = false

  // 模块列表
  private let modules = [
    ("AI提示词", "doc.text"),
    ("卡片信息", "creditcard"),
    ("交易类别", "folder"),
    ("货币信息", "dollarsign.circle"),
    ("交易记录", "list.bullet"),
    ("系统操作", "gear"),
  ]

  var body: some View {
    NavigationView {
      VStack(spacing: 0) {
        // 顶部横向滚动模块按钮
        ScrollView(.horizontal, showsIndicators: false) {
          HStack(spacing: 12) {
            ForEach(0..<modules.count, id: \.self) { index in
              Button(action: {
                selectedModule = index
              }) {
                HStack(spacing: 8) {
                  Image(systemName: modules[index].1)
                    .font(.system(size: 14, weight: .medium))
                  Text(modules[index].0)
                    .font(.system(size: 14, weight: .medium))
                }
                .foregroundColor(selectedModule == index ? .white : .primary)
                .padding(.horizontal, 16)
                .padding(.vertical, 10)
                .background(
                  selectedModule == index
                    ? Color.blue
                    : Color(.systemGray6)
                )
                .cornerRadius(20)
              }
            }
          }
          .padding(.horizontal, 16)
        }
        .padding(.vertical, 12)
        .background(Color(.systemBackground))

        Divider()

        // 内容区域
        ScrollView {
          VStack(spacing: 20) {
            switch selectedModule {
            case 0:
              // AI 提示词内容
              AIPromptContentView(promptText: $promptText, modelContext: modelContext)
            case 1:
              // 卡片信息
              CardInfoSection(
                cards: cards,
                modelContext: modelContext,
                isExpanded: $isCardsExpanded
              )
            case 2:
              // 交易类别
              CategoryInfoSection(
                transactionCategories: transactionCategories,
                modelContext: modelContext,
                isExpanded: $isCategoriesExpanded
              )
            case 3:
              // 货币信息
              CurrencyInfoSection(
                currencies: currencies,
                modelContext: modelContext,
                isExpanded: $isCurrenciesExpanded
              )
            case 4:
              // 交易记录
              TransactionInfoSection(
                transactionModels: transactionModels,
                modelContext: modelContext,
                isExpanded: $isTransactionsExpanded
              )
            case 5:
              // 系统操作
              SystemOperationSection(
                currencies: currencies,
                transactionCategories: transactionCategories,
                cards: cards,
                transactionModels: transactionModels,
                modelContext: modelContext
              )
            default:
              EmptyView()
            }
          }
          .padding()
        }
      }
      .navigationTitle("数据调试")
      .navigationBarTitleDisplayMode(.inline)
      .onAppear {
        // 预先生成AI提示词
        if promptText.isEmpty {
          promptText = AIPromptService.shared.generateSystemPrompt(modelContext: modelContext)
        }
      }
    }
  }
}

// MARK: - AI 提示词内容展示组件

struct AIPromptContentView: View {
  @Binding var promptText: String
  let modelContext: ModelContext

  var body: some View {
    VStack(alignment: .leading, spacing: 16) {
      HStack {
        Text("AI 提示词内容")
          .font(.headline)
          .foregroundColor(.primary)

        Spacer()

        Button(action: {
          promptText = AIPromptService.shared.generateSystemPrompt(modelContext: modelContext)
        }) {
          HStack(spacing: 4) {
            Image(systemName: "arrow.clockwise")
            Text("刷新")
          }
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.blue)
        }

        Button(action: {
          UIPasteboard.general.string = promptText
        }) {
          HStack(spacing: 4) {
            Image(systemName: "doc.on.clipboard")
            Text("复制")
          }
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.green)
        }
      }

      // 提示词内容显示
      Text(promptText)
        .font(.system(.caption, design: .monospaced))
        .textSelection(.enabled)
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
  }
}

// MARK: - AI 提示词查看组件

struct AIPromptSection: View {
  @Binding var showPromptSheet: Bool
  @Binding var promptText: String
  let modelContext: ModelContext

  var body: some View {
    VStack(alignment: .leading, spacing: 12) {
      HStack {
        Text("AI 提示词")
          .font(.headline)
          .foregroundColor(.primary)

        Spacer()

        Button(action: {
          promptText = AIPromptService.shared.generateSystemPrompt(modelContext: modelContext)
        }) {
          Image(systemName: "arrow.clockwise")
            .foregroundColor(.blue)
            .font(.system(size: 14, weight: .medium))
        }
      }

      HStack(spacing: 12) {
        Button(action: {
          showPromptSheet = true
        }) {
          HStack {
            Image(systemName: "doc.text.magnifyingglass")
            Text("查看提示词")
          }
          .font(.system(size: 14))
          .foregroundColor(.white)
          .padding(.horizontal, 16)
          .padding(.vertical, 10)
          .background(Color.blue)
          .cornerRadius(8)
        }

        Button(action: {
          UIPasteboard.general.string = promptText
        }) {
          HStack {
            Image(systemName: "doc.on.clipboard")
            Text("复制提示词")
          }
          .font(.system(size: 14))
          .foregroundColor(.white)
          .padding(.horizontal, 16)
          .padding(.vertical, 10)
          .background(Color.green)
          .cornerRadius(8)
        }
      }
    }
  }
}

// MARK: - 提示词展示弹窗

struct PromptDisplaySheet: View {
  let promptText: String
  @Environment(\.dismiss) private var dismiss

  var body: some View {
    NavigationView {
      ScrollView {
        VStack(alignment: .leading, spacing: 16) {
          Text("系统提示词内容")
            .font(.headline)
            .foregroundColor(.primary)
            .padding(.horizontal)

          Text(promptText)
            .font(.system(.caption, design: .monospaced))
            .padding()
            .textSelection(.enabled)
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
            .padding(.horizontal)
        }
        .padding(.vertical)
      }
      .navigationTitle("AI 提示词")
      .navigationBarTitleDisplayMode(.inline)
      .navigationBarBackButtonHidden(true)
      .toolbar {
        ToolbarItem(placement: .navigationBarLeading) {
          Button("关闭") {
            dismiss()
          }
        }
        ToolbarItem(placement: .navigationBarTrailing) {
          Button(action: {
            UIPasteboard.general.string = promptText
          }) {
            HStack {
              Image(systemName: "doc.on.clipboard")
              Text("复制")
            }
          }
        }
      }
    }
  }
}

// MARK: - 卡片信息组件

struct CardInfoSection: View {
  let cards: [CardModel]
  let modelContext: ModelContext
  @Binding var isExpanded: Bool

  var body: some View {
    VStack(alignment: .leading, spacing: 16) {
      Text("卡片信息 (\(cards.count))")
        .font(.headline)
        .foregroundColor(.primary)

      if cards.isEmpty {
        Text("暂无资产数据")
          .foregroundColor(.secondary)
          .padding()
          .background(Color.gray.opacity(0.1))
          .cornerRadius(8)
      } else {
        let sortedCards = cards.sorted { $0.order < $1.order }
        let savingCards = sortedCards.filter { !$0.isCredit }
        let creditCards = sortedCards.filter { $0.isCredit }

        // 储蓄卡部分
        if !savingCards.isEmpty {
          VStack(alignment: .leading, spacing: 12) {
            HStack {
              Image(systemName: "banknote")
                .foregroundColor(.green)
              Text("储蓄卡 (\(savingCards.count))")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            }

            ForEach(savingCards) { card in
              cardDetailView(card: card)
            }
          }
        }

        // 信用卡部分
        if !creditCards.isEmpty {
          VStack(alignment: .leading, spacing: 12) {
            HStack {
              Image(systemName: "creditcard")
                .foregroundColor(.red)
              Text("信用卡 (\(creditCards.count))")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            }

            ForEach(creditCards) { card in
              cardDetailView(card: card)
            }
          }
        }
      }

      // 清除按钮
      HStack {
        Spacer()
        Button(action: {
          for card in cards {
            modelContext.delete(card)
          }
          try? modelContext.save()
        }) {
          HStack(spacing: 8) {
            Image(systemName: "trash.fill")
              .font(.system(size: 14, weight: .medium))
            Text("清除全部卡片")
              .font(.system(size: 15, weight: .medium))
          }
          .foregroundColor(.white)
          .padding(.horizontal, 20)
          .padding(.vertical, 12)
          .background(Color.red)
          .cornerRadius(10)
        }
        .disabled(cards.isEmpty)
        Spacer()
      }
    }
  }

  @ViewBuilder
  private func cardDetailView(card: CardModel) -> some View {
    VStack(alignment: .leading, spacing: 12) {
      // 第一行：名称和类型
      HStack {
        Text(card.name.isEmpty ? "未命名卡片" : card.name)
          .font(.system(size: 16, weight: .semibold))
          .foregroundColor(.primary)
        Spacer()
        Text(card.isCredit ? "信用卡" : "储蓄卡")
          .font(.caption)
          .padding(.horizontal, 8)
          .padding(.vertical, 3)
          .background(card.isCredit ? Color.red.opacity(0.1) : Color.green.opacity(0.1))
          .foregroundColor(card.isCredit ? .red : .green)
          .cornerRadius(6)
      }

      // 银行信息
      if !card.bankName.isEmpty || !card.cardNumber.isEmpty {
        HStack {
          if !card.bankName.isEmpty {
            Text("银行: \(card.bankName)")
              .font(.system(size: 13))
              .foregroundColor(.primary)
          }
          Spacer()
          if !card.cardNumber.isEmpty {
            Text("卡号: ****\(card.cardNumber)")
              .font(.system(size: 13))
              .foregroundColor(.secondary)
          }
        }
      }

      // 余额信息
      VStack(alignment: .leading, spacing: 4) {
        HStack {
          Text("余额:")
            .font(.system(size: 13))
            .foregroundColor(.secondary)
          Text(
            "\(CurrencyService.shared.formatCurrency(card.balance, symbol: card.symbol, showDecimals: true))"
          )
          .font(.system(size: 14, weight: .semibold))
          .foregroundColor(card.balance >= 0 ? .green : .red)
          Spacer()
          Text("\(card.currency) (\(card.symbol))")
            .font(.system(size: 13))
            .foregroundColor(.secondary)
        }

        // 信用卡额度信息
        if card.isCredit && card.credit > 0 {
          HStack {
            Text("信用额度:")
              .font(.system(size: 13))
              .foregroundColor(.secondary)
            Text(
              "\(CurrencyService.shared.formatCurrency(card.credit, symbol: card.symbol, showDecimals: true))"
            )
            .font(.system(size: 13, weight: .medium))
            .foregroundColor(.blue)
            Spacer()
            let availableCredit = card.credit + card.balance
            Text(
              "可用: \(CurrencyService.shared.formatCurrency(availableCredit, symbol: card.symbol, showDecimals: true))"
            )
            .font(.system(size: 13))
            .foregroundColor(availableCredit > 0 ? .green : .red)
          }
        }
      }

      // 信用卡账单信息
      if card.isCredit {
        VStack(alignment: .leading, spacing: 4) {
          if let billDay = card.billDay {
            HStack {
              Text("账单日:")
                .font(.system(size: 13))
                .foregroundColor(.secondary)
              Text("\(billDay == 0 ? "月末" : "\(billDay)日")")
                .font(.system(size: 13, weight: .medium))
                .foregroundColor(.primary)
              Spacer()
              if let dueDay = card.dueDay {
                Text("还款日:")
                  .font(.system(size: 13))
                  .foregroundColor(.secondary)
                Text(card.isFixedDueDay ? "\(dueDay)日" : "账单日后\(dueDay)天")
                  .font(.system(size: 13, weight: .medium))
                  .foregroundColor(.orange)
              }
            }
          }
        }
      }

      // 状态信息
      HStack {
        Text("选中: \(card.isSelected ? "是" : "否")")
          .font(.system(size: 12))
          .foregroundColor(card.isSelected ? .green : .secondary)

        Text("统计: \(card.isStatistics ? "是" : "否")")
          .font(.system(size: 12))
          .foregroundColor(card.isStatistics ? .green : .secondary)

        Spacer()

        Text("排序: \(card.order)")
          .font(.system(size: 12))
          .foregroundColor(.secondary)
      }

      // 备注信息
      if !card.remark.isEmpty {
        HStack {
          Text("备注:")
            .font(.system(size: 12))
            .foregroundColor(.secondary)
          Text(card.remark)
            .font(.system(size: 12))
            .foregroundColor(.primary)
          Spacer()
        }
      }

      // 封面和时间信息
      HStack {
        if !card.cover.isEmpty {
          Text("封面: \(card.cover)")
            .font(.system(size: 12))
            .foregroundColor(.secondary)
        }
        Spacer()
        Text(
          "创建: \(DateFormatter.localizedString(from: card.createdAt, dateStyle: .short, timeStyle: .none))"
        )
        .font(.system(size: 12))
        .foregroundColor(.secondary)
      }

      // ID信息
      HStack {
        Text("ID: \(card.id.uuidString.prefix(8))...")
          .font(.system(size: 11))
          .foregroundColor(.secondary)
        Spacer()
        Text(
          "更新: \(DateFormatter.localizedString(from: card.updatedAt, dateStyle: .short, timeStyle: .short))"
        )
        .font(.system(size: 11))
        .foregroundColor(.secondary)
      }
    }
    .padding(16)
    .background(Color(.systemGray6))
    .cornerRadius(12)
  }
}

// MARK: - 交易类别信息组件

struct CategoryInfoSection: View {
  let transactionCategories: [TransactionMainCategoryModel]
  let modelContext: ModelContext
  @Binding var isExpanded: Bool

  var body: some View {
    VStack(alignment: .leading, spacing: 16) {
      Text("交易类别 (\(transactionCategories.count))")
        .font(.headline)
        .foregroundColor(.primary)

      if transactionCategories.isEmpty {
        Text("暂无交易类别数据")
          .foregroundColor(.secondary)
          .padding()
          .background(Color.gray.opacity(0.1))
          .cornerRadius(8)
      } else {
        let sortedCategories = transactionCategories.sorted { $0.order < $1.order }
        let expenseCategories = sortedCategories.filter { $0.type == "expense" }
        let incomeCategories = sortedCategories.filter { $0.type == "income" }

        // 支出类别
        if !expenseCategories.isEmpty {
          VStack(alignment: .leading, spacing: 12) {
            HStack {
              Image(systemName: "minus.circle")
                .foregroundColor(.red)
              Text("支出类别 (\(expenseCategories.count))")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            }

            ForEach(expenseCategories) { category in
              categoryDetailView(category: category)
            }
          }
        }

        // 收入类别
        if !incomeCategories.isEmpty {
          VStack(alignment: .leading, spacing: 12) {
            HStack {
              Image(systemName: "plus.circle")
                .foregroundColor(.green)
              Text("收入类别 (\(incomeCategories.count))")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            }

            ForEach(incomeCategories) { category in
              categoryDetailView(category: category)
            }
          }
        }
      }

      // 清除按钮
      HStack {
        Spacer()
        Button(action: {
          for category in transactionCategories {
            modelContext.delete(category)
          }
          try? modelContext.save()
        }) {
          HStack(spacing: 8) {
            Image(systemName: "trash.fill")
              .font(.system(size: 14, weight: .medium))
            Text("清理交易类别")
              .font(.system(size: 15, weight: .medium))
          }
          .foregroundColor(.white)
          .padding(.horizontal, 20)
          .padding(.vertical, 12)
          .background(Color.red)
          .cornerRadius(10)
        }
        .disabled(transactionCategories.isEmpty)
        Spacer()
      }
    }
  }

  @ViewBuilder
  private func categoryDetailView(category: TransactionMainCategoryModel) -> some View {
    VStack(alignment: .leading, spacing: 12) {
      // 主类别信息
      VStack(alignment: .leading, spacing: 6) {
        HStack {
          IconView(viewModel: IconViewVM(icon: category.icon, size: 20))
          Text(category.name)
            .font(.system(size: 15, weight: .medium))
            .foregroundColor(.primary)
          Spacer()
          Text(category.type == "expense" ? "支出" : "收入")
            .font(.caption)
            .padding(.horizontal, 8)
            .padding(.vertical, 3)
            .background(
              category.type == "expense" ? Color.red.opacity(0.1) : Color.green.opacity(0.1)
            )
            .foregroundColor(category.type == "expense" ? .red : .green)
            .cornerRadius(6)
        }

        HStack {
          Text("ID: \(category.id)")
            .font(.caption)
            .foregroundColor(.secondary)
          Spacer()
          Text("排序: \(category.order)")
            .font(.caption)
            .foregroundColor(.secondary)
        }
      }
      .padding(12)
      .background(Color(.systemGray6))
      .cornerRadius(8)

      // 子类别信息
      if let subCategories = category.subCategories, !subCategories.isEmpty {
        VStack(alignment: .leading, spacing: 8) {
          Text("子类别 (\(subCategories.count)个)")
            .font(.caption)
            .fontWeight(.medium)
            .foregroundColor(.secondary)
            .padding(.leading, 12)

          ForEach(subCategories.sorted { $0.order < $1.order }) { subCategory in
            HStack {
              IconView(viewModel: IconViewVM(icon: subCategory.icon, size: 16))
              Text(subCategory.name)
                .font(.system(size: 13))
                .foregroundColor(.primary)
              Spacer()
              Text("ID: \(subCategory.id)")
                .font(.caption)
                .foregroundColor(.secondary)
              Text("排序: \(subCategory.order)")
                .font(.caption)
                .foregroundColor(.secondary)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color(.systemGray5))
            .cornerRadius(6)
          }
        }
        .padding(.leading, 16)
      } else {
        Text("无子类别")
          .font(.caption)
          .foregroundColor(.secondary)
          .padding(.leading, 12)
      }
    }
  }
}

// MARK: - 货币信息组件

struct CurrencyInfoSection: View {
  let currencies: [CurrencyModel]
  let modelContext: ModelContext
  @Binding var isExpanded: Bool

  var body: some View {
    VStack(alignment: .leading, spacing: 16) {
      Text("货币信息 (\(currencies.count))")
        .font(.headline)
        .foregroundColor(.primary)

      if currencies.isEmpty {
        Text("暂无货币数据")
          .foregroundColor(.secondary)
          .padding()
          .background(Color.gray.opacity(0.1))
          .cornerRadius(8)
      } else {
        ForEach(currencies) { currency in
          currencyDetailView(currency: currency)
        }
      }

      // 清除按钮
      HStack {
        Spacer()
        Button(action: {
          for currency in currencies {
            modelContext.delete(currency)
          }
          try? modelContext.save()
        }) {
          HStack(spacing: 8) {
            Image(systemName: "trash.fill")
              .font(.system(size: 14, weight: .medium))
            Text("清理货币")
              .font(.system(size: 15, weight: .medium))
          }
          .foregroundColor(.white)
          .padding(.horizontal, 20)
          .padding(.vertical, 12)
          .background(Color.red)
          .cornerRadius(10)
        }
        .disabled(currencies.isEmpty)
        Spacer()
      }
    }
  }

  @ViewBuilder
  private func currencyDetailView(currency: CurrencyModel) -> some View {
    VStack(alignment: .leading, spacing: 8) {
      // 第一行：名称和代码
      HStack {
        Text(currency.name)
          .font(.system(size: 15, weight: .medium))
          .foregroundColor(.primary)
        Spacer()
        Text(currency.code)
          .font(.caption)
          .padding(.horizontal, 8)
          .padding(.vertical, 3)
          .background(Color.blue.opacity(0.1))
          .foregroundColor(.blue)
          .cornerRadius(6)
      }

      // 第二行：汇率和符号
      HStack {
        Text("汇率:")
          .font(.caption)
          .foregroundColor(.secondary)
        Text("\(currency.formattedRate())")
          .font(.caption)
          .fontWeight(.medium)
          .foregroundColor(.primary)
        Spacer()
        Text("符号: \(currency.symbol)")
          .font(.caption)
          .foregroundColor(.secondary)
      }

      // 第三行：状态信息
      HStack {
        Text("本位币: \(currency.isBaseCurrency ? "是" : "否")")
          .font(.caption)
          .foregroundColor(currency.isBaseCurrency ? .green : .secondary)
        Spacer()
        Text("选中: \(currency.isSelected ? "是" : "否")")
          .font(.caption)
          .foregroundColor(currency.isSelected ? .green : .secondary)
      }

      // 第四行：其他信息
      HStack {
        Text("自定义: \(currency.isCustom ? "是" : "否")")
          .font(.caption)
          .foregroundColor(.secondary)
        Spacer()
        Text("排序: \(currency.order)")
          .font(.caption)
          .foregroundColor(.secondary)
      }

      // 第五行：默认汇率
      HStack {
        Text("默认汇率: \(String(format: "%.6f", currency.defaultRate))")
          .font(.caption)
          .foregroundColor(.secondary)
        Spacer()
      }
    }
    .padding(12)
    .background(Color(.systemGray6))
    .cornerRadius(8)
  }
}

// MARK: - 交易记录信息组件

struct TransactionInfoSection: View {
  let transactionModels: [TransactionModel]
  let modelContext: ModelContext
  @Binding var isExpanded: Bool

  var body: some View {
    VStack(alignment: .leading, spacing: 16) {
      Text("交易记录 (\(transactionModels.count))")
        .font(.headline)
        .foregroundColor(.primary)

      if transactionModels.isEmpty {
        Text("暂无交易记录")
          .foregroundColor(.secondary)
          .padding()
          .background(Color.gray.opacity(0.1))
          .cornerRadius(8)
      } else {
        let sortedTransactions = transactionModels.sorted {
          $0.transactionDate > $1.transactionDate
        }

        ForEach(sortedTransactions) { transaction in
          transactionDetailView(transaction: transaction)
        }
      }

      // 清除按钮
      HStack {
        Spacer()
        Button(action: {
          for transaction in transactionModels {
            modelContext.delete(transaction)
          }
          try? modelContext.save()
        }) {
          HStack(spacing: 8) {
            Image(systemName: "trash.fill")
              .font(.system(size: 14, weight: .medium))
            Text("清除全部交易")
              .font(.system(size: 15, weight: .medium))
          }
          .foregroundColor(.white)
          .padding(.horizontal, 20)
          .padding(.vertical, 12)
          .background(Color.red)
          .cornerRadius(10)
        }
        .disabled(transactionModels.isEmpty)
        Spacer()
      }
    }
  }

  @ViewBuilder
  private func transactionDetailView(transaction: TransactionModel) -> some View {
    VStack(alignment: .leading, spacing: 10) {
      // 第一行：类型和金额
      HStack {
        Text(transaction.transactionType.rawValue)
          .font(.caption)
          .padding(.horizontal, 8)
          .padding(.vertical, 3)
          .background(typeColor(for: transaction.transactionType).opacity(0.1))
          .foregroundColor(typeColor(for: transaction.transactionType))
          .cornerRadius(6)

        Spacer()

        Text(
          "\(transaction.symbol)\(NumberFormatService.shared.formatAmount(transaction.transactionAmount))"
        )
        .font(.system(size: 15, weight: .medium))
        .foregroundColor(.primary)
      }

      // 第二行：日期和货币
      HStack {
        Text("日期:")
          .font(.caption)
          .foregroundColor(.secondary)
        Text(
          DateFormatter.localizedString(
            from: transaction.transactionDate, dateStyle: .short, timeStyle: .short)
        )
        .font(.caption)
        .foregroundColor(.primary)
        Spacer()
        Text("货币: \(transaction.currency)")
          .font(.caption)
          .foregroundColor(.secondary)
      }

      // 第三行：类别信息
      HStack {
        if let categoryId = transaction.transactionCategoryId {
          Text("类别ID: \(categoryId)")
            .font(.caption)
            .foregroundColor(.secondary)
        } else {
          Text("无类别")
            .font(.caption)
            .foregroundColor(.secondary)
        }
        Spacer()
      }

      // 第四行：卡片信息（根据交易类型显示）
      VStack(alignment: .leading, spacing: 2) {
        if transaction.transactionType == .transfer {
          // 转账：显示转出和转入卡片
          HStack {
            if let fromCardId = transaction.fromCardId {
              Text("转出: \(fromCardId.uuidString.prefix(8))...")
                .font(.caption)
                .foregroundColor(.red)
            } else {
              Text("转出: 无")
                .font(.caption)
                .foregroundColor(.secondary)
            }
            Spacer()
            if let toCardId = transaction.toCardId {
              Text("转入: \(toCardId.uuidString.prefix(8))...")
                .font(.caption)
                .foregroundColor(.green)
            } else {
              Text("转入: 无")
                .font(.caption)
                .foregroundColor(.secondary)
            }
          }
        } else {
          // 其他交易类型：显示相关卡片
          HStack {
            if let fromCardId = transaction.fromCardId {
              Text("卡片ID: \(fromCardId.uuidString.prefix(8))...")
                .font(.caption)
                .foregroundColor(.secondary)
            } else if let toCardId = transaction.toCardId {
              Text("卡片ID: \(toCardId.uuidString.prefix(8))...")
                .font(.caption)
                .foregroundColor(.secondary)
            } else {
              Text("无关联卡片")
                .font(.caption)
                .foregroundColor(.secondary)
            }
            Spacer()
          }
        }
      }

      // 第五行：备注和优惠
      HStack {
        if !transaction.remark.isEmpty {
          Text("备注: \(transaction.remark)")
            .font(.caption)
            .foregroundColor(.secondary)
        } else {
          Text("无备注")
            .font(.caption)
            .foregroundColor(.secondary)
        }
        Spacer()
        if let discountAmount = transaction.discountAmount, discountAmount > 0 {
          Text(
            "优惠: \(transaction.symbol)\(NumberFormatService.shared.formatAmount(discountAmount))"
          )
          .font(.caption)
          .foregroundColor(.orange)
        }
      }

      // 第六行：ID信息
      HStack {
        Text("ID: \(transaction.id.uuidString.prefix(8))...")
          .font(.caption)
          .foregroundColor(.secondary)
        Spacer()
      }
    }
    .padding(12)
    .background(Color(.systemGray6))
    .cornerRadius(8)
  }

  private func typeColor(for type: TransactionType) -> Color {
    switch type {
    case .expense:
      return .red
    case .income:
      return .green
    case .transfer:
      return .blue
    case .refund:
      return .orange
    case .createCard:
      return .purple
    case .adjustCard:
      return .indigo
    }
  }
}

// MARK: - 系统操作组件

struct SystemOperationSection: View {
  let currencies: [CurrencyModel]
  let transactionCategories: [TransactionMainCategoryModel]
  let cards: [CardModel]
  let transactionModels: [TransactionModel]
  let modelContext: ModelContext

  var body: some View {
    VStack(alignment: .leading, spacing: 20) {
      Text("系统操作")
        .font(.headline)
        .foregroundColor(.primary)

      // 初始化操作区域
      VStack(alignment: .leading, spacing: 12) {
        Text("数据初始化")
          .font(.subheadline)
          .fontWeight(.medium)
          .foregroundColor(.primary)

        VStack(spacing: 12) {
          systemButton(
            title: "初始化类别",
            icon: "folder.badge.plus",
            color: .blue,
            action: { initializeTransactionCategories() }
          )

          systemButton(
            title: "初始化货币",
            icon: "plus.circle",
            color: .green,
            action: { initializeCurrencies() }
          )
        }
      }

      // 初始化状态显示
      VStack(alignment: .leading, spacing: 12) {
        Text("初始化状态")
          .font(.subheadline)
          .fontWeight(.medium)
          .foregroundColor(.primary)

        VStack(alignment: .leading, spacing: 8) {
          HStack {
            Image(systemName: "iphone")
              .foregroundColor(.blue)
              .frame(width: 20)
            Text("本地数据:")
              .font(.system(size: 14))
              .foregroundColor(.secondary)
            Text(
              UserDefaults.standard.bool(forKey: "com.cstory.localDataInitialized")
                ? "已初始化" : "未初始化"
            )
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(
              UserDefaults.standard.bool(forKey: "com.cstory.localDataInitialized")
                ? .green : .orange
            )
            Spacer()
          }

          HStack {
            Image(systemName: "icloud")
              .foregroundColor(.blue)
              .frame(width: 20)
            Text("云端数据:")
              .font(.system(size: 14))
              .foregroundColor(.secondary)
            Text(
              NSUbiquitousKeyValueStore.default.bool(forKey: "com.cstory.cloudDataInitialized")
                ? "已初始化" : "未初始化"
            )
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(
              NSUbiquitousKeyValueStore.default.bool(forKey: "com.cstory.cloudDataInitialized")
                ? .green : .orange)
            Spacer()
          }
        }
        .padding(12)
        .background(Color(.systemGray6))
        .cornerRadius(8)
      }

      // 系统管理操作
      VStack(alignment: .leading, spacing: 12) {
        Text("系统管理")
          .font(.subheadline)
          .fontWeight(.medium)
          .foregroundColor(.primary)

        VStack(spacing: 12) {
          systemButton(
            title: "重置初始化标记",
            icon: "arrow.clockwise",
            color: .orange,
            action: {
              UserDefaults.standard.set(false, forKey: "com.cstory.localDataInitialized")
              NSUbiquitousKeyValueStore.default.set(
                false, forKey: "com.cstory.cloudDataInitialized")
              NSUbiquitousKeyValueStore.default.synchronize()
            }
          )

          systemButton(
            title: "清除所有数据",
            icon: "trash.fill",
            color: .red,
            action: { clearAllData() }
          )
        }
      }
    }
  }

  @ViewBuilder
  private func systemButton(title: String, icon: String, color: Color, action: @escaping () -> Void)
    -> some View
  {
    Button(action: action) {
      HStack {
        Image(systemName: icon)
          .font(.system(size: 16, weight: .medium))
        Text(title)
          .font(.system(size: 15, weight: .medium))
        Spacer()
      }
      .foregroundColor(.white)
      .padding(.horizontal, 16)
      .padding(.vertical, 12)
      .background(color)
      .cornerRadius(10)
    }
  }

  // MARK: - 私有方法

  /// 初始化货币数据
  private func initializeCurrencies(onlyPriority: Bool = true) {
    print("🚀 开始初始化货币数据...")

    // 检查是否已有货币数据
    if !currencies.isEmpty {
      print("⚠️ 已存在货币数据，跳过初始化")
      return
    }

    // 从本地JSON文件加载汇率数据
    guard let path = Bundle.main.path(forResource: "exchange_rates", ofType: "json") else {
      print("❌ 找不到exchange_rates.json文件")
      return
    }

    let url = URL(fileURLWithPath: path)
    var exchangeRates: ExchangeRateAPIResponse

    do {
      let data = try Data(contentsOf: url)
      if let decoded = try? JSONDecoder().decode(ExchangeRateAPIResponse.self, from: data) {
        exchangeRates = decoded
      } else {
        print("❌ 无法解析汇率JSON数据")
        return
      }
    } catch {
      print("❌ 无法读取汇率JSON文件: \(error)")
      return
    }

    let (currencyNames, currencySymbols) = CurrencyService.getCurrencyMappings()
    let priorityCurrencies = CurrencyService.getPriorityCurrencies()

    var orderCounter = 0
    var processedCurrencies = Set<String>()

    // 首先添加人民币作为本位币
    let cnyName = currencyNames["CNY"] ?? "人民币"
    let cnySymbol = currencySymbols["CNY"] ?? "¥"
    let cnyCurrency = CurrencyModel(
      name: cnyName,
      code: "CNY",
      symbol: cnySymbol,
      rate: 1.0,
      defaultRate: 1.0,
      isBaseCurrency: true,
      isCustom: false,
      isSelected: true,
      order: orderCounter
    )
    orderCounter += 1
    modelContext.insert(cnyCurrency)
    processedCurrencies.insert("CNY")

    // 然后按优先级添加其他货币
    for code in priorityCurrencies where code != "CNY" {
      if let apiRate = exchangeRates.data.rates[code] {
        let name = currencyNames[code] ?? code
        let symbol = currencySymbols[code] ?? code

        // API返回的汇率是"1CNY=多少外币"的形式
        // 但我们需要"1外币=多少CNY"的形式，所以需要取倒数
        var inverseRate = 1.0
        if apiRate > 0 {
          inverseRate = 1.0 / apiRate
          // 确保保留6位小数精度
          inverseRate = (inverseRate * 1_000_000).rounded() / 1_000_000
        }

        let currency = CurrencyModel(
          name: name,
          code: code,
          symbol: symbol,
          rate: inverseRate,
          defaultRate: inverseRate,
          isBaseCurrency: false,
          isCustom: false,
          isSelected: true,
          order: orderCounter
        )
        orderCounter += 1
        modelContext.insert(currency)
        processedCurrencies.insert(code)
      }
    }

    // 添加其他所有货币（按字母顺序）
    if !onlyPriority {
      let sortedRemainingCurrencies = exchangeRates.data.rates.keys.sorted().filter {
        !processedCurrencies.contains($0)
      }

      for code in sortedRemainingCurrencies {
        let apiRate = exchangeRates.data.rates[code] ?? 1.0
        let name = currencyNames[code] ?? code
        let symbol = currencySymbols[code] ?? code

        // 计算反向汇率
        var inverseRate = 1.0
        if apiRate > 0 {
          inverseRate = 1.0 / apiRate
          // 确保保留6位小数精度
          inverseRate = (inverseRate * 1_000_000).rounded() / 1_000_000
        }

        let currency = CurrencyModel(
          name: name,
          code: code,
          symbol: symbol,
          rate: inverseRate,
          defaultRate: inverseRate,
          isBaseCurrency: false,
          isCustom: false,
          isSelected: false,  // 非优先货币默认不选中
          order: orderCounter
        )
        orderCounter += 1
        modelContext.insert(currency)
        processedCurrencies.insert(code)
      }
    }

    // 保存更改
    do {
      try modelContext.save()
      print("✅ 货币数据初始化完成，共创建 \(processedCurrencies.count) 种货币")
    } catch {
      print("❌ 货币数据保存失败: \(error)")
    }
  }

  /// 初始化交易类别数据
  private func initializeTransactionCategories() {
    print("🚀 开始初始化交易类别数据...")

    // 检查是否已有类别数据
    if !transactionCategories.isEmpty {
      print("⚠️ 已存在交易类别数据，跳过初始化")
      return
    }

    // 从本地JSON加载默认类别数据
    let mainCategories = MainCategoryJSONDecoder.decode(from: "TransactionCategoryDefaults")
    guard !mainCategories.isEmpty else {
      print("❌ 本地JSON文件为空或解析失败，无法创建类别")
      return
    }

    print("📊 从JSON加载了\(mainCategories.count)个主分类，开始创建类别...")

    // 创建主类别和子类别
    var totalSubCategories = 0
    for item in mainCategories {
      // 创建主类别
      let mainCategory = TransactionMainCategoryModel(
        id: item.categoryId,
        name: item.name,
        icon: item.iconType,
        order: item.order,
        type: item.transactionType
      )
      modelContext.insert(mainCategory)

      // 创建子类别
      if let subCategories = item.subCategories {
        print("为主类别 '\(item.name)' 创建\(subCategories.count)个子类别")
        totalSubCategories += subCategories.count

        subCategories.forEach { subItem in
          let subCategory = TransactionSubCategoryModel(
            id: subItem.categoryId,
            name: subItem.name,
            icon: subItem.iconType,
            order: subItem.order,
            mainId: mainCategory.id
          )
          modelContext.insert(subCategory)
          // SwiftData会自动处理@Relationship，只需设置一侧
          subCategory.mainCategory = mainCategory
        }
      }
    }

    // 保存更改
    do {
      try modelContext.save()
      print("✅ 交易类别创建完成:")
      print("   📁 主分类: \(mainCategories.count) 个")
      print("   📄 子分类: \(totalSubCategories) 个")
    } catch {
      print("❌ 交易类别保存失败: \(error)")
    }
  }

  /// 清除所有数据和初始化标记
  private func clearAllData() {
    print("🗑️ 开始清除所有数据...")

    // 清除所有数据
    for transaction in transactionModels {
      modelContext.delete(transaction)
    }

    for card in cards {
      modelContext.delete(card)
    }

    for category in transactionCategories {
      modelContext.delete(category)
    }

    for currency in currencies {
      modelContext.delete(currency)
    }

    // 清除初始化标记
    UserDefaults.standard.set(false, forKey: "com.cstory.localDataInitialized")
    NSUbiquitousKeyValueStore.default.set(false, forKey: "com.cstory.cloudDataInitialized")
    NSUbiquitousKeyValueStore.default.synchronize()

    // 保存更改
    do {
      try modelContext.save()
      print("✅ 所有数据和标记已清除")
    } catch {
      print("❌ 清除数据失败: \(error)")
    }
  }
}

// MARK: - Extensions

#Preview {
  ContentView()
}
