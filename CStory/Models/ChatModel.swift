//
//  ChatModel.swift
//  CStory
//
//  Created by NZUE on 2025/7/11.
//

import Foundation
import SwiftData
import UIKit
import AVFoundation

// MARK: - 聊天消息模型

/// 聊天消息数据模型
@Model
final class ChatMessageModel {
    /// 唯一标识符
    @Attribute(.unique) var id: UUID
    /// 角色（"user" 或 "assistant"）
    var role: String
    /// 文字内容
    var content: String
    /// 时间戳
    var timestamp: Date
    /// 关联的交易ID列表（编码为Data）
    var transactionIdsData: Data?
    /// 图片数据列表（编码为Data）
    var imageDataArray: Data?
    
    init(
        id: UUID = UUID(),
        role: String,
        content: String,
        timestamp: Date = Date(),
        transactionIds: [UUID] = [],
        imageData: [Data] = []
    ) {
        self.id = id
        self.role = role
        self.content = content
        self.timestamp = timestamp
        self.transactionIdsData = Self.encodeUUIDs(transactionIds)
        self.imageDataArray = Self.encodeDataArray(imageData)
    }
    
    // MARK: - 计算属性
    
    /// 交易ID列表
    var transactionIds: [UUID] {
        get {
            guard let data = transactionIdsData else { return [] }
            return Self.decodeUUIDs(data) ?? []
        }
        set {
            transactionIdsData = Self.encodeUUIDs(newValue)
        }
    }
    
    /// 图片数据列表
    var imageData: [Data] {
        get {
            guard let data = imageDataArray else { return [] }
            return Self.decodeDataArray(data) ?? []
        }
        set {
            imageDataArray = Self.encodeDataArray(newValue)
        }
    }
    
    // MARK: - 编码/解码
    
    /// 编码UUID数组
    private static func encodeUUIDs(_ uuids: [UUID]) -> Data? {
        return try? JSONEncoder().encode(uuids)
    }
    
    /// 解码UUID数组
    private static func decodeUUIDs(_ data: Data) -> [UUID]? {
        return try? JSONDecoder().decode([UUID].self, from: data)
    }
    
    /// 编码Data数组
    private static func encodeDataArray(_ dataArray: [Data]) -> Data? {
        return try? JSONEncoder().encode(dataArray)
    }
    
    /// 解码Data数组
    private static func decodeDataArray(_ data: Data) -> [Data]? {
        return try? JSONDecoder().decode([Data].self, from: data)
    }
    
    // MARK: - 辅助属性
    
    /// 是否有图片
    var hasImages: Bool {
        !imageData.isEmpty
    }
    
    /// 是否有关联交易
    var hasTransactions: Bool {
        !transactionIds.isEmpty
    }
    
    /// 转换为UIImage列表
    var images: [UIImage] {
        imageData.compactMap { UIImage(data: $0) }
    }
    
    // MARK: - 方法
    
    /// 添加图片
    /// - Parameter images: 要添加的 UIImage 数组
    func addImages(_ images: [UIImage]) {
        let dataArray = images.compactMap { image -> Data? in
            // 使用HEIF压缩
            if let heifData = convertImageToHEIF(image: image, quality: 0.8) {
                return heifData
            }
            // 备用方案：JPEG压缩
            return image.jpegData(compressionQuality: 0.7)
        }
        var currentData = imageData
        currentData.append(contentsOf: dataArray)
        imageData = currentData
    }
    
    /// HEIF格式转换
    /// - Parameters:
    ///   - image: 要转换的图片
    ///   - quality: 压缩质量（0-1）
    /// - Returns: HEIF 格式的图片数据
    private func convertImageToHEIF(image: UIImage, quality: CGFloat) -> Data? {
        let imageData = NSMutableData()
        
        guard
            let imageDestination = CGImageDestinationCreateWithData(
                imageData,
                AVFileType.heic as CFString,
                1,
                nil
            ), let originalCGImage = image.cgImage
        else {
            return nil
        }
        
        let options: [CFString: Any] = [
            kCGImageDestinationLossyCompressionQuality: quality
        ]
        
        CGImageDestinationAddImage(imageDestination, originalCGImage, options as CFDictionary)
        
        guard CGImageDestinationFinalize(imageDestination) else {
            return nil
        }
        
        return imageData as Data
    }
    
    /// 添加交易ID
    /// - Parameter ids: 要添加的交易ID数组
    func addTransactionIds(_ ids: [UUID]) {
        var currentIds = transactionIds
        currentIds.append(contentsOf: ids)
        transactionIds = currentIds
    }
}