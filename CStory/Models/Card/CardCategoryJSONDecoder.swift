//
//  CardCategoryJSONDecoder.swift
//  MStory
//
//  Created by NZUE on 2024/12/30.
//

import Foundation

// MARK: - 响应模型

/// 卡片类别响应模型
struct CardCategoryResponse: Decodable {
  let id: Int
  let name: String
  let imageUrl: String
  let subCategories: [CardSubCategoryResponse]
}

/// 卡片子类别响应模型
struct CardSubCategoryResponse: Decodable {
  let name: String
  let displayName: String
}

/// 完整响应模型
struct CardCategoriesResponse: Decodable {
  let savingCategories: [CardCategoryResponse]
  let creditCategories: [CardCategoryResponse]
}

/// 银行列表响应模型
struct BankListResponse: Decodable {
  let banks: [CardSubCategoryResponse]
}

/// 热门银行响应模型
struct PopularBanksResponse: Codable {
  let popularBanks: [PopularBank]
  let bankCategories: [BankCategory]
}

/// 热门银行模型
struct PopularBank: Codable {
  let name: String
  let displayName: String
}

/// 银行分类模型
struct BankCategory: Codable {
  let name: String
  let banks: [String]
}

// MARK: - JSON 解码器

/// 卡片类别 JSON 解码器
struct CardCategoryJSONDecoder {

  /// 解码卡片类别数据
  /// - Parameter fileName: JSON 文件名（不含扩展名）
  /// - Returns: 解码后的卡片类别响应对象
  static func decode(from fileName: String) -> CardCategoriesResponse? {
    guard let url = Bundle.main.url(forResource: fileName, withExtension: "json"),
      let data = try? Data(contentsOf: url)
    else {
      return nil
    }

    let decoder = JSONDecoder()

    do {
      let response = try decoder.decode(CardCategoriesResponse.self, from: data)
      return response
    } catch {
      print("解码错误：\(error)")
      return nil
    }
  }

  /// 解码银行列表数据
  /// - Returns: 银行列表数组
  static func decodeBankList() -> [CardSubCategoryResponse] {
    guard let url = Bundle.main.url(forResource: "BankList", withExtension: "json"),
      let data = try? Data(contentsOf: url)
    else {
      return []
    }

    let decoder = JSONDecoder()

    do {
      let response = try decoder.decode(BankListResponse.self, from: data)
      return response.banks
    } catch {
      print("解码银行列表错误：\(error)")
      return []
    }
  }
}
