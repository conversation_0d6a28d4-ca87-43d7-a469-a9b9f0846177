import Foundation
import SwiftData

/// 卡片数据模型
@Model
final class CardModel: Identifiable {
  /// 唯一标识符
  var id: UUID = UUID()
  /// 排序顺序
  var order: Int = 0
  /// 是否为信用卡
  var isCredit: Bool = false
  /// 是否可选择
  var isSelected: Bool = true
  /// 卡片名称
  var name: String = ""
  /// 备注信息
  var remark: String = ""
  /// 货币代码
  var currency: String = ""
  /// 货币符号
  var symbol: String = ""
  /// 余额
  var balance: Double = 0
  /// 信用额度
  var credit: Double = 0
  /// 是否纳入统计
  var isStatistics: Bool = false
  /// 封面图片名称
  var cover: String = ""
  /// 银行Logo数据
  var bankLogo: Data?
  /// 银行名称
  var bankName: String = ""
  /// 卡号（后四位）
  var cardNumber: String = ""
  /// 账单日（1-30，0表示月末，nil表示未设置）
  var billDay: Int?
  /// 还款类型（true: 固定日期，false: 账单日后X天）
  var isFixedDueDay: Bool = true
  /// 还款日
  var dueDay: Int?
  /// 创建时间
  var createdAt: Date = Date()
  /// 更新时间
  var updatedAt: Date = Date()
  /// 初始化卡片模型
  init(
    id: UUID, order: Int, isCredit: Bool, isSelected: Bool, name: String, remark: String,
    currency: String, symbol: String, balance: Double, credit: Double, isStatistics: Bool,
    cover: String, bankLogo: Data? = nil, bankName: String, cardNumber: String, billDay: Int? = nil,
    isFixedDueDay: Bool, dueDay: Int? = nil, createdAt: Date, updatedAt: Date
  ) {
    self.id = id
    self.order = order
    self.isCredit = isCredit
    self.isSelected = isSelected
    self.name = name
    self.remark = remark
    self.currency = currency
    self.symbol = symbol
    self.balance = balance
    self.credit = credit
    self.isStatistics = isStatistics
    self.cover = cover
    self.bankLogo = bankLogo
    self.bankName = bankName
    self.cardNumber = cardNumber
    self.billDay = billDay
    self.isFixedDueDay = isFixedDueDay
    self.dueDay = dueDay

    self.createdAt = createdAt
    self.updatedAt = updatedAt
  }
}

extension CardModel {
  /// 获取下一个账单日期
  /// - Returns: 下一个账单日期，若不是信用卡或未设置账单日则返回nil
  func getNextBillDate() -> Date? {
    guard isCredit, let billDay = billDay else { return nil }

    let isMonthEnd = (billDay == 0)
    let actualBillDay = isMonthEnd ? 31 : billDay

    return calculateNextBillDate(
      billDay: actualBillDay,
      from: Date(),
      isMonthEnd: isMonthEnd
    )
  }

  /// 获取下一个还款日期
  /// - Returns: 下一个还款日期，若不是信用卡或未设置相关参数则返回nil
  func getNextDueDate() -> Date? {
    guard isCredit, let billDay = billDay, let dueDay = dueDay else { return nil }

    let isMonthEnd = (billDay == 0)
    let actualBillDay = isMonthEnd ? 31 : billDay
    let dueMode: CreditCardDueMode = isFixedDueDay ? .fixed : .afterBill

    return calculateNextDueDate(
      billDay: actualBillDay,
      dueMode: dueMode,
      dueDay: dueDay,
      from: Date(),
      isMonthEnd: isMonthEnd
    )
  }

  // MARK: - Private Credit Card Calculations

  /// 计算信用卡下一个账单日
  private func calculateNextBillDate(
    billDay: Int,
    from date: Date = Date(),
    isMonthEnd: Bool = false
  ) -> Date {
    let calendar = Calendar.current
    let today = date

    // 获取当前月份信息
    let currentComponents = calendar.dateComponents([.year, .month, .day], from: today)
    guard let currentYear = currentComponents.year,
      let currentMonth = currentComponents.month,
      currentComponents.day != nil
    else {
      return today
    }

    // 计算本月的账单日
    let thisMonthBillDay: Int
    if isMonthEnd {
      // 月末账单日：取当月最后一天
      let monthRange = calendar.range(of: .day, in: .month, for: today)!
      thisMonthBillDay = monthRange.count
    } else {
      // 固定日期账单日：如果账单日超过当月天数，则取当月最后一天
      let monthRange = calendar.range(of: .day, in: .month, for: today)!
      thisMonthBillDay = min(billDay, monthRange.count)
    }

    // 创建本月账单日期
    var thisMonthBillComponents = DateComponents()
    thisMonthBillComponents.year = currentYear
    thisMonthBillComponents.month = currentMonth
    thisMonthBillComponents.day = thisMonthBillDay
    thisMonthBillComponents.hour = 0
    thisMonthBillComponents.minute = 0
    thisMonthBillComponents.second = 0

    guard let thisMonthBillDate = calendar.date(from: thisMonthBillComponents) else {
      return today
    }

    // 如果今天还没到本月账单日，返回本月账单日
    if today < thisMonthBillDate {
      return thisMonthBillDate
    }

    // 否则计算下月账单日
    guard let nextMonth = calendar.date(byAdding: .month, value: 1, to: thisMonthBillDate) else {
      return thisMonthBillDate
    }

    if isMonthEnd {
      // 月末账单日：取下月最后一天
      let nextMonthRange = calendar.range(of: .day, in: .month, for: nextMonth)!
      var nextMonthBillComponents = calendar.dateComponents([.year, .month], from: nextMonth)
      nextMonthBillComponents.day = nextMonthRange.count
      nextMonthBillComponents.hour = 0
      nextMonthBillComponents.minute = 0
      nextMonthBillComponents.second = 0

      return calendar.date(from: nextMonthBillComponents) ?? nextMonth
    } else {
      // 固定日期账单日：处理月份天数不足的情况
      let nextMonthRange = calendar.range(of: .day, in: .month, for: nextMonth)!
      let nextMonthBillDay = min(billDay, nextMonthRange.count)

      var nextMonthBillComponents = calendar.dateComponents([.year, .month], from: nextMonth)
      nextMonthBillComponents.day = nextMonthBillDay
      nextMonthBillComponents.hour = 0
      nextMonthBillComponents.minute = 0
      nextMonthBillComponents.second = 0

      return calendar.date(from: nextMonthBillComponents) ?? nextMonth
    }
  }

  /// 计算信用卡下一个还款日
  private func calculateNextDueDate(
    billDay: Int,
    dueMode: CreditCardDueMode,
    dueDay: Int,
    from date: Date = Date(),
    isMonthEnd: Bool = false
  ) -> Date {
    let calendar = Calendar.current

    switch dueMode {
    case .fixed:
      // 固定还款日模式：每月固定日期还款
      return calculateNextBillDate(billDay: dueDay, from: date, isMonthEnd: false)

    case .afterBill:
      // 账单日后模式：账单日后N天还款
      let nextBillDate = calculateNextBillDate(billDay: billDay, from: date, isMonthEnd: isMonthEnd)
      return calendar.date(byAdding: .day, value: dueDay, to: nextBillDate) ?? nextBillDate
    }
  }
}

extension CardModel {
  /// 更新时间戳
  func updateTimestamp() {
    updatedAt = Date()
  }
}

// MARK: - Supporting Types

/// 信用卡还款日模式
enum CreditCardDueMode {
  case fixed  // 固定日期模式
  case afterBill  // 账单日后模式
}
