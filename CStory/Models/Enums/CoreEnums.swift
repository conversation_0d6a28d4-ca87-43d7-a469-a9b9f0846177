//
//  CoreEnums.swift
//  CStory
//
//  Created by NZUE on 2025/7/12.
//

import Foundation

// MARK: - 核心枚举定义

/// 此文件包含项目中被多处使用的核心枚举类型。
/// 仅将真正共享的、基础的枚举放在这里。

// MARK: - 交易类型

/// 交易类型
enum TransactionType: String, Codable {
    /// 收入
    case income
    /// 支出
    case expense
    /// 转账
    case transfer
    /// 退款
    case refund
    /// 创建卡片
    case createCard
    /// 调整卡片余额
    case adjustCard
}

// MARK: - 图标类型

/// 图标类型
/// 
/// 用于交易分类的图标显示。
enum IconType: Codable, Equatable {
    /// 表情符号
    case emoji(String)
    /// 图片数据
    case image(Data)
    
    /// 实现Equatable协议
    static func == (lhs: IconType, rhs: IconType) -> Bool {
        switch (lhs, rhs) {
        case (.emoji(let lhsEmoji), .emoji(let rhsEmoji)):
            return lhsEmoji == rhsEmoji
        case (.image(let lhsData), .image(let rhsData)):
            return lhsData == rhsData
        default:
            return false
        }
    }
}

// MARK: - 时间周期

/// 交易时间周期
/// 
/// 用于交易统计和筛选。
enum TransactionTimePeriod: String, CaseIterable {
    case week = "周"
    case month = "月"
    case year = "年"
}

// MARK: - UI 状态

/// 控制栏状态
/// 
/// 管理创建交易界面底部控制栏的不同状态。
enum ControlBarState: CaseIterable {
    /// 数字键盘状态 - 显示数字输入键盘
    case numericKeypad
    /// 选择卡片状态 - 显示卡片选择界面
    case selectCard
    /// 选择时间状态 - 显示时间选择界面
    case selectTime
}

// MARK: - 卡片相关

/// 卡片背景类型
enum CardCoverType: String, Codable {
    case card1 = "Card_CS_1"
    case card2 = "Card_CS_2"
    case card3 = "Card_CS_3"
    case card4 = "Card_CS_4"
    case card5 = "Card_CS_5"
    case card6 = "Card_CS_6"
    case card7 = "Card_CS_7"
    case card8 = "Card_CS_8"
    case card9 = "Card_CS_9"
    case card10 = "Card_CS_10"
    case card11 = "Card_CS_11"
}