import Alamofire
import Foundation
import SwiftData
import UIKit

/// AI 服务
///
/// 与豆包AI API通信，支持文本和图像的多模态输入，使用结构化输出格式。
final class AIService: Sendable {
  /// 共享实例
  static let shared = AIService()

  /// 私有构造方法，确保单例模式
  private init() {}

  /// 发送消息到AI并获取回复
  /// - Parameters:
  ///   - text: 用户输入的文本消息
  ///   - images: 可选的图片数组
  ///   - systemPrompt: 系统提示词
  /// - Returns: AI返回的结构化JSON响应字符串
  /// - Throws: 网络错误或解析错误
  func sendMessage(text: String, images: [UIImage] = [], systemPrompt: String) async throws
    -> String
  {
    let url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"

    // 使用传入的系统提示词
    var messages: [[String: Any]] = [
      ["role": "system", "content": systemPrompt]
    ]

    // 用户消息 - 纯文本或包含图片
    if images.isEmpty {
      // 只有文本
      messages.append(["role": "user", "content": text])
    } else {
      // 文本 + 图片
      var userContent: [[String: Any]] = []

      // 添加文本部分
      userContent.append(["type": "text", "text": text])

      // 添加每张图片
      for image in images {
        if let imageData = image.jpegData(compressionQuality: 0.8) {
          let base64String = imageData.base64EncodedString()
          userContent.append([
            "type": "image_url",
            "image_url": ["url": "data:image/jpeg;base64,\(base64String)"],
          ])
        }
      }

      messages.append(["role": "user", "content": userContent])
    }

    // 请求参数
    let parameters: [String: Any] = [
      "model": "doubao-seed-1-6-250615",
      "messages": messages,
      "temperature": 0.7,
      "max_tokens": 1500,
      "thinking": ["type": "disabled"],
      // 要求AI返回结构化的JSON格式
      "response_format": buildJsonSchema(),
    ]

    // 发送请求
    return try await withCheckedThrowingContinuation { continuation in
      AF.request(
        url,
        method: .post,
        parameters: parameters,
        encoding: JSONEncoding.default,
        headers: [
          "Authorization": "Bearer 0e960689-bd97-4869-8884-75328be861b6",
          "Content-Type": "application/json",
        ]
      )
      .validate()
      .responseData { response in
        switch response.result {
        case .success(let data):
          do {
            let json = try JSONSerialization.jsonObject(with: data, options: [])
            guard let responseJson = json as? [String: Any],
              let choices = responseJson["choices"] as? [[String: Any]],
              let firstChoice = choices.first,
              let message = firstChoice["message"] as? [String: Any],
              let content = message["content"] as? String
            else {
              throw NSError(
                domain: "AIService", code: -1,
                userInfo: [NSLocalizedDescriptionKey: "Invalid response"])
            }
            continuation.resume(returning: content)
          } catch {
            continuation.resume(throwing: error)
          }
        case .failure(let error):
          continuation.resume(throwing: error)
        }
      }
    }
  }

  // MARK: - 辅助方法

  /// 构建 JSON Schema
  ///
  /// 定义 AI 返回的数据格式，确保返回结构化的交易数据。
  ///
  /// - Returns: JSON Schema 字典
  /// - Note: 参考 Volcengine 结构化输出文档: https://www.volcengine.com/docs/82379/1568221#07ec5656
  private func buildJsonSchema() -> [String: Any] {
    return [
      "type": "json_schema",
      "json_schema": [
        "name": "transaction_records",
        "schema": [
          "type": "object",
          "properties": [
            "transactions": [
              "type": "array",
              "items": [
                "type": "object",
                "properties": [
                  "transactionType": [
                    "type": "string",
                    "enum": ["expense", "income", "transfer", "refund", "installment"],
                  ],
                  "timestamp": ["type": "integer"],
                  "categoryId": ["type": "string"],
                  "cardId": ["type": "string"],
                  "amount": ["type": "number"],
                  "currency": ["type": "string"],
                  "note": ["type": "string"],
                  "discount": ["type": "number"],
                ],
                "required": ["transactionType", "timestamp", "categoryId", "amount", "currency"],
                "additionalProperties": false,
              ],
            ]
          ],
          "required": ["transactions"],
          "additionalProperties": false,
        ],
        "strict": true,
      ],
    ]
  }
}
