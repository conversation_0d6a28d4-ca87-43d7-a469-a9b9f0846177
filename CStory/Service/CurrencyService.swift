import Foundation
import SwiftUI

/// 货币服务
///
/// 专注于核心货币功能的服务类，提供汇率计算、货币格式化和静态配置数据加载。
/// 使用单例模式确保全局唯一实例。
///
/// ## 核心功能
/// - **静态数据加载**: 从JSON配置文件加载货币名称、符号和常用货币列表
/// - **汇率计算**: 基于汇率数据进行多货币间的转换计算
/// - **货币格式化**: 将数值格式化为带货币符号的显示字符串
/// - **历史汇率转换**: 使用交易记录中的历史汇率进行精确转换
///
/// - Author: CStory Team
/// - Since: 2025.1.1
final class CurrencyService {
  /// 共享实例
  static let shared = CurrencyService()

  /// 私有构造方法，确保单例模式
  private init() {
  }

  // MARK: - Static Data Loading

  /// 加载货币映射配置数据
  /// - Returns: 货币映射配置对象，加载失败返回nil
  private static func loadCurrencyMappings() -> CurrencyMappingsResponse? {
    guard let url = Bundle.main.url(forResource: "CurrencyMappings", withExtension: "json"),
      let data = try? Data(contentsOf: url),
      let mappings = try? JSONDecoder().decode(CurrencyMappingsResponse.self, from: data)
    else {
      print("❌ 无法加载货币映射配置文件")
      return nil
    }

    return mappings
  }

  /// 获取货币名称和符号映射
  /// - Returns: 包含货币名称映射和符号映射的元组
  static func getCurrencyMappings() -> ([String: String], [String: String]) {
    guard let mappings = loadCurrencyMappings() else {
      print("❌ 使用默认货币映射数据")
      return (["CNY": "人民币"], ["CNY": "¥"])
    }

    return (mappings.currencyNames, mappings.currencySymbols)
  }

  /// 获取常用货币代码列表
  /// - Returns: 常用货币代码数组
  static func getPriorityCurrencies() -> [String] {
    guard let mappings = loadCurrencyMappings() else {
      print("❌ 使用默认常用货币列表")
      return ["CNY", "USD", "EUR", "GBP", "JPY"]
    }

    return mappings.priorityCurrencies
  }

  // MARK: - Currency Rate Calculation

  /// 获取货币汇率
  /// - Parameters:
  ///   - sourceCurrency: 源货币代码
  ///   - targetCurrency: 目标货币代码
  ///   - currencies: 可用货币列表
  /// - Returns: 转换汇率值
  func getCurrencyRate(
    from sourceCurrency: String,
    to targetCurrency: String,
    currencies: [CurrencyModel]
  ) -> Double {
    if sourceCurrency == targetCurrency {
      return 1.0
    }

    // 从源货币转换到CNY
    let sourceRate = currencies.first { $0.code == sourceCurrency }?.rate ?? 1.0

    // 从CNY转换到目标货币
    let targetRate = currencies.first { $0.code == targetCurrency }?.rate ?? 1.0

    // 计算最终汇率
    return sourceRate / targetRate
  }

  // MARK: - Currency Formatting

  /// 格式化货币金额（带货币符号）
  /// - Parameters:
  ///   - amount: 金额数值
  ///   - symbol: 货币符号
  ///   - showDecimals: 是否显示小数部分，默认为true
  ///   - showPlusSign: 是否为正数显示加号，默认为false
  /// - Returns: 格式化后的金额字符串
  func formatCurrency(
    _ amount: Double,
    symbol: String,
    showDecimals: Bool = true,
    showPlusSign: Bool = false
  ) -> String {
    let maxDecimals = showDecimals ? 2 : 0
    let formattedNumber = NumberFormatService.shared.formatAmount(
      abs(amount), maxDecimals: maxDecimals)

    let sign: String
    if amount < 0 {
      sign = "-"
    } else if showPlusSign && amount > 0 {
      sign = "+"
    } else {
      sign = ""
    }

    return "\(sign)\(symbol)\(formattedNumber)"
  }

  // MARK: - Transaction Rate Conversion

  /// 使用交易记录的历史汇率进行金额转换
  /// - Parameters:
  ///   - amount: 原始金额
  ///   - transaction: 交易记录
  ///   - targetCurrency: 目标货币代码
  ///   - forExpense: 是否为支出类型转换
  /// - Returns: 转换后的金额
  func convertAmountUsingTransactionRate(
    amount: Double,
    transaction: TransactionModel,
    targetCurrency: String,
    forExpense: Bool
  ) -> Double {
    // 如果交易货币与目标货币相同，无需转换
    if transaction.currency == targetCurrency {
      return amount
    }

    // 获取本位币代码
    let baseCurrencyCode = UserDefaults.standard.string(forKey: "baseCurrencyCode") ?? "CNY"

    // 根据目标货币和交易类型选择合适的汇率
    let rate: Double
    if targetCurrency == baseCurrencyCode {
      // 转换到本位币
      if forExpense {
        rate = transaction.expenseToBaseRate
      } else {
        rate = transaction.incomeToBaseRate
      }
    } else {
      // 转换到卡片货币
      if forExpense {
        rate = transaction.expenseToCardRate
      } else {
        rate = transaction.incomeToCardRate
      }
    }

    return amount * rate
  }

}

// MARK: - Supporting Types

/// 货币映射数据结构
///
/// 从 JSON 文件加载的货币配置信息。
struct CurrencyMappingsResponse: Codable {
  /// 货币代码到名称的映射
  let currencyNames: [String: String]
  /// 货币代码到符号的映射
  let currencySymbols: [String: String]
  /// 常用货币代码列表
  let priorityCurrencies: [String]
}
