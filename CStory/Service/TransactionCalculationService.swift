import Foundation

/// 交易计算服务
///
/// 专门处理交易相关的计算逻辑，包括金额转换、收支统计等。
final class TransactionCalculationService {

  /// 单例实例
  static let shared = TransactionCalculationService()

  /// 私有初始化器，确保单例模式
  private init() {}

  // MARK: - 交易金额计算

  /// 获取交易的收入和支出金额（转换为目标货币）
  ///
  /// 专门用于统计分析，返回交易在目标货币下的收入和支出金额。
  /// 对于转账交易，会同时返回收入和支出金额。
  ///
  /// - Parameters:
  ///   - transaction: 交易记录
  ///   - targetCurrency: 目标货币代码
  /// - Returns: 元组 (收入金额, 支出金额)
  func getTransactionIncomeExpenseAmounts(
    transaction: TransactionModel,
    targetCurrency: String
  ) -> (income: Double, expense: Double) {
    let actualAmount = transaction.transactionAmount - (transaction.discountAmount ?? 0)
    let absAmount = abs(actualAmount)

    switch transaction.transactionType {
    case .income:
      let convertedAmount = CurrencyService.shared.convertAmountUsingTransactionRate(
        amount: absAmount,
        transaction: transaction,
        targetCurrency: targetCurrency,
        forExpense: false
      )
      return (income: convertedAmount, expense: 0)

    case .expense:
      let convertedAmount = CurrencyService.shared.convertAmountUsingTransactionRate(
        amount: absAmount,
        transaction: transaction,
        targetCurrency: targetCurrency,
        forExpense: true
      )
      return (income: 0, expense: convertedAmount)

    case .transfer:
      // 转账同时计入收入和支出，分别使用相应汇率
      let expenseConverted = CurrencyService.shared.convertAmountUsingTransactionRate(
        amount: absAmount,
        transaction: transaction,
        targetCurrency: targetCurrency,
        forExpense: true
      )
      let incomeConverted = CurrencyService.shared.convertAmountUsingTransactionRate(
        amount: absAmount,
        transaction: transaction,
        targetCurrency: targetCurrency,
        forExpense: false
      )
      return (income: incomeConverted, expense: expenseConverted)

    case .refund:
      let convertedAmount = CurrencyService.shared.convertAmountUsingTransactionRate(
        amount: transaction.refundAmount ?? absAmount,
        transaction: transaction,
        targetCurrency: targetCurrency,
        forExpense: false
      )
      return (income: convertedAmount, expense: 0)

    case .createCard, .adjustCard:
      // 系统交易不计入收支统计
      return (income: 0, expense: 0)
    }
  }
}
