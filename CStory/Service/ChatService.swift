//
//  ChatService.swift
//  CStory
//
//  Created by NZUE on 2025/7/12.
//

import Foundation
import SwiftData
import UIKit

// MARK: - 聊天服务

/// 聊天服务管理器
///
/// 负责管理聊天消息的保存、查询和删除操作。
/// 提供用户消息和AI消息的持久化存储功能。
class ChatService {
    /// SwiftData 模型上下文
    private let modelContext: ModelContext

    /// 初始化聊天服务
    ///
    /// - Parameter modelContext: SwiftData 模型上下文
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
    }

    // MARK: - 方法

    /// 保存用户消息
    ///
    /// 创建并保存用户发送的消息，支持文本和图片。
    ///
    /// - Parameters:
    ///   - content: 消息内容
    ///   - images: 图片数组（可选）
    /// - Returns: 创建的消息模型
    /// - Note: 自动保存到数据库
    func saveUserMessage(content: String, images: [UIImage] = []) -> ChatMessageModel {
        let message = ChatMessageModel(
            role: "user",
            content: content
        )

        if !images.isEmpty {
            message.addImages(images)
        }

        modelContext.insert(message)

        do {
            try modelContext.save()
            print("✅ 用户消息保存成功")
        } catch {
            print("❌ 保存用户消息失败: \(error)")
        }

        return message
    }

    /// 保存AI消息
    ///
    /// 创建并保存AI返回的消息，可关联交易记录。
    ///
    /// - Parameters:
    ///   - content: 消息内容
    ///   - transactionIds: 关联的交易ID数组（可选）
    /// - Returns: 创建的消息模型
    /// - Note: 自动保存到数据库
    func saveAIMessage(content: String, transactionIds: [UUID] = []) -> ChatMessageModel {
        let message = ChatMessageModel(
            role: "assistant",
            content: content,
            transactionIds: transactionIds
        )

        modelContext.insert(message)

        do {
            try modelContext.save()
            print("✅ AI消息保存成功")
        } catch {
            print("❌ 保存AI消息失败: \(error)")
        }

        return message
    }

    /// 获取最近的消息
    ///
    /// 查询最近的聊天消息记录。
    ///
    /// - Parameter limit: 消息数量限制（默认50条）
    /// - Returns: 消息数组（按时间正序）
    func getRecentMessages(limit: Int = 50) -> [ChatMessageModel] {
        let descriptor = FetchDescriptor<ChatMessageModel>(
            sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
        )

        do {
            let allMessages = try modelContext.fetch(descriptor)
            let recentMessages = Array(allMessages.prefix(limit))
            return recentMessages.reversed() // 返回正序
        } catch {
            print("❌ 获取消息失败: \(error)")
            return []
        }
    }

    /// 根据交易ID获取关联的交易
    ///
    /// 从所有交易中筛选出与消息关联的交易记录。
    ///
    /// - Parameters:
    ///   - message: 聊天消息
    ///   - allTransactions: 所有交易列表
    /// - Returns: 关联的交易数组
    func getTransactions(for message: ChatMessageModel, allTransactions: [TransactionModel]) -> [TransactionModel] {
        return allTransactions.filter { transaction in
            message.transactionIds.contains(transaction.id)
        }
    }

    /// 删除消息
    ///
    /// 从数据库中删除指定的消息。
    ///
    /// - Parameter message: 要删除的消息
    func deleteMessage(_ message: ChatMessageModel) {
        do {
            modelContext.delete(message)
            try modelContext.save()
            print("✅ 消息删除成功")
        } catch {
            print("❌ 删除消息失败: \(error)")
        }
    }

    /// 清空所有消息
    ///
    /// 删除数据库中的所有聊天消息记录。
    ///
    /// - Important: 此操作不可撤销
    func clearAllMessages() {
        let messages = getRecentMessages(limit: 1000) // 获取所有消息
        for message in messages {
            modelContext.delete(message)
        }

        do {
            try modelContext.save()
            print("✅ 所有消息已清空")
        } catch {
            print("❌ 清空消息失败: \(error)")
        }
    }
}
