import Foundation

/// 资产计算服务
/// 负责计算净资产、总资产、总负债等基础财务数据
class AssetCalculationService {

  // MARK: - Singleton

  static let shared = AssetCalculationService()

  private init() {}

  // MARK: - Data Structures

  /// 资产计算结果
  struct AssetCalculationResult {
    /// 净资产
    let netAsset: Double
    /// 总资产
    let totalAsset: Double
    /// 总负债
    let totalLiability: Double
    /// 参与统计的卡片数量
    let activeCardCount: Int
    /// 货币符号
    let currencySymbol: String
  }

  // MARK: - Public Methods

  /// 计算资产数据
  /// - Parameters:
  ///   - cards: 卡片列表
  ///   - currencies: 货币列表
  ///   - baseCurrencyCode: 基准货币代码，如果为nil则使用系统默认
  /// - Returns: 资产计算结果
  /// - Note: 只统计 isSelected 和 isStatistics 都为 true 的卡片
  func calculateAssets(
    cards: [CardModel],
    currencies: [CurrencyModel],
    baseCurrencyCode: String? = nil
  ) -> AssetCalculationResult {

    let targetCurrencyCode =
      baseCurrencyCode ?? (UserDefaults.standard.string(forKey: "baseCurrencyCode") ?? "CNY")
    let currencySymbol = currencies.first { $0.code == targetCurrencyCode }?.symbol ?? "¥"

    var totalAsset: Double = 0
    var totalLiability: Double = 0
    var activeCardCount: Int = 0

    // 过滤有效卡片
    let validCards = cards.filter { $0.isSelected && $0.isStatistics }

    for card in validCards {
      activeCardCount += 1

      // 获取汇率
      let currencyRate = CurrencyService.shared.getCurrencyRate(
        from: card.currency,
        to: targetCurrencyCode,
        currencies: currencies
      )

      if card.isCredit {
        // 信用卡逻辑
        if card.balance < 0 {
          // 负值表示有欠款，计入负债
          let debtValue = abs(card.balance) * currencyRate
          totalLiability += debtValue
        } else {
          // 正值表示有溢缴，计入资产
          let overpayValue = card.balance * currencyRate
          totalAsset += overpayValue
        }
      } else {
        // 储蓄卡逻辑
        if card.balance >= 0 {
          // 正余额计入资产
          let assetValue = card.balance * currencyRate
          totalAsset += assetValue
        } else {
          // 负余额(透支)计入负债
          let debtValue = abs(card.balance) * currencyRate
          totalLiability += debtValue
        }
      }
    }

    let netAsset = totalAsset - totalLiability

    return AssetCalculationResult(
      netAsset: netAsset,
      totalAsset: totalAsset,
      totalLiability: totalLiability,
      activeCardCount: activeCardCount,
      currencySymbol: currencySymbol
    )
  }

  /// 只计算净资产（为 CardBag 等只需要净资产的场景优化）
  /// - Parameters:
  ///   - cards: 卡片列表
  ///   - currencies: 货币列表
  ///   - baseCurrencyCode: 基准货币代码
  /// - Returns: 净资产金额和货币符号
  func calculateNetAsset(
    cards: [CardModel],
    currencies: [CurrencyModel],
    baseCurrencyCode: String? = nil
  ) -> (amount: Double, symbol: String) {

    let result = calculateAssets(
      cards: cards,
      currencies: currencies,
      baseCurrencyCode: baseCurrencyCode
    )

    return (amount: result.netAsset, symbol: result.currencySymbol)
  }

}

// MARK: - Debug Extension

extension AssetCalculationService {

  /// 计算资产数据（带调试信息）
  /// - Parameters:
  ///   - cards: 卡片列表
  ///   - currencies: 货币列表
  ///   - baseCurrencyCode: 基准货币代码
  ///   - debug: 是否打印调试信息
  /// - Returns: 资产计算结果
  func calculateAssetsWithDebug(
    cards: [CardModel],
    currencies: [CurrencyModel],
    baseCurrencyCode: String? = nil,
    debug: Bool = false
  ) -> AssetCalculationResult {

    if debug {
      print("===== AssetCalculationService 计算开始 =====")
    }

    let result = calculateAssets(
      cards: cards,
      currencies: currencies,
      baseCurrencyCode: baseCurrencyCode
    )

    if debug {
      print("计算结果:")
      print("  净资产: \(result.netAsset) \(result.currencySymbol)")
      print("  总资产: \(result.totalAsset) \(result.currencySymbol)")
      print("  总负债: \(result.totalLiability) \(result.currencySymbol)")
      print("  活跃卡片数: \(result.activeCardCount)")
      print("===== AssetCalculationService 计算结束 =====\n")
    }

    return result
  }
}
