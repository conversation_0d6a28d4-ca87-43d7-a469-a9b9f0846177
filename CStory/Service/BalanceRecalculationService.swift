//
//  BalanceRecalculationService.swift
//  CStory
//
//  Created by NZUE on 2025/7/12.
//

import Foundation
import SwiftData

/// 余额重新计算服务
///
/// 负责处理交易的创建、更新、删除时的卡片余额重新计算。
/// 基于卡片ID的简洁版本，支持历史余额查询和全局资产计算。
class BalanceRecalculationService {

  /// 共享实例
  static let shared = BalanceRecalculationService()

  /// 私有构造方法，确保单例模式
  private init() {}

  // MARK: - 公共方法

  /// 统一的余额重新计算方法
  ///
  /// 重新计算指定卡片的余额，基于所有相关交易记录。
  ///
  /// - Parameters:
  ///   - cardIds: 需要重新计算的卡片ID列表（可包含nil值，会自动过滤）
  ///   - modelContext: SwiftData模型上下文
  ///   - currencies: 货币汇率数据
  ///   - operation: 操作类型描述（用于日志）
  /// - Important: 此方法会自动保存更改到数据库
  func recalculateBalances(
    for cardIds: [UUID?],
    modelContext: ModelContext,
    currencies: [CurrencyModel],
    operation: String = "操作"
  ) {
    // 过滤nil值并去重
    let validCardIds = Set(cardIds.compactMap { $0 })

    guard !validCardIds.isEmpty else {
      print("⚠️ 没有需要重新计算的卡片")
      return
    }

    var successCount = 0

    // 重新计算所有受影响卡片的余额
    for cardId in validCardIds {
      do {
        // 查找卡片
        let cardDescriptor = FetchDescriptor<CardModel>(
          predicate: #Predicate<CardModel> { $0.id == cardId }
        )
        guard let card = try modelContext.fetch(cardDescriptor).first else {
          print("❌ 未找到卡片: \(cardId)")
          continue
        }

        // 查找所有相关交易
        let transactionDescriptor = FetchDescriptor<TransactionModel>(
          predicate: #Predicate<TransactionModel> {
            $0.fromCardId == cardId || $0.toCardId == cardId
          },
          sortBy: [SortDescriptor(\.transactionDate, order: .forward)]
        )
        let allTransactions = try modelContext.fetch(transactionDescriptor)

        // 计算新余额
        let newBalance = calculateCardBalance(
          card: card,
          transactions: allTransactions,
          currencies: currencies
        )

        // 更新卡片余额
        card.balance = newBalance
        card.updatedAt = Date()

        try modelContext.save()

        print("✅ 卡片余额重新计算完成: \(card.name), 新余额: \(newBalance)")
        successCount += 1

      } catch {
        print("❌ 卡片余额重新计算失败: \(error.localizedDescription)")
      }
    }

    print("📊 \(operation)余额重新计算完成，成功: \(successCount)/\(validCardIds.count) 张卡片")
  }

  /// 计算指定时间点的卡片余额
  ///
  /// 根据目标时间点前的所有交易记录计算卡片余额。
  ///
  /// - Parameters:
  ///   - card: 卡片对象
  ///   - targetDate: 目标时间点
  ///   - transactions: 所有相关交易记录
  ///   - currencies: 货币汇率数据
  /// - Returns: 计算后的余额
  func calculateBalanceAtTimePoint(
    card: CardModel,
    targetDate: Date,
    transactions: [TransactionModel],
    currencies: [CurrencyModel]
  ) -> Double {
    // 筛选到目标时间点的所有相关交易
    let transactionsUpToTarget = transactions.filter { transaction in
      transaction.transactionDate <= targetDate
        && (transaction.fromCardId == card.id || transaction.toCardId == card.id)
    }.sorted { $0.transactionDate < $1.transactionDate }

    return calculateCardBalance(
      card: card, transactions: transactionsUpToTarget, currencies: currencies)
  }

  /// 计算指定时间点的全局资产状况
  ///
  /// 根据目标时间点前的所有交易计算总资产、总负债和净资产。
  ///
  /// - Parameters:
  ///   - cards: 所有卡片
  ///   - targetDate: 目标时间点
  ///   - transactions: 所有交易记录
  ///   - currencies: 货币汇率数据
  /// - Returns: 元组包含 (总资产, 总负债, 净资产)
  /// - Note: 只统计 isStatistics 为 true 的卡片
  func calculateGlobalAssetsAtTimePoint(
    cards: [CardModel],
    targetDate: Date,
    transactions: [TransactionModel],
    currencies: [CurrencyModel]
  ) -> (totalAssets: Double, totalDebts: Double, netAssets: Double) {
    var totalAssets: Double = 0
    var totalDebts: Double = 0

    let baseCurrencyCode = UserDefaults.standard.string(forKey: "baseCurrencyCode") ?? "CNY"

    // 对每张统计卡片计算历史余额
    for card in cards.filter({ $0.isStatistics }) {
      let historicalBalance = calculateBalanceAtTimePoint(
        card: card,
        targetDate: targetDate,
        transactions: transactions,
        currencies: currencies
      )

      // 转换为本位币
      let rate = CurrencyService.shared.getCurrencyRate(
        from: card.currency,
        to: baseCurrencyCode,
        currencies: currencies
      )
      let balanceInBaseCurrency = historicalBalance * rate

      // 根据卡片类型和余额正负性分类
      if card.isCredit {
        // 信用卡：负余额是负债，正余额是资产
        if balanceInBaseCurrency >= 0 {
          totalAssets += balanceInBaseCurrency
        } else {
          totalDebts += abs(balanceInBaseCurrency)
        }
      } else {
        // 普通卡：正余额是资产，负余额是负债
        if balanceInBaseCurrency >= 0 {
          totalAssets += balanceInBaseCurrency
        } else {
          totalDebts += abs(balanceInBaseCurrency)
        }
      }
    }

    let netAssets = totalAssets - totalDebts
    return (totalAssets, totalDebts, netAssets)
  }

  // MARK: - 余额计算核心方法

  /// 计算卡片的当前余额
  ///
  /// 基于所有相关交易记录计算卡片的最终余额。
  /// 从最近的基准点（createCard 或 adjustCard）开始计算。
  ///
  /// - Parameters:
  ///   - card: 卡片对象
  ///   - transactions: 所有相关交易记录
  ///   - currencies: 货币汇率数据
  /// - Returns: 计算后的余额
  func calculateCardBalance(
    card: CardModel,
    transactions: [TransactionModel],
    currencies: [CurrencyModel]
  ) -> Double {
    // 1. 查找最近的基准点交易
    let basePointTransactions = transactions.filter { transaction in
      (transaction.transactionType == .createCard || transaction.transactionType == .adjustCard)
        && transaction.toCardId == card.id
    }

    var startingBalance: Double = 0
    var startDate: Date = Date.distantPast

    // 2. 确定起始余额和起始时间
    if let basePoint = basePointTransactions.max(by: { $0.transactionDate < $1.transactionDate }) {
      startingBalance = getBasePointBalance(
        basePoint: basePoint, card: card, currencies: currencies)
      startDate = basePoint.transactionDate

      print(
        "📍 找到基准点: \(basePoint.transactionType.rawValue), 日期: \(basePoint.transactionDate), 金额: \(basePoint.transactionAmount)"
      )
      print("📊 基准点余额: \(startingBalance)")
    } else {
      print("📍 未找到基准点，从0开始计算卡片: \(card.name)")
    }

    // 3. 查找起始点之后的所有相关交易
    let subsequentTransactions = transactions.filter { transaction in
      transaction.transactionDate > startDate
        && (transaction.fromCardId == card.id || transaction.toCardId == card.id)
    }.sorted { $0.transactionDate < $1.transactionDate }

    // 4. 累积计算交易影响
    let finalBalance = calculateTransactionsImpact(
      startingBalance: startingBalance,
      transactions: subsequentTransactions,
      card: card,
      currencies: currencies
    )

    return finalBalance
  }

  /// 累积计算多个交易对卡片余额的影响
  ///
  /// 可复用的核心计算逻辑，从起始余额开始累加每笔交易的影响。
  ///
  /// - Parameters:
  ///   - startingBalance: 起始余额
  ///   - transactions: 需要处理的交易列表（已排序）
  ///   - card: 卡片对象
  ///   - currencies: 货币汇率数据
  /// - Returns: 最终余额
  private func calculateTransactionsImpact(
    startingBalance: Double,
    transactions: [TransactionModel],
    card: CardModel,
    currencies: [CurrencyModel]
  ) -> Double {
    var runningBalance = startingBalance

    print("📊 起始余额: \(startingBalance), 需要处理的交易数量: \(transactions.count)")

    for transaction in transactions {
      let impact = calculateTransactionImpact(
        transaction: transaction, card: card, currencies: currencies)
      runningBalance += impact

      print("💰 交易影响: \(transaction.transactionType.rawValue) \(impact), 累积余额: \(runningBalance)")
    }

    return runningBalance
  }

  /// 获取基准点的余额
  ///
  /// 根据基准点交易类型返回对应的余额值。
  ///
  /// - Parameters:
  ///   - basePoint: 基准点交易
  ///   - card: 卡片对象
  ///   - currencies: 货币汇率数据
  /// - Returns: 基准点的余额
  /// - Note: 使用交易记录中的历史汇率进行转换
  private func getBasePointBalance(
    basePoint: TransactionModel,
    card: CardModel,
    currencies: [CurrencyModel]
  ) -> Double {
    switch basePoint.transactionType {
    case .createCard:
      // 卡片创建交易：余额就是交易金额（使用历史汇率转换为卡片货币）
      return CurrencyService.shared.convertAmountUsingTransactionRate(
        amount: basePoint.transactionAmount,
        transaction: basePoint,
        targetCurrency: card.currency,
        forExpense: false
      )

    case .adjustCard:
      // 卡片调整交易：余额就是调整后的金额（使用历史汇率转换为卡片货币）
      return CurrencyService.shared.convertAmountUsingTransactionRate(
        amount: basePoint.transactionAmount,
        transaction: basePoint,
        targetCurrency: card.currency,
        forExpense: false
      )

    default:
      return 0
    }
  }

  /// 计算单笔交易对卡片余额的影响
  ///
  /// 根据交易类型和卡片关系计算交易对余额的影响。
  ///
  /// - Parameters:
  ///   - transaction: 交易对象
  ///   - card: 卡片对象
  ///   - currencies: 货币汇率数据
  /// - Returns: 交易对余额的影响（正数表示增加，负数表示减少）
  /// - Note: 使用交易记录中的历史汇率保证计算准确性
  private func calculateTransactionImpact(
    transaction: TransactionModel,
    card: CardModel,
    currencies: [CurrencyModel]
  ) -> Double {
    switch transaction.transactionType {
    case .expense:
      // 支出交易：如果是支出卡片，余额减少
      if transaction.fromCardId == card.id {
        let actualExpense = transaction.transactionAmount - (transaction.discountAmount ?? 0)
        // 使用交易记录中的历史汇率
        let convertedAmount = CurrencyService.shared.convertAmountUsingTransactionRate(
          amount: actualExpense,
          transaction: transaction,
          targetCurrency: card.currency,
          forExpense: true
        )
        return -convertedAmount
      }
      return 0

    case .income:
      // 收入交易：如果是收入卡片，余额增加
      if transaction.toCardId == card.id {
        // 使用交易记录中的历史汇率
        return CurrencyService.shared.convertAmountUsingTransactionRate(
          amount: transaction.transactionAmount,
          transaction: transaction,
          targetCurrency: card.currency,
          forExpense: false
        )
      }
      return 0

    case .transfer:
      // 转账交易：转出卡片余额减少，转入卡片余额增加
      if transaction.fromCardId == card.id {
        // 转出卡片 - 使用支出汇率
        let convertedAmount = CurrencyService.shared.convertAmountUsingTransactionRate(
          amount: transaction.transactionAmount,
          transaction: transaction,
          targetCurrency: card.currency,
          forExpense: true
        )
        return -convertedAmount
      } else if transaction.toCardId == card.id {
        // 转入卡片 - 使用收入汇率
        return CurrencyService.shared.convertAmountUsingTransactionRate(
          amount: transaction.transactionAmount,
          transaction: transaction,
          targetCurrency: card.currency,
          forExpense: false
        )
      }
      return 0

    case .refund:
      // 退款交易：原始支出卡片余额增加，退款接收卡片余额增加
      if transaction.fromCardId == card.id || transaction.toCardId == card.id {
        // 使用收入汇率（退款是收入性质）
        return CurrencyService.shared.convertAmountUsingTransactionRate(
          amount: transaction.refundAmount ?? transaction.transactionAmount,
          transaction: transaction,
          targetCurrency: card.currency,
          forExpense: false
        )
      }
      return 0

    case .createCard, .adjustCard:
      // 基准点交易不在这里处理
      return 0
    }
  }

}
