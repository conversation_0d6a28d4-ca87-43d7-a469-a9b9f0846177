import Foundation

/// 汇率管理服务
///
/// 专门处理交易汇率的更新和管理，包括历史汇率的保护和智能更新。
final class ExchangeRateService {

  /// 单例实例
  static let shared = ExchangeRateService()

  /// 私有初始化器，确保单例模式
  private init() {}

  // MARK: - 汇率更新判断

  /// 检查交易编辑是否需要更新汇率
  ///
  /// 判断交易编辑的变更是否会影响汇率计算，只有在货币相关的变更时才需要更新汇率。
  /// 这避免了不必要的汇率覆盖，保护历史汇率数据。
  ///
  /// - Parameters:
  ///   - originalTransaction: 原始交易
  ///   - newCurrency: 新的交易货币（如果有变更）
  ///   - newFromCardId: 新的支出卡片ID（如果有变更）
  ///   - newToCardId: 新的收入卡片ID（如果有变更）
  ///   - cards: 卡片列表
  /// - Returns: 是否需要更新汇率
  func shouldUpdateExchangeRates(
    originalTransaction: TransactionModel,
    newCurrency: String? = nil,
    newFromCardId: UUID? = nil,
    newToCardId: UUID? = nil,
    cards: [CardModel]
  ) -> Bool {
    // 如果交易货币发生变化，需要更新汇率
    if let newCurrency = newCurrency, newCurrency != originalTransaction.currency {
      return true
    }

    // 如果卡片发生变化且涉及不同货币，需要更新汇率
    if let newFromCardId = newFromCardId, newFromCardId != originalTransaction.fromCardId {
      let originalCard = cards.first { $0.id == originalTransaction.fromCardId }
      let newCard = cards.first { $0.id == newFromCardId }
      if originalCard?.currency != newCard?.currency {
        return true
      }
    }

    if let newToCardId = newToCardId, newToCardId != originalTransaction.toCardId {
      let originalCard = cards.first { $0.id == originalTransaction.toCardId }
      let newCard = cards.first { $0.id == newToCardId }
      if originalCard?.currency != newCard?.currency {
        return true
      }
    }

    return false
  }

  /// 更新交易汇率（如果需要）
  ///
  /// 根据交易类型和货币设置，智能更新交易的汇率信息。
  /// 只在必要时更新，保护历史汇率数据。
  ///
  /// - Parameters:
  ///   - transaction: 要更新的交易
  ///   - cards: 卡片列表
  ///   - currencies: 货币列表
  ///   - baseCurrencyCode: 本位币代码
  func updateTransactionExchangeRatesIfNeeded(
    transaction: TransactionModel,
    cards: [CardModel],
    currencies: [CurrencyModel],
    baseCurrencyCode: String
  ) {
    switch transaction.transactionType {
    case .expense:
      updateExpenseTransactionRates(
        transaction: transaction,
        cards: cards,
        currencies: currencies,
        baseCurrencyCode: baseCurrencyCode
      )
    case .income:
      updateIncomeTransactionRates(
        transaction: transaction,
        cards: cards,
        currencies: currencies,
        baseCurrencyCode: baseCurrencyCode
      )
    case .transfer:
      updateTransferTransactionRates(
        transaction: transaction,
        cards: cards,
        currencies: currencies,
        baseCurrencyCode: baseCurrencyCode
      )
    case .refund:
      updateRefundTransactionRates(
        transaction: transaction,
        cards: cards,
        currencies: currencies,
        baseCurrencyCode: baseCurrencyCode
      )
    case .createCard, .adjustCard:
      // 系统交易不需要汇率更新
      break
    }
  }

  // MARK: - 私有汇率更新方法

  /// 更新支出交易的汇率
  private func updateExpenseTransactionRates(
    transaction: TransactionModel,
    cards: [CardModel],
    currencies: [CurrencyModel],
    baseCurrencyCode: String
  ) {
    guard let card = cards.first(where: { $0.id == transaction.fromCardId }) else { return }

    let transactionCurrency = transaction.currency.isEmpty ? baseCurrencyCode : transaction.currency
    let cardCurrency = card.currency

    // 计算交易货币到卡片货币的汇率
    transaction.expenseToCardRate = CurrencyService.shared.getCurrencyRate(
      from: transactionCurrency, to: cardCurrency, currencies: currencies)

    // 计算交易货币到本位币的汇率
    transaction.expenseToBaseRate = CurrencyService.shared.getCurrencyRate(
      from: transactionCurrency, to: baseCurrencyCode, currencies: currencies)

    // 收入相关汇率保持为1.0（支出交易不涉及收入）
    transaction.incomeToCardRate = 1.0
    transaction.incomeToBaseRate = 1.0

    print(
      "💱 支出交易汇率更新完成: \(transactionCurrency) -> \(cardCurrency)(\(transaction.expenseToCardRate)), \(baseCurrencyCode)(\(transaction.expenseToBaseRate))"
    )
  }

  /// 更新收入交易的汇率
  private func updateIncomeTransactionRates(
    transaction: TransactionModel,
    cards: [CardModel],
    currencies: [CurrencyModel],
    baseCurrencyCode: String
  ) {
    guard let card = cards.first(where: { $0.id == transaction.toCardId }) else { return }

    let transactionCurrency = transaction.currency.isEmpty ? baseCurrencyCode : transaction.currency
    let cardCurrency = card.currency

    // 支出相关汇率保持为1.0（收入交易不涉及支出）
    transaction.expenseToCardRate = 1.0
    transaction.expenseToBaseRate = 1.0

    // 计算交易货币到卡片货币的汇率
    transaction.incomeToCardRate = CurrencyService.shared.getCurrencyRate(
      from: transactionCurrency, to: cardCurrency, currencies: currencies)

    // 计算交易货币到本位币的汇率
    transaction.incomeToBaseRate = CurrencyService.shared.getCurrencyRate(
      from: transactionCurrency, to: baseCurrencyCode, currencies: currencies)

    print(
      "💱 收入交易汇率更新完成: \(transactionCurrency) -> \(cardCurrency)(\(transaction.incomeToCardRate)), \(baseCurrencyCode)(\(transaction.incomeToBaseRate))"
    )
  }

  /// 更新转账交易的汇率
  private func updateTransferTransactionRates(
    transaction: TransactionModel,
    cards: [CardModel],
    currencies: [CurrencyModel],
    baseCurrencyCode: String
  ) {
    guard let fromCard = cards.first(where: { $0.id == transaction.fromCardId }),
      let toCard = cards.first(where: { $0.id == transaction.toCardId })
    else { return }

    let transactionCurrency = transaction.currency.isEmpty ? baseCurrencyCode : transaction.currency
    let fromCardCurrency = fromCard.currency
    let toCardCurrency = toCard.currency

    // 计算支出相关汇率（从支出卡片角度）
    transaction.expenseToCardRate = CurrencyService.shared.getCurrencyRate(
      from: transactionCurrency, to: fromCardCurrency, currencies: currencies)
    transaction.expenseToBaseRate = CurrencyService.shared.getCurrencyRate(
      from: transactionCurrency, to: baseCurrencyCode, currencies: currencies)

    // 计算收入相关汇率（从收入卡片角度）
    transaction.incomeToCardRate = CurrencyService.shared.getCurrencyRate(
      from: transactionCurrency, to: toCardCurrency, currencies: currencies)
    transaction.incomeToBaseRate = CurrencyService.shared.getCurrencyRate(
      from: transactionCurrency, to: baseCurrencyCode, currencies: currencies)

    print(
      "💱 转账交易汇率更新完成: \(transactionCurrency) -> 支出卡片(\(transaction.expenseToCardRate)), 收入卡片(\(transaction.incomeToCardRate)), 本位币(\(transaction.expenseToBaseRate))"
    )
  }

  /// 更新退款交易的汇率
  private func updateRefundTransactionRates(
    transaction: TransactionModel,
    cards: [CardModel],
    currencies: [CurrencyModel],
    baseCurrencyCode: String
  ) {
    guard let card = cards.first(where: { $0.id == transaction.toCardId }) else { return }

    let transactionCurrency = transaction.currency.isEmpty ? baseCurrencyCode : transaction.currency
    let cardCurrency = card.currency

    // 支出相关汇率保持为1.0（退款交易不涉及支出）
    transaction.expenseToCardRate = 1.0
    transaction.expenseToBaseRate = 1.0

    // 计算交易货币到卡片货币的汇率
    transaction.incomeToCardRate = CurrencyService.shared.getCurrencyRate(
      from: transactionCurrency, to: cardCurrency, currencies: currencies)

    // 计算交易货币到本位币的汇率
    transaction.incomeToBaseRate = CurrencyService.shared.getCurrencyRate(
      from: transactionCurrency, to: baseCurrencyCode, currencies: currencies)

    print(
      "💱 退款交易汇率更新完成: \(transactionCurrency) -> \(cardCurrency)(\(transaction.incomeToCardRate)), \(baseCurrencyCode)(\(transaction.incomeToBaseRate))"
    )
  }
}
