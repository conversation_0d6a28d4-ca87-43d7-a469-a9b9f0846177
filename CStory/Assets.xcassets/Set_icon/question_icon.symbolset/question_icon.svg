<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg
PUBLIC "-//W3C//DTD SVG 1.1//EN"
"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 3300 2200">
<!--glyph: "10199A", point size: 100.0, font version: "20.0d8e1", template writer version: "138.0.0"-->
<style>.SFSymbolsPreviewWireframe {fill:none;opacity:1.0;stroke:black;stroke-width:0.5}
</style>
<g id="Notes">
<rect height="2200" id="artboard" style="fill:white;opacity:1" width="3300" x="0" y="0"/>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="292" y2="292"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 322)">Weight/Scale Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 559.711 322)">Ultralight</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 856.422 322)">Thin</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1153.13 322)">Light</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1449.84 322)">Regular</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 1746.56 322)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2043.27 322)">Semibold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2339.98 322)">Bold</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2636.69 322)">Heavy</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:middle;" transform="matrix(1 0 0 1 2933.4 322)">Black</text>
<line style="fill:none;stroke:black;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1903" y2="1903"/>
<g transform="matrix(0.2 0 0 0.2 263 1933)">
<path d="m46.2402 4.15039c21.7773 0 39.4531-17.627 39.4531-39.4043s-17.6758-39.4043-39.4531-39.4043c-21.7285 0-39.4043 17.627-39.4043 39.4043s17.6758 39.4043 39.4043 39.4043Zm0-7.42188c-17.6758 0-31.9336-14.3066-31.9336-31.9824s14.2578-31.9824 31.9336-31.9824 31.9824 14.3066 31.9824 31.9824-14.3066 31.9824-31.9824 31.9824Zm-17.9688-31.9824c0 2.14844 1.51367 3.61328 3.75977 3.61328h10.498v10.5957c0 2.19727 1.46484 3.71094 3.61328 3.71094 2.24609 0 3.71094-1.51367 3.71094-3.71094v-10.5957h10.5957c2.19727 0 3.71094-1.46484 3.71094-3.61328 0-2.19727-1.51367-3.71094-3.71094-3.71094h-10.5957v-10.5469c0-2.24609-1.46484-3.75977-3.71094-3.75977-2.14844 0-3.61328 1.51367-3.61328 3.75977v10.5469h-10.498c-2.24609 0-3.75977 1.51367-3.75977 3.71094Z"/>
</g>
<g transform="matrix(0.2 0 0 0.2 281.506 1933)">
<path d="m58.5449 14.5508c27.4902 0 49.8047-22.3145 49.8047-49.8047s-22.3145-49.8047-49.8047-49.8047-49.8047 22.3145-49.8047 49.8047 22.3145 49.8047 49.8047 49.8047Zm0-8.30078c-22.9492 0-41.5039-18.5547-41.5039-41.5039s18.5547-41.5039 41.5039-41.5039 41.5039 18.5547 41.5039 41.5039-18.5547 41.5039-41.5039 41.5039Zm-22.6562-41.5039c0 2.39258 1.66016 4.00391 4.15039 4.00391h14.3555v14.4043c0 2.44141 1.66016 4.15039 4.05273 4.15039 2.44141 0 4.15039-1.66016 4.15039-4.15039v-14.4043h14.4043c2.44141 0 4.15039-1.61133 4.15039-4.00391 0-2.44141-1.70898-4.15039-4.15039-4.15039h-14.4043v-14.3555c0-2.49023-1.70898-4.19922-4.15039-4.19922-2.39258 0-4.05273 1.70898-4.05273 4.19922v14.3555h-14.3555c-2.49023 0-4.15039 1.70898-4.15039 4.15039Z"/>
</g>
<g transform="matrix(0.2 0 0 0.2 304.924 1933)">
<path d="m74.8535 28.3203c35.1074 0 63.623-28.4668 63.623-63.5742s-28.5156-63.623-63.623-63.623-63.5742 28.5156-63.5742 63.623 28.4668 63.5742 63.5742 63.5742Zm0-9.08203c-30.127 0-54.4922-24.3652-54.4922-54.4922s24.3652-54.4922 54.4922-54.4922 54.4922 24.3652 54.4922 54.4922-24.3652 54.4922-54.4922 54.4922Zm-28.8574-54.4922c0 2.58789 1.85547 4.39453 4.58984 4.39453h19.7266v19.7754c0 2.68555 1.85547 4.58984 4.44336 4.58984 2.68555 0 4.54102-1.85547 4.54102-4.58984v-19.7754h19.7754c2.68555 0 4.58984-1.80664 4.58984-4.39453 0-2.73438-1.85547-4.58984-4.58984-4.58984h-19.7754v-19.7266c0-2.73438-1.85547-4.63867-4.54102-4.63867-2.58789 0-4.44336 1.9043-4.44336 4.63867v19.7266h-19.7266c-2.73438 0-4.58984 1.85547-4.58984 4.58984Z"/>
</g>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 263 1953)">Design Variations</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1971)">Symbols are supported in up to nine weights and three scales.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1989)">For optimal layout with text and other symbols, vertically align</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 2007)">symbols with the adjacent text.</text>
<line style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="776" x2="776" y1="1919" y2="1933"/>
<g transform="matrix(0.2 0 0 0.2 776 1933)">
<path d="m16.5527 0.78125c2.58789 0 3.85742-0.976562 4.78516-3.71094l6.29883-17.2363h28.8086l6.29883 17.2363c0.927734 2.73438 2.19727 3.71094 4.73633 3.71094 2.58789 0 4.24805-1.5625 4.24805-4.00391 0-0.830078-0.146484-1.61133-0.537109-2.63672l-22.9004-60.9863c-1.12305-2.97852-3.125-4.49219-6.25-4.49219-3.02734 0-5.07812 1.46484-6.15234 4.44336l-22.9004 61.084c-0.390625 1.02539-0.537109 1.80664-0.537109 2.63672 0 2.44141 1.5625 3.95508 4.10156 3.95508Zm13.4766-28.3691 11.8652-32.8613h0.244141l11.8652 32.8613Z"/>
</g>
<line style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="792.836" x2="792.836" y1="1919" y2="1933"/>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 776 1953)">Margins</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1971)">Leading and trailing margins on the left and right side of each symbol</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 1989)">can be adjusted by modifying the x-location of the margin guidelines.</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2007)">Modifications are automatically applied proportionally to all</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 776 2025)">scales and weights.</text>
<g transform="matrix(0.2 0 0 0.2 1289 1933)">
<path d="m14.209 9.32617 8.49609 8.54492c4.29688 4.3457 9.22852 4.05273 13.8672-1.07422l53.4668-58.9355-4.83398-4.88281-53.0762 58.3984c-1.75781 2.00195-3.41797 2.49023-5.76172 0.146484l-5.85938-5.81055c-2.34375-2.29492-1.80664-4.00391 0.195312-5.81055l57.373-54.0039-4.88281-4.83398-57.959 54.4434c-4.93164 4.58984-5.32227 9.47266-1.02539 13.8184Zm32.0801-90.9668c-2.09961 2.05078-2.24609 4.93164-1.07422 6.88477 1.17188 1.80664 3.4668 2.97852 6.68945 2.14844 7.32422-1.70898 14.9414-2.00195 22.0703 2.68555l-2.92969 7.27539c-1.70898 4.15039-0.830078 7.08008 1.85547 9.81445l11.4746 11.5723c2.44141 2.44141 4.49219 2.53906 7.32422 2.05078l5.32227-0.976562 3.32031 3.36914-0.195312 2.7832c-0.195312 2.49023 0.439453 4.39453 2.88086 6.78711l3.80859 3.71094c2.39258 2.39258 5.46875 2.53906 7.8125 0.195312l14.5508-14.5996c2.34375-2.34375 2.24609-5.32227-0.146484-7.71484l-3.85742-3.80859c-2.39258-2.39258-4.24805-3.17383-6.64062-2.97852l-2.88086 0.244141-3.22266-3.17383 1.2207-5.61523c0.634766-2.83203-0.146484-5.0293-3.07617-7.95898l-10.9863-10.9375c-16.6992-16.6016-38.8672-16.2109-53.3203-1.75781Zm7.4707 1.85547c12.1582-8.88672 28.6133-7.37305 39.7461 3.75977l12.1582 12.0605c1.17188 1.17188 1.36719 2.09961 1.02539 3.80859l-1.61133 7.42188 7.51953 7.42188 4.93164-0.292969c1.26953-0.0488281 1.66016 0.0488281 2.63672 1.02539l2.88086 2.88086-12.207 12.207-2.88086-2.88086c-0.976562-0.976562-1.12305-1.36719-1.07422-2.68555l0.341797-4.88281-7.4707-7.42188-7.61719 1.26953c-1.61133 0.341797-2.34375 0.195312-3.56445-0.976562l-10.0098-10.0098c-1.26953-1.17188-1.41602-2.00195-0.634766-3.85742l4.39453-10.4492c-7.8125-7.27539-17.9688-10.4004-28.125-7.42188-0.78125 0.195312-1.07422-0.439453-0.439453-0.976562Z"/>
</g>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;font-weight:bold;" transform="matrix(1 0 0 1 1289 1953)">Exporting</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1971)">Symbols should be outlined when exporting to ensure the</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 1289 1989)">design is preserved when submitting to Xcode.</text>
<text id="template-version" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1933)">Template v.6.0</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1951)">Requires Xcode 16 or greater</text>
<text id="descriptive-name" style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1969)">Generated from square.and.arrow.up</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;text-anchor:end;" transform="matrix(1 0 0 1 3036 1987)">Typeset at 100.0 points</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 726)">Small</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1156)">Medium</text>
<text style="stroke:none;fill:black;font-family:sans-serif;font-size:13;" transform="matrix(1 0 0 1 263 1586)">Large</text>
</g>
<g id="Guides">
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 696)">
<path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="696" y2="696"/>
<line id="Capline-S" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="625.541" y2="625.541"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1126)">
<path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1126" y2="1126"/>
<line id="Capline-M" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1055.54" y2="1055.54"/>
<g id="H-reference" style="fill:#27AAE1;stroke:none;" transform="matrix(1 0 0 1 339 1556)">
<path d="M0.993654 0L3.63775 0L29.3281-67.1323L30.0303-67.1323L30.0303-70.459L28.1226-70.459ZM11.6885-24.4799L46.9815-24.4799L46.2315-26.7285L12.4385-26.7285ZM55.1196 0L57.7637 0L30.6382-70.459L29.4326-70.459L29.4326-67.1323Z"/>
</g>
<line id="Baseline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1556" y2="1556"/>
<line id="Capline-L" style="fill:none;stroke:#27AAE1;opacity:1;stroke-width:0.5;" x1="263" x2="3036" y1="1485.54" y2="1485.54"/>
<line id="right-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2980.81" x2="2980.81" y1="600.785" y2="720.121"/>
<line id="left-margin-Black-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="2885.99" x2="2885.99" y1="600.785" y2="720.121"/>
<line id="right-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1493.96" x2="1493.96" y1="600.785" y2="720.121"/>
<line id="left-margin-Regular-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="1405.73" x2="1405.73" y1="600.785" y2="720.121"/>
<line id="right-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="601.625" x2="601.625" y1="600.785" y2="720.121"/>
<line id="left-margin-Ultralight-S" style="fill:none;stroke:#00AEEF;stroke-width:0.5;opacity:1.0;" x1="517.797" x2="517.797" y1="600.785" y2="720.121"/>
</g>
<g id="Symbols">
<g id="Black-S" transform="matrix(1 0 0 1 2884.57 696) translate(3 -81.25)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M45.7804 4.79175H46.2203C55.0691 4.7917 62.0038 4.79166 67.4145 5.51911C72.9526 6.2637 77.3227 7.8175 80.7528 11.2476C84.1829 14.6777 85.7367 19.0478 86.4813 24.586C87.2087 29.9967 87.2087 36.9313 87.2087 45.7802V46.22C87.2087 55.0689 87.2087 62.0035 86.4813 67.4142C85.7367 72.9524 84.1829 77.3225 80.7528 80.7526C77.3227 84.1827 72.9526 85.7365 67.4145 86.4811C62.0038 87.2085 55.0691 87.2085 46.2203 87.2084H45.7804C36.9315 87.2085 29.9969 87.2085 24.5862 86.4811C19.048 85.7365 14.678 84.1827 11.2478 80.7526C7.81774 77.3225 6.26394 72.9524 5.51935 67.4142C4.7919 62.0035 4.79194 55.0689 4.79199 46.22V45.7801C4.79194 36.9313 4.7919 29.9967 5.51935 24.586C6.26394 19.0478 7.81774 14.6777 11.2478 11.2476C14.678 7.8175 19.048 6.2637 24.5862 5.51911C29.9969 4.79166 36.9315 4.7917 45.7804 4.79175ZM25.3524 11.2178C20.4518 11.8767 17.4974 13.1298 15.3137 15.3135C13.13 17.4972 11.8769 20.4516 11.2181 25.3521C10.5481 30.3354 10.542 36.8836 10.542 46.0001C10.542 55.1166 10.5481 61.6648 11.2181 66.648C11.8769 71.5486 13.13 74.503 15.3137 76.6867C17.4974 78.8704 20.4518 80.1235 25.3524 80.7823C30.3356 81.4523 36.8838 81.4584 46.0003 81.4584C55.1169 81.4584 61.665 81.4523 66.6483 80.7823C71.5488 80.1235 74.5033 78.8704 76.6869 76.6867C78.8706 74.503 80.1237 71.5486 80.7826 66.648C81.4526 61.6648 81.4587 55.1166 81.4587 46.0001C81.4587 36.8836 81.4526 30.3354 80.7826 25.3521C80.1237 20.4516 78.8706 17.4972 76.6869 15.3135C74.5033 13.1298 71.5488 11.8767 66.6483 11.2178C61.665 10.5479 55.1169 10.5417 46.0003 10.5417C36.8838 10.5417 30.3356 10.5479 25.3524 11.2178Z" fill="#1C274C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M46 29.7083C43.6183 29.7083 41.6875 31.639 41.6875 34.0208C41.6875 35.6086 40.4003 36.8958 38.8125 36.8958C37.2247 36.8958 35.9375 35.6086 35.9375 34.0208C35.9375 28.4634 40.4426 23.9583 46 23.9583C51.5574 23.9583 56.0625 28.4634 56.0625 34.0208C56.0625 37.7135 54.0717 40.9383 51.1185 42.6855C50.3643 43.1317 49.7417 43.6189 49.3361 44.0969C48.9423 44.561 48.875 44.8637 48.875 45.0416V49.8333C48.875 51.4211 47.5878 52.7083 46 52.7083C44.4122 52.7083 43.125 51.4211 43.125 49.8333V45.0416C43.125 43.1024 43.974 41.5289 44.9519 40.3765C45.9181 39.238 47.1228 38.3685 48.1906 37.7367C49.467 36.9816 50.3125 35.5984 50.3125 34.0208C50.3125 31.639 48.3817 29.7083 46 29.7083Z" fill="#1C274C"/>
<path d="M49.8337 61.3333C49.8337 63.4504 48.1174 65.1667 46.0003 65.1667C43.8832 65.1667 42.167 63.4504 42.167 61.3333C42.167 59.2162 43.8832 57.5 46.0003 57.5C48.1174 57.5 49.8337 59.2162 49.8337 61.3333Z" fill="#1C274C"/>
</g>
<g id="Regular-S" transform="matrix(1 0 0 1 1403.58 696) translate(0 -81.25)">
 <path fill-rule="evenodd" clip-rule="evenodd" d="M45.7804 4.79175H46.2203C55.0691 4.7917 62.0038 4.79166 67.4145 5.51911C72.9526 6.2637 77.3227 7.8175 80.7528 11.2476C84.1829 14.6777 85.7367 19.0478 86.4813 24.586C87.2087 29.9967 87.2087 36.9313 87.2087 45.7802V46.22C87.2087 55.0689 87.2087 62.0035 86.4813 67.4142C85.7367 72.9524 84.1829 77.3225 80.7528 80.7526C77.3227 84.1827 72.9526 85.7365 67.4145 86.4811C62.0038 87.2085 55.0691 87.2085 46.2203 87.2084H45.7804C36.9315 87.2085 29.9969 87.2085 24.5862 86.4811C19.048 85.7365 14.678 84.1827 11.2478 80.7526C7.81774 77.3225 6.26394 72.9524 5.51935 67.4142C4.7919 62.0035 4.79194 55.0689 4.79199 46.22V45.7801C4.79194 36.9313 4.7919 29.9967 5.51935 24.586C6.26394 19.0478 7.81774 14.6777 11.2478 11.2476C14.678 7.8175 19.048 6.2637 24.5862 5.51911C29.9969 4.79166 36.9315 4.7917 45.7804 4.79175ZM25.3524 11.2178C20.4518 11.8767 17.4974 13.1298 15.3137 15.3135C13.13 17.4972 11.8769 20.4516 11.2181 25.3521C10.5481 30.3354 10.542 36.8836 10.542 46.0001C10.542 55.1166 10.5481 61.6648 11.2181 66.648C11.8769 71.5486 13.13 74.503 15.3137 76.6867C17.4974 78.8704 20.4518 80.1235 25.3524 80.7823C30.3356 81.4523 36.8838 81.4584 46.0003 81.4584C55.1169 81.4584 61.665 81.4523 66.6483 80.7823C71.5488 80.1235 74.5033 78.8704 76.6869 76.6867C78.8706 74.503 80.1237 71.5486 80.7826 66.648C81.4526 61.6648 81.4587 55.1166 81.4587 46.0001C81.4587 36.8836 81.4526 30.3354 80.7826 25.3521C80.1237 20.4516 78.8706 17.4972 76.6869 15.3135C74.5033 13.1298 71.5488 11.8767 66.6483 11.2178C61.665 10.5479 55.1169 10.5417 46.0003 10.5417C36.8838 10.5417 30.3356 10.5479 25.3524 11.2178Z" fill="#1C274C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M46 29.7083C43.6183 29.7083 41.6875 31.639 41.6875 34.0208C41.6875 35.6086 40.4003 36.8958 38.8125 36.8958C37.2247 36.8958 35.9375 35.6086 35.9375 34.0208C35.9375 28.4634 40.4426 23.9583 46 23.9583C51.5574 23.9583 56.0625 28.4634 56.0625 34.0208C56.0625 37.7135 54.0717 40.9383 51.1185 42.6855C50.3643 43.1317 49.7417 43.6189 49.3361 44.0969C48.9423 44.561 48.875 44.8637 48.875 45.0416V49.8333C48.875 51.4211 47.5878 52.7083 46 52.7083C44.4122 52.7083 43.125 51.4211 43.125 49.8333V45.0416C43.125 43.1024 43.974 41.5289 44.9519 40.3765C45.9181 39.238 47.1228 38.3685 48.1906 37.7367C49.467 36.9816 50.3125 35.5984 50.3125 34.0208C50.3125 31.639 48.3817 29.7083 46 29.7083Z" fill="#1C274C"/>
<path d="M49.8337 61.3333C49.8337 63.4504 48.1174 65.1667 46.0003 65.1667C43.8832 65.1667 42.167 63.4504 42.167 61.3333C42.167 59.2162 43.8832 57.5 46.0003 57.5C48.1174 57.5 49.8337 59.2162 49.8337 61.3333Z" fill="#1C274C"/>
</g>
<g id="Ultralight-S" transform="matrix(1 0 0 1 515.649 696) translate(-2 -81.25)">
 <path fill-rule="evenodd" clip-rule="evenodd" d="M45.7804 4.79175H46.2203C55.0691 4.7917 62.0038 4.79166 67.4145 5.51911C72.9526 6.2637 77.3227 7.8175 80.7528 11.2476C84.1829 14.6777 85.7367 19.0478 86.4813 24.586C87.2087 29.9967 87.2087 36.9313 87.2087 45.7802V46.22C87.2087 55.0689 87.2087 62.0035 86.4813 67.4142C85.7367 72.9524 84.1829 77.3225 80.7528 80.7526C77.3227 84.1827 72.9526 85.7365 67.4145 86.4811C62.0038 87.2085 55.0691 87.2085 46.2203 87.2084H45.7804C36.9315 87.2085 29.9969 87.2085 24.5862 86.4811C19.048 85.7365 14.678 84.1827 11.2478 80.7526C7.81774 77.3225 6.26394 72.9524 5.51935 67.4142C4.7919 62.0035 4.79194 55.0689 4.79199 46.22V45.7801C4.79194 36.9313 4.7919 29.9967 5.51935 24.586C6.26394 19.0478 7.81774 14.6777 11.2478 11.2476C14.678 7.8175 19.048 6.2637 24.5862 5.51911C29.9969 4.79166 36.9315 4.7917 45.7804 4.79175ZM25.3524 11.2178C20.4518 11.8767 17.4974 13.1298 15.3137 15.3135C13.13 17.4972 11.8769 20.4516 11.2181 25.3521C10.5481 30.3354 10.542 36.8836 10.542 46.0001C10.542 55.1166 10.5481 61.6648 11.2181 66.648C11.8769 71.5486 13.13 74.503 15.3137 76.6867C17.4974 78.8704 20.4518 80.1235 25.3524 80.7823C30.3356 81.4523 36.8838 81.4584 46.0003 81.4584C55.1169 81.4584 61.665 81.4523 66.6483 80.7823C71.5488 80.1235 74.5033 78.8704 76.6869 76.6867C78.8706 74.503 80.1237 71.5486 80.7826 66.648C81.4526 61.6648 81.4587 55.1166 81.4587 46.0001C81.4587 36.8836 81.4526 30.3354 80.7826 25.3521C80.1237 20.4516 78.8706 17.4972 76.6869 15.3135C74.5033 13.1298 71.5488 11.8767 66.6483 11.2178C61.665 10.5479 55.1169 10.5417 46.0003 10.5417C36.8838 10.5417 30.3356 10.5479 25.3524 11.2178Z" fill="#1C274C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M46 29.7083C43.6183 29.7083 41.6875 31.639 41.6875 34.0208C41.6875 35.6086 40.4003 36.8958 38.8125 36.8958C37.2247 36.8958 35.9375 35.6086 35.9375 34.0208C35.9375 28.4634 40.4426 23.9583 46 23.9583C51.5574 23.9583 56.0625 28.4634 56.0625 34.0208C56.0625 37.7135 54.0717 40.9383 51.1185 42.6855C50.3643 43.1317 49.7417 43.6189 49.3361 44.0969C48.9423 44.561 48.875 44.8637 48.875 45.0416V49.8333C48.875 51.4211 47.5878 52.7083 46 52.7083C44.4122 52.7083 43.125 51.4211 43.125 49.8333V45.0416C43.125 43.1024 43.974 41.5289 44.9519 40.3765C45.9181 39.238 47.1228 38.3685 48.1906 37.7367C49.467 36.9816 50.3125 35.5984 50.3125 34.0208C50.3125 31.639 48.3817 29.7083 46 29.7083Z" fill="#1C274C"/>
<path d="M49.8337 61.3333C49.8337 63.4504 48.1174 65.1667 46.0003 65.1667C43.8832 65.1667 42.167 63.4504 42.167 61.3333C42.167 59.2162 43.8832 57.5 46.0003 57.5C48.1174 57.5 49.8337 59.2162 49.8337 61.3333Z" fill="#1C274C"/>
</g>
</g>
</svg>