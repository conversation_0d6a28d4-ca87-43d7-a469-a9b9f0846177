//
//  DateFormattingHelper.swift
//  CStory
//
//  Created by NZUE on 2025/7/12.
//

import Foundation

/// 日期格式化辅助工具
///
/// 提供日期格式化的统一接口，实现各种日期显示样式和计算功能。
/// 采用单例模式确保全局唯一实例。
/// 该类中的所有方法都是线程安全的，可以在任何线程中调用。
final class DateFormattingHelper {
  /// 共享实例
  ///
  /// 使用单例模式，确保全局只有一个实例，减少资源消耗。
  /// 所有对DateFormattingHelper的调用都应使用此共享实例。
  ///
  /// ## 示例
  /// ```swift
  /// // 使用共享实例格式化日期
  /// let formattedDate = DateFormattingHelper.shared.format(date: Date(), with: "yyyy-MM-dd")
  /// ```
  static let shared = DateFormattingHelper()

  /// 常用日期格式
  enum DateFormat {
    /// 年月日 (2024-05-15)
    case yearMonthDay
    /// 中文年月日 (2024年05月15日)
    case chineseYearMonthDay
    /// 月日 (05-15)
    case monthDay
    /// 中文月日 (05月15日)
    case chineseMonthDay
    /// 时分 (14:30)
    case hourMinute
    /// 时分秒 (14:30:45)
    case hourMinuteSecond
    /// 完整日期时间 (2024-05-15 14:30:45)
    case fullDateTime
    /// 中文完整日期时间 (2024年05月15日 14:30:45)
    case chineseFullDateTime
    /// 自定义格式
    case custom(String)

    /// 获取格式字符串
    var formatString: String {
      switch self {
      case .yearMonthDay:
        return "yyyy-MM-dd"
      case .chineseYearMonthDay:
        return "yyyy年MM月dd日"
      case .monthDay:
        return "MM-dd"
      case .chineseMonthDay:
        return "MM月dd日"
      case .hourMinute:
        return "HH:mm"
      case .hourMinuteSecond:
        return "HH:mm:ss"
      case .fullDateTime:
        return "yyyy-MM-dd HH:mm:ss"
      case .chineseFullDateTime:
        return "yyyy年MM月dd日 HH:mm:ss"
      case .custom(let format):
        return format
      }
    }
  }

  /// 私有构造方法
  ///
  /// 防止外部直接实例化，确保单例模式的实现。
  private init() {}

  /// 创建一个带有指定格式的 DateFormatter 实例
  ///
  /// 快速创建并配置DateFormatter，避免重复编写相同的初始化代码。
  ///
  /// - Parameter format: 日期格式字符串，例如 "yyyy-MM-dd"
  /// - Returns: 配置了指定格式的 DateFormatter 实例
  ///
  /// - Note: 该方法会创建一个新的 DateFormatter 实例，并设置其格式。
  ///   创建DateFormatter是相对耗时的操作，如果在性能关键场景中频繁使用，
  ///   建议缓存formatter实例而不是反复创建。
  private func createFormatter(with format: String) -> DateFormatter {
    let formatter = DateFormatter()
    formatter.dateFormat = format
    formatter.locale = Locale(identifier: "zh_CN")
    return formatter
  }

  /// 将日期格式化为指定格式的字符串
  ///
  /// 根据提供的格式将Date对象转换为对应的字符串表示。
  ///
  /// - Parameters:
  ///   - date: 需要格式化的日期
  ///   - format: 日期格式枚举
  /// - Returns: 格式化后的日期字符串
  ///
  /// ## 示例
  /// ```swift
  /// let now = Date()
  ///
  /// // 格式化为年月日
  /// let dateString = DateFormattingService.shared.format(date: now, as: .yearMonthDay)
  /// // 输出如: "2024-05-15"
  ///
  /// // 使用自定义格式
  /// let customString = DateFormattingService.shared.format(date: now, as: .custom("yyyy/MM/dd"))
  /// // 输出如: "2024/05/15"
  /// ```
  func format(date: Date, as format: DateFormat) -> String {
    let formatter = createFormatter(with: format.formatString)
    return formatter.string(from: date)
  }

  /// 将日期格式化为指定格式的字符串
  ///
  /// 根据提供的格式字符串将Date对象转换为对应的字符串表示。
  ///
  /// - Parameters:
  ///   - date: 需要格式化的日期
  ///   - format: 日期格式字符串
  /// - Returns: 格式化后的日期字符串
  ///
  /// ## 示例
  /// ```swift
  /// let now = Date()
  ///
  /// // 格式化为年月日
  /// let dateString = DateFormattingService.shared.format(date: now, with: "yyyy年MM月dd日")
  /// // 输出如: "2024年05月15日"
  /// ```
  func format(date: Date, with format: String) -> String {
    let formatter = createFormatter(with: format)
    return formatter.string(from: date)
  }

  /// 将字符串解析为日期
  ///
  /// 根据提供的格式字符串将字符串解析为Date对象。
  ///
  /// - Parameters:
  ///   - string: 需要解析的字符串
  ///   - format: 日期格式枚举
  /// - Returns: 解析后的日期，如果解析失败则返回nil
  ///
  /// ## 示例
  /// ```swift
  /// let dateString = "2024-05-15"
  /// if let date = DateFormattingService.shared.parse(string: dateString, as: .yearMonthDay) {
  ///     // 成功解析日期
  /// }
  /// ```
  func parse(string: String, as format: DateFormat) -> Date? {
    let formatter = createFormatter(with: format.formatString)
    return formatter.date(from: string)
  }

  /// 将字符串解析为日期
  ///
  /// 根据提供的格式字符串将字符串解析为Date对象。
  ///
  /// - Parameters:
  ///   - string: 需要解析的字符串
  ///   - format: 日期格式字符串
  /// - Returns: 解析后的日期，如果解析失败则返回nil
  func parse(string: String, with format: String) -> Date? {
    let formatter = createFormatter(with: format)
    return formatter.date(from: string)
  }

  /// 智能格式化日期
  ///
  /// 根据日期与当前时间的相对关系，自动选择最适合的格式显示。
  /// 这种智能格式化方式在社交媒体、聊天应用等场景中特别有用。
  ///
  /// 根据日期的不同情况返回不同的格式：
  /// 1. 今天：显示"今天 HH:mm"
  /// 2. 昨天：显示"昨天 HH:mm"
  /// 3. 前天：显示"前天 HH:mm"
  /// 4. 本周内：显示"周x HH:mm"
  /// 5. 当年内：显示"MM月dd日 HH:mm"
  /// 6. 其他：显示"yyyy年MM月dd日 HH:mm"
  ///
  /// - Parameter date: 需要格式化的日期
  /// - Returns: 格式化后的日期字符串
  ///
  /// ## 示例
  /// ```swift
  /// let now = Date()
  /// let yesterday = Calendar.current.date(byAdding: .day, value: -1, to: now)!
  /// let lastWeek = Calendar.current.date(byAdding: .day, value: -6, to: now)!
  /// let lastYear = Calendar.current.date(byAdding: .year, value: -1, to: now)!
  ///
  /// DateFormattingService.shared.smartFormat(date: now)         // "今天 15:30"
  /// DateFormattingService.shared.smartFormat(date: yesterday)   // "昨天 15:30"
  /// DateFormattingService.shared.smartFormat(date: lastWeek)    // "周六 15:30"
  /// DateFormattingService.shared.smartFormat(date: lastYear)    // "2023年05月15日 15:30"
  /// ```
  func smartFormat(date: Date) -> String {
    let now = Date()
    let calendar = Calendar.current

    // 获取时间部分
    let timeString = format(date: date, as: .hourMinute)

    // 判断是否是今天、昨天、前天
    if calendar.isDateInToday(date) {
      return "今天 \(timeString)"
    }

    if calendar.isDateInYesterday(date) {
      return "昨天 \(timeString)"
    }

    // 判断前天
    let twoDaysAgo = calendar.date(byAdding: .day, value: -2, to: now)!
    if calendar.isDate(date, inSameDayAs: twoDaysAgo) {
      return "前天 \(timeString)"
    }

    // 判断是否在本周内
    let currentWeek = calendar.component(.weekOfYear, from: now)
    let dateWeek = calendar.component(.weekOfYear, from: date)
    let currentYear = calendar.component(.year, from: now)
    let dateYear = calendar.component(.year, from: date)

    if currentWeek == dateWeek && currentYear == dateYear {
      let weekday = calendar.component(.weekday, from: date)
      let weekdaySymbols = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"]
      return "\(weekdaySymbols[weekday - 1]) \(timeString)"
    }

    // 判断是否在当年
    if currentYear == dateYear {
      return format(date: date, as: .custom("MM月dd日 HH:mm"))
    }

    // 其他情况显示完整日期
    return format(date: date, as: .chineseFullDateTime)
  }

  /// 计算两个日期之间的时间差
  ///
  /// 计算两个日期之间的差值，以指定的时间单位返回。
  ///
  /// - Parameters:
  ///   - from: 起始日期
  ///   - to: 结束日期
  ///   - component: 时间单位（年、月、日、小时等）
  /// - Returns: 时间差值
  ///
  /// ## 示例
  /// ```swift
  /// let now = Date()
  /// let nextWeek = Calendar.current.date(byAdding: .day, value: 7, to: now)!
  ///
  /// // 计算日期差
  /// let daysDiff = DateFormattingService.shared.dateDifference(from: now, to: nextWeek, in: .day)
  /// // 结果为 7
  /// ```
  func dateDifference(from: Date, to: Date, in component: Calendar.Component) -> Int {
    let calendar = Calendar.current
    let components = calendar.dateComponents([component], from: from, to: to)

    switch component {
    case .year:
      return components.year ?? 0
    case .month:
      return components.month ?? 0
    case .day:
      return components.day ?? 0
    case .hour:
      return components.hour ?? 0
    case .minute:
      return components.minute ?? 0
    case .second:
      return components.second ?? 0
    default:
      return 0
    }
  }

  /// 获取相对于给定日期的新日期
  ///
  /// 计算相对于给定日期偏移指定单位的新日期。
  ///
  /// - Parameters:
  ///   - date: 基准日期
  ///   - value: 偏移值（可为正或负）
  ///   - component: 时间单位（年、月、日、小时等）
  /// - Returns: 计算后的新日期，如果计算失败则返回nil
  ///
  /// ## 示例
  /// ```swift
  /// let now = Date()
  ///
  /// // 获取下周同一天
  /// if let nextWeek = DateFormattingService.shared.date(from: now, byAdding: 7, to: .day) {
  ///     // 成功计算出下周日期
  /// }
  /// ```
  func date(from date: Date, byAdding value: Int, to component: Calendar.Component) -> Date? {
    let calendar = Calendar.current
    return calendar.date(byAdding: component, value: value, to: date)
  }

  /// 获取日期的开始时间
  ///
  /// 返回给定日期当天的开始时间（0点0分0秒）。
  ///
  /// - Parameter date: 输入日期
  /// - Returns: 该日期的开始时间
  ///
  /// ## 示例
  /// ```swift
  /// let now = Date()
  /// let startOfDay = DateFormattingService.shared.startOfDay(for: now)
  /// // 结果为今天的0点0分0秒
  /// ```
  func startOfDay(for date: Date) -> Date {
    let calendar = Calendar.current
    return calendar.startOfDay(for: date)
  }

  /// 获取日期的结束时间
  ///
  /// 返回给定日期当天的结束时间（23点59分59秒）。
  ///
  /// - Parameter date: 输入日期
  /// - Returns: 该日期的结束时间
  ///
  /// ## 示例
  /// ```swift
  /// let now = Date()
  /// let endOfDay = DateFormattingService.shared.endOfDay(for: now)
  /// // 结果为今天的23点59分59秒
  /// ```
  func endOfDay(for date: Date) -> Date {
    let calendar = Calendar.current
    var components = DateComponents()
    components.day = 1
    components.second = -1

    let startOfDay = calendar.startOfDay(for: date)
    return calendar.date(byAdding: components, to: startOfDay) ?? date
  }

  /// 格式化日期头部显示
  ///
  /// 专门用于交易列表等场景的日期头部显示，只返回日期部分，不包含时间。
  /// 根据日期与当前时间的相对关系，自动选择最适合的格式显示。
  ///
  /// 根据日期的不同情况返回不同的格式：
  /// 1. 今天：显示"今天"
  /// 2. 昨天：显示"昨天"
  /// 3. 前天：显示"前天"
  /// 4. 其他：显示"M月d日"格式
  ///
  /// - Parameter date: 需要格式化的日期
  /// - Returns: 格式化后的日期字符串
  ///
  /// ## 示例
  /// ```swift
  /// let now = Date()
  /// let yesterday = Calendar.current.date(byAdding: .day, value: -1, to: now)!
  /// let lastWeek = Calendar.current.date(byAdding: .day, value: -7, to: now)!
  ///
  /// DateFormattingService.shared.formatDateHeader(date: now)         // "今天"
  /// DateFormattingService.shared.formatDateHeader(date: yesterday)   // "昨天"
  /// DateFormattingService.shared.formatDateHeader(date: twoDaysAgo)  // "前天"
  /// DateFormattingService.shared.formatDateHeader(date: lastWeek)    // "6月26日"
  /// ```
  func formatDateHeader(date: Date) -> String {
    let calendar = Calendar.current

    if calendar.isDateInToday(date) {
      return "今天"
    } else if calendar.isDateInYesterday(date) {
      return "昨天"
    } else {
      // 判断前天
      let now = Date()
      let twoDaysAgo = calendar.date(byAdding: .day, value: -2, to: now)!
      if calendar.isDate(date, inSameDayAs: twoDaysAgo) {
        return "前天"
      } else {
        return format(date: date, with: "M月d日")
      }
    }
  }

  // MARK: - Calendar Component Utilities

  /// 提取日期的年月日组件
  ///
  /// - Parameter date: 输入日期
  /// - Returns: 年、月、日组件的元组
  func extractDateComponents(_ date: Date) -> (year: Int, month: Int, day: Int) {
    let calendar = Calendar.current
    let components = calendar.dateComponents([.year, .month, .day], from: date)

    return (
      year: components.year ?? 0,
      month: components.month ?? 0,
      day: components.day ?? 0
    )
  }

  /// 创建指定年月日的日期
  ///
  /// - Parameters:
  ///   - year: 年
  ///   - month: 月
  ///   - day: 日
  ///   - hour: 小时，默认为0
  ///   - minute: 分钟，默认为0
  ///   - second: 秒，默认为0
  /// - Returns: 创建的日期，失败返回nil
  func createDate(
    year: Int,
    month: Int,
    day: Int,
    hour: Int = 0,
    minute: Int = 0,
    second: Int = 0
  ) -> Date? {
    let calendar = Calendar.current
    var components = DateComponents()
    components.year = year
    components.month = month
    components.day = day
    components.hour = hour
    components.minute = minute
    components.second = second

    return calendar.date(from: components)
  }

  /// 获取指定月份的天数
  ///
  /// - Parameters:
  ///   - year: 年
  ///   - month: 月
  /// - Returns: 该月的天数
  func daysInMonth(year: Int, month: Int) -> Int {
    guard let date = createDate(year: year, month: month, day: 1) else {
      return 0
    }

    let calendar = Calendar.current
    let range = calendar.range(of: .day, in: .month, for: date)
    return range?.count ?? 0
  }

  /// 获取月份的最后一天日期
  ///
  /// - Parameter date: 输入日期
  /// - Returns: 该月最后一天的日期
  func lastDayOfMonth(for date: Date) -> Date {
    let calendar = Calendar.current
    let components = calendar.dateComponents([.year, .month], from: date)

    guard let firstDayOfMonth = calendar.date(from: components),
      let firstDayOfNextMonth = calendar.date(byAdding: .month, value: 1, to: firstDayOfMonth),
      let lastDayOfMonth = calendar.date(byAdding: .day, value: -1, to: firstDayOfNextMonth)
    else {
      return date
    }

    return lastDayOfMonth
  }
}
