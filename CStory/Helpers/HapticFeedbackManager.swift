//
//  HapticFeedbackManager.swift
//  CStory
//
//  Created by <PERSON> on 2025/7/22.
//

import SwiftUI
import UIKit

/// 触觉反馈管理器
///
/// 提供统一的触觉反馈接口，支持不同类型的反馈强度和模式。
/// 相比直接使用 hapticFeedback modifier，提供更精确的控制和更好的性能。
final class HapticFeedbackManager: ObservableObject {
  
  // MARK: - Singleton
  
  nonisolated static let shared = HapticFeedbackManager()
  
  private init() {}
  
  // MARK: - Feedback Types
  
  enum FeedbackType {
    case selection      // 选择操作，轻微反馈
    case impactLight    // 轻度冲击，按钮点击
    case impactMedium   // 中度冲击，重要操作
    case impactHeavy    // 重度冲击，错误或警告
    case success        // 成功操作
    case warning        // 警告操作
    case error          // 错误操作
  }
  
  // MARK: - Published Properties
  
  /// 触觉反馈触发器（用于SwiftUI的hapticFeedback modifier）
  @Published var triggerSelection = false
  @Published var triggerImpactLight = false
  @Published var triggerImpactMedium = false
  @Published var triggerImpactHeavy = false
  @Published var triggerSuccess = false
  @Published var triggerWarning = false
  @Published var triggerError = false
  
  // MARK: - Private Properties
  
  private let selectionFeedback = UISelectionFeedbackGenerator()
  private let impactLightFeedback = UIImpactFeedbackGenerator(style: .light)
  private let impactMediumFeedback = UIImpactFeedbackGenerator(style: .medium)
  private let impactHeavyFeedback = UIImpactFeedbackGenerator(style: .heavy)
  private let notificationFeedback = UINotificationFeedbackGenerator()
  
  // MARK: - Public Methods
  
  /// 触发触觉反馈
  /// - Parameter type: 反馈类型
  @MainActor
  func trigger(_ type: FeedbackType) {
    switch type {
    case .selection:
      triggerSelection.toggle()
      selectionFeedback.selectionChanged()
      
    case .impactLight:
      triggerImpactLight.toggle()
      impactLightFeedback.impactOccurred()
      
    case .impactMedium:
      triggerImpactMedium.toggle()
      impactMediumFeedback.impactOccurred()
      
    case .impactHeavy:
      triggerImpactHeavy.toggle()
      impactHeavyFeedback.impactOccurred()
      
    case .success:
      triggerSuccess.toggle()
      notificationFeedback.notificationOccurred(.success)
      
    case .warning:
      triggerWarning.toggle()
      notificationFeedback.notificationOccurred(.warning)
      
    case .error:
      triggerError.toggle()
      notificationFeedback.notificationOccurred(.error)
    }
  }
  
  /// 预准备触觉反馈（提高响应速度）
  /// - Parameter type: 要预准备的反馈类型
  func prepare(_ type: FeedbackType) {
    switch type {
    case .selection:
      selectionFeedback.prepare()
    case .impactLight:
      impactLightFeedback.prepare()
    case .impactMedium:
      impactMediumFeedback.prepare()
    case .impactHeavy:
      impactHeavyFeedback.prepare()
    case .success, .warning, .error:
      notificationFeedback.prepare()
    }
  }
  
  /// 批量预准备常用的触觉反馈
  func prepareCommon() {
    selectionFeedback.prepare()
    impactLightFeedback.prepare()
    impactMediumFeedback.prepare()
  }
}