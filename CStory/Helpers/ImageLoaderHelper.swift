//
//  ImageLoaderHelper.swift
//  CStory
//
//  Created by NZUE on 2025/7/12.
//

import SwiftUI
import UIKit

/// 从 Assets 加载交易类别图标并转换为 Data 格式
struct ImageLoaderHelper {

  /// 根据类别 ID 加载对应的图片数据
  /// - Parameter categoryId: 类别标识符，对应 Assets 中的图片文件名
  /// - Returns: PNG 格式的图片数据，加载失败返回 nil
  static func loadCategoryImageData(for categoryId: String) -> Data? {
    // 尝试从 TransactionCategory 子目录加载
    if let image = UIImage(named: "TransactionCategory/\(categoryId)") {
      return image.pngData()
    }

    // 备用：直接使用 categoryId 作为图片名
    if let image = UIImage(named: categoryId) {
      return image.pngData()
    }

    return nil
  }

}