//
//  MotionManagerHelper.swift
//  CStory
//
//  Created by NZUE on 2025/7/12.
//

import Combine
import CoreMotion
import SwiftUI

/// 陀螺仪运动管理器，为 UI 元素提供 3D 倾斜效果
class MotionManagerHelper: ObservableObject {
  #if os(iOS)
    private let motionManager = CMMotionManager()
  #endif
  private var cancellables = Set<AnyCancellable>()

  /// 当前倾斜角度（度数）
  @Published var tiltX: Double = 0.0
  @Published var tiltY: Double = 0.0

  /// 是否启用运动效果
  @Published var isMotionEnabled: Bool = true

  /// 最大倾斜角度（度数）
  private let maxTiltDegrees: Double = 5.0

  /// 运动更新频率（Hz）
  private let updateInterval: TimeInterval = 1.0 / 60.0

  init() {
    startMotionUpdates()
  }

  deinit {
    stopMotionUpdates()
  }

  /// 开始运动更新
  private func startMotionUpdates() {
    #if os(iOS)
      guard motionManager.isDeviceMotionAvailable else {
        print("设备运动数据不可用")
        return
      }

      motionManager.deviceMotionUpdateInterval = updateInterval
      motionManager.startDeviceMotionUpdates(to: .main) { [weak self] motion, error in
        guard let self = self, let motion = motion, error == nil else {
          if let error = error {
            print("陀螺仪数据获取失败: \(error.localizedDescription)")
          }
          return
        }

        if self.isMotionEnabled {
          self.updateTilt(from: motion)
        }
      }
    #else
      print("陀螺仪功能仅在iOS设备上可用")
    #endif
  }

  /// 停止运动更新
  private func stopMotionUpdates() {
    #if os(iOS)
      motionManager.stopDeviceMotionUpdates()
    #endif
  }

  /// 更新倾斜角度
  private func updateTilt(from motion: Any) {
    #if os(iOS)
      guard let motion = motion as? CMDeviceMotion else { return }

      let attitude = motion.attitude
      
      // 将弧度转换为度数并限制在最大范围内
      let newTiltX = max(-maxTiltDegrees, min(maxTiltDegrees, attitude.pitch * 180 / .pi))
      let newTiltY = max(-maxTiltDegrees, min(maxTiltDegrees, attitude.roll * 180 / .pi))

      // 使用动画平滑更新
      withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
        self.tiltX = newTiltX
        self.tiltY = newTiltY
      }
    #endif
  }
}

/// 将运动效果应用到视图的修饰符
struct MotionEffectModifier: ViewModifier {
  @StateObject private var motionManager = MotionManagerHelper()

  let isEnabled: Bool
  let intensity: Double

  init(isEnabled: Bool = true, intensity: Double = 1.0) {
    self.isEnabled = isEnabled
    self.intensity = max(0.0, min(1.0, intensity))
  }

  func body(content: Content) -> some View {
    content
      .rotation3DEffect(
        .degrees(motionManager.tiltX * intensity),
        axis: (x: 1, y: 0, z: 0)
      )
      .rotation3DEffect(
        .degrees(-motionManager.tiltY * intensity),
        axis: (x: 0, y: 1, z: 0)
      )
      .onChange(of: isEnabled) { _, newValue in
        motionManager.isMotionEnabled = newValue
      }
  }
}

// MARK: - View Extension

extension View {
  /// 添加陀螺仪 3D 运动效果
  /// - Parameters:
  ///   - isEnabled: 是否启用效果
  ///   - intensity: 效果强度，范围 0.0-1.0
  /// - Returns: 应用了运动效果的视图
  func motionEffect(isEnabled: Bool = true, intensity: Double = 1.0) -> some View {
    self.modifier(MotionEffectModifier(isEnabled: isEnabled, intensity: intensity))
  }
}