import Foundation
import SwiftUI

/// 卡片背景管理器
///
/// 管理卡片背景样式、显示名称和暗色模式判断，使用单例模式和缓存机制优化性能。
class CardCoverHelper {
  /// 单例实例
  static let shared = CardCoverHelper()

  /// 背景类型缓存
  private var coverTypeCache: [String: CardCoverType] = [:]
  /// 暗色模式缓存
  private var isDarkCache: [CardCoverType: Bool] = [:]
  /// 显示名称缓存
  private var displayNameCache: [CardCoverType: String] = [:]

  // MARK: - 类型定义
  
  /// 预设背景配置
  struct PresetCoverConfig {
    /// 背景类型
    let type: CardCoverType
    /// 显示标题
    let title: String
    /// 图片资源名称
    let imageName: String
    /// 是否为暗色背景
    let isDark: Bool
  }

  // MARK: - 预设数据
  
  /// 预设背景列表
  static let presetCovers: [PresetCoverConfig] = [
    PresetCoverConfig(type: .card1, title: "经典蓝色", imageName: "Card_CS_1", isDark: true),
    PresetCoverConfig(type: .card2, title: "深邃黑色", imageName: "Card_CS_2", isDark: true),
    PresetCoverConfig(type: .card3, title: "优雅灰色", imageName: "Card_CS_3", isDark: false),
    PresetCoverConfig(type: .card4, title: "纯净白色", imageName: "Card_CS_4", isDark: false),
    PresetCoverConfig(type: .card5, title: "尊享黑金", imageName: "Card_CS_5", isDark: true),
    PresetCoverConfig(type: .card6, title: "清新绿粉", imageName: "Card_CS_6", isDark: true),
    PresetCoverConfig(type: .card7, title: "活力黄黑", imageName: "Card_CS_7", isDark: true),
    PresetCoverConfig(type: .card8, title: "深邃灰蓝", imageName: "Card_CS_8", isDark: true),
    PresetCoverConfig(type: .card9, title: "青春蓝色", imageName: "Card_CS_9", isDark: false),
    PresetCoverConfig(type: .card10, title: "梦幻绚烂", imageName: "Card_CS_10", isDark: false),
    PresetCoverConfig(type: .card11, title: "自然绿色", imageName: "Card_CS_11", isDark: true),
  ]

  // MARK: - 初始化
  
  /// 私有初始化器
  private init() {
    // 预填充缓存
    preloadCache()
  }

  /// 预加载缓存以提高查询性能
  private func preloadCache() {
    for preset in CardCoverHelper.presetCovers {
      isDarkCache[preset.type] = preset.isDark
      displayNameCache[preset.type] = preset.title
      coverTypeCache[preset.type.rawValue] = preset.type
    }
  }

  // MARK: - 公开方法
  
  /// 获取背景类型对应的背景图片名称
  /// - Parameter type: 背景类型
  /// - Returns: 背景图片名称
  func getCoverImageName(for type: CardCoverType) -> String {
    return type.rawValue
  }

  /// 获取背景类型对应的显示名称
  /// - Parameter type: 背景类型
  /// - Returns: 背景显示名称，默认为"经典蓝色"
  func getCoverDisplayName(for type: CardCoverType) -> String {
    if let cachedName = displayNameCache[type] {
      return cachedName
    }

    // 缓存未命中时的回退逻辑
    if let preset = CardCoverHelper.presetCovers.first(where: { $0.type == type }) {
      displayNameCache[type] = preset.title
      return preset.title
    }

    let defaultName = "经典蓝色"
    displayNameCache[type] = defaultName
    return defaultName
  }

  /// 获取背景是否为暗色
  /// - Parameter type: 背景类型
  /// - Returns: 是否为暗色背景
  func isCoverDark(for type: CardCoverType) -> Bool {
    if let cachedValue = isDarkCache[type] {
      return cachedValue
    }

    // 缓存未命中时的回退逻辑
    if let preset = CardCoverHelper.presetCovers.first(where: { $0.type == type }) {
      isDarkCache[type] = preset.isDark
      return preset.isDark
    }

    isDarkCache[type] = false
    return false
  }

  /// 从原始字符串获取背景类型
  /// - Parameter rawValue: 原始字符串值
  /// - Returns: 背景类型，若无法解析则返回 .card1
  func getCoverType(from rawValue: String) -> CardCoverType {
    if let cachedType = coverTypeCache[rawValue] {
      return cachedType
    }

    let type = CardCoverType(rawValue: rawValue) ?? .card1
    coverTypeCache[rawValue] = type
    return type
  }
}
