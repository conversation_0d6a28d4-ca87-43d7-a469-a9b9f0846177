//
//  PermissionManagerHelper.swift
//  CStory
//
//  Created by NZUE on 2025/7/12.
//

import AVFoundation
import Foundation
import Network
import Photos
import Speech
import UIKit

/// 系统权限管理器
class PermissionManagerHelper: ObservableObject {
  /// 麦克风权限状态
  @Published var microphonePermission: AVAuthorizationStatus = .notDetermined
  /// 语音识别权限状态
  @Published var speechRecognitionPermission: SFSpeechRecognizerAuthorizationStatus = .notDetermined
  /// 相机权限状态
  @Published var cameraPermission: AVAuthorizationStatus = .notDetermined
  /// 相册权限状态
  @Published var photoLibraryPermission: PHAuthorizationStatus = .notDetermined
  /// 网络连接状态
  @Published var networkAvailable: Bool = true

  private let networkMonitor = NWPathMonitor()
  private let networkQueue = DispatchQueue(label: "NetworkMonitor")

  init() {
    checkCurrentPermissions()
    startNetworkMonitoring()
  }

  // MARK: - 权限检查

  private func checkCurrentPermissions() {
    microphonePermission = AVCaptureDevice.authorizationStatus(for: .audio)
    speechRecognitionPermission = SFSpeechRecognizer.authorizationStatus()
    cameraPermission = AVCaptureDevice.authorizationStatus(for: .video)
    photoLibraryPermission = PHPhotoLibrary.authorizationStatus()
  }

  // MARK: - 网络监控

  private func startNetworkMonitoring() {
    networkMonitor.pathUpdateHandler = { [weak self] path in
      DispatchQueue.main.async {
        self?.networkAvailable = path.status == .satisfied
      }
    }
    networkMonitor.start(queue: networkQueue)
  }

  // MARK: - 权限请求

  /// 请求所有权限
  func requestAllPermissions() async {
    await requestMicrophonePermission()
    await requestSpeechRecognitionPermission()
    await requestCameraPermission()
    await requestPhotoLibraryPermission()
  }

  /// 请求麦克风权限
  @MainActor
  func requestMicrophonePermission() async {
    guard microphonePermission == .notDetermined else { return }

    let status = await AVCaptureDevice.requestAccess(for: .audio)
    microphonePermission = status ? .authorized : .denied
  }

  /// 请求语音识别权限
  @MainActor
  func requestSpeechRecognitionPermission() async {
    guard speechRecognitionPermission == .notDetermined else { return }

    await withCheckedContinuation { continuation in
      SFSpeechRecognizer.requestAuthorization { status in
        DispatchQueue.main.async {
          self.speechRecognitionPermission = status
          continuation.resume()
        }
      }
    }
  }

  /// 请求相机权限
  @MainActor
  func requestCameraPermission() async {
    guard cameraPermission == .notDetermined else { return }

    let status = await AVCaptureDevice.requestAccess(for: .video)
    cameraPermission = status ? .authorized : .denied
  }

  /// 请求相册权限
  @MainActor
  func requestPhotoLibraryPermission() async {
    guard photoLibraryPermission == .notDetermined else { return }

    let status = await PHPhotoLibrary.requestAuthorization(for: .readWrite)
    photoLibraryPermission = status
  }

  // MARK: - 权限状态

  /// 是否所有权限都已授权
  var allPermissionsGranted: Bool {
    return microphonePermission == .authorized && speechRecognitionPermission == .authorized
      && cameraPermission == .authorized
      && (photoLibraryPermission == .authorized || photoLibraryPermission == .limited)
      && networkAvailable
  }

  /// 是否具备基础权限
  var hasEssentialPermissions: Bool {
    return (microphonePermission == .authorized || microphonePermission == .notDetermined)
      && (speechRecognitionPermission == .authorized
        || speechRecognitionPermission == .notDetermined)
      && networkAvailable
  }

  // MARK: - 辅助方法

  /// 获取权限描述
  func permissionDescription(for permission: String) -> String {
    switch permission {
    case "microphone":
      return "需要麦克风权限来录制语音输入"
    case "speech":
      return "需要语音识别权限来转换语音为文字"
    case "camera":
      return "需要相机权限来拍摄账单和收据"
    case "photos":
      return "需要相册权限来选择已有的交易凭证"
    case "network":
      return "需要网络连接来使用AI服务"
    default:
      return "需要此权限来正常使用功能"
    }
  }

  /// 打开系统设置
  func openSettings() {
    guard let settingsUrl = URL(string: UIApplication.openSettingsURLString) else { return }

    if UIApplication.shared.canOpenURL(settingsUrl) {
      UIApplication.shared.open(settingsUrl)
    }
  }

  deinit {
    networkMonitor.cancel()
  }
}
