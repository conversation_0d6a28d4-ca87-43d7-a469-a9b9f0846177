import Foundation

/// 交易默认设置管理器
///
/// 管理用户的默认记账偏好设置，包括记账方式、默认卡片等
final class TransactionDefaultsManager {

  // MARK: - Singleton

  static let shared = TransactionDefaultsManager()
  private init() {}

  // MARK: - Keys

  private enum Keys {
    static let defaultRecordingMode = "defaultRecordingMode"
    static let defaultExpenseCardId = "defaultExpenseCardId"
    static let defaultIncomeCardId = "defaultIncomeCardId"
    static let lastUsedExpenseCardId = "lastUsedExpenseCardId"
    static let lastUsedIncomeCardId = "lastUsedIncomeCardId"
    static let useLastUsedCards = "useLastUsedCards"
    static let aiInputMode = "aiInputMode"
  }

  // MARK: - Recording Mode

  /// 记账方式枚举
  enum RecordingMode: String, CaseIterable {
    case ai = "ai"
    case manual = "manual"

    var displayName: String {
      switch self {
      case .ai: return "AI智能记账"
      case .manual: return "手动记账"
      }
    }

    var description: String {
      switch self {
      case .ai: return "使用AI自动识别交易信息"
      case .manual: return "手动输入交易详情"
      }
    }

    var iconName: String {
      switch self {
      case .ai: return "brain.head.profile"
      case .manual: return "hand.point.up"
      }
    }
  }

  /// AI记账默认输入方式枚举
  enum AIInputMode: String, CaseIterable {
    case voice = "voice"
    case text = "text"

    var displayName: String {
      switch self {
      case .voice: return "语音输入"
      case .text: return "文字输入"
      }
    }

    var description: String {
      switch self {
      case .voice: return "默认使用语音输入"
      case .text: return "默认使用文字输入"
      }
    }

    var iconName: String {
      switch self {
      case .voice: return "mic.fill"
      case .text: return "keyboard"
      }
    }
  }

  /// 默认记账方式
  var defaultRecordingMode: RecordingMode {
    get {
      let rawValue =
        UserDefaults.standard.string(forKey: Keys.defaultRecordingMode)
        ?? RecordingMode.manual.rawValue
      return RecordingMode(rawValue: rawValue) ?? .manual
    }
    set {
      UserDefaults.standard.set(newValue.rawValue, forKey: Keys.defaultRecordingMode)
    }
  }

  /// AI记账默认输入方式
  var aiInputMode: AIInputMode {
    get {
      let rawValue =
        UserDefaults.standard.string(forKey: Keys.aiInputMode)
        ?? AIInputMode.voice.rawValue
      return AIInputMode(rawValue: rawValue) ?? .voice
    }
    set {
      UserDefaults.standard.set(newValue.rawValue, forKey: Keys.aiInputMode)
    }
  }

  // MARK: - Default Cards

  /// 默认支出卡片ID
  var defaultExpenseCardId: UUID? {
    get {
      guard let string = UserDefaults.standard.string(forKey: Keys.defaultExpenseCardId) else {
        return nil
      }
      return UUID(uuidString: string)
    }
    set {
      UserDefaults.standard.set(newValue?.uuidString, forKey: Keys.defaultExpenseCardId)
    }
  }

  /// 默认收入卡片ID
  var defaultIncomeCardId: UUID? {
    get {
      guard let string = UserDefaults.standard.string(forKey: Keys.defaultIncomeCardId) else {
        return nil
      }
      return UUID(uuidString: string)
    }
    set {
      UserDefaults.standard.set(newValue?.uuidString, forKey: Keys.defaultIncomeCardId)
    }
  }

  /// 是否使用上次使用的卡片
  var useLastUsedCards: Bool {
    get {
      UserDefaults.standard.bool(forKey: Keys.useLastUsedCards)
    }
    set {
      UserDefaults.standard.set(newValue, forKey: Keys.useLastUsedCards)
    }
  }

  // MARK: - Last Used Cards

  /// 上次使用的支出卡片ID
  var lastUsedExpenseCardId: UUID? {
    get {
      guard let string = UserDefaults.standard.string(forKey: Keys.lastUsedExpenseCardId) else {
        return nil
      }
      return UUID(uuidString: string)
    }
    set {
      UserDefaults.standard.set(newValue?.uuidString, forKey: Keys.lastUsedExpenseCardId)
    }
  }

  /// 上次使用的收入卡片ID
  var lastUsedIncomeCardId: UUID? {
    get {
      guard let string = UserDefaults.standard.string(forKey: Keys.lastUsedIncomeCardId) else {
        return nil
      }
      return UUID(uuidString: string)
    }
    set {
      UserDefaults.standard.set(newValue?.uuidString, forKey: Keys.lastUsedIncomeCardId)
    }
  }

  // MARK: - Helper Methods

  /// 获取推荐的支出卡片ID（仅用于手动记账）
  ///
  /// 选择优先级：
  /// 1. 如果设置了默认支出卡片且该卡片可用 → 使用默认卡片
  /// 2. 如果没有设置默认卡片 → 自动选择第一张可用的储蓄卡（非信用卡）
  ///
  /// - Parameter availableCards: 可用的卡片列表
  /// - Returns: 推荐的卡片ID，如果没有可用卡片则返回nil
  func getRecommendedExpenseCardId(from availableCards: [CardModel]) -> UUID? {
    // AI记账模式不使用默认卡片，直接选择第一张储蓄卡
    if defaultRecordingMode == .ai {
      return availableCards.first(where: { !$0.isCredit })?.id
    }

    // 手动记账模式：优先使用设置的默认卡片
    if let defaultId = defaultExpenseCardId {
      if availableCards.contains(where: { $0.id == defaultId }) {
        return defaultId
      }
    }

    // 如果没有设置默认卡片或默认卡片不可用，选择第一张储蓄卡
    return availableCards.first(where: { !$0.isCredit })?.id
  }

  /// 获取推荐的收入卡片ID（仅用于手动记账）
  ///
  /// 选择优先级：
  /// 1. 如果设置了默认收入卡片且该卡片可用 → 使用默认卡片
  /// 2. 如果没有设置默认卡片 → 自动选择第一张可用的储蓄卡（非信用卡）
  ///
  /// - Parameter availableCards: 可用的卡片列表
  /// - Returns: 推荐的卡片ID，如果没有可用卡片则返回nil
  func getRecommendedIncomeCardId(from availableCards: [CardModel]) -> UUID? {
    // AI记账模式不使用默认卡片，直接选择第一张储蓄卡
    if defaultRecordingMode == .ai {
      return availableCards.first(where: { !$0.isCredit })?.id
    }

    // 手动记账模式：优先使用设置的默认卡片
    if let defaultId = defaultIncomeCardId {
      if availableCards.contains(where: { $0.id == defaultId }) {
        return defaultId
      }
    }

    // 如果没有设置默认卡片或默认卡片不可用，选择第一张储蓄卡
    return availableCards.first(where: { !$0.isCredit })?.id
  }

  /// 更新上次使用的卡片（仅用于手动记账）
  /// - Parameters:
  ///   - cardId: 卡片ID
  ///   - transactionType: 交易类型
  func updateLastUsedCard(cardId: UUID, for transactionType: TransactionType) {
    // AI记账模式不保存上次使用的卡片
    if defaultRecordingMode == .ai {
      return
    }

    // 手动记账模式才保存上次使用的卡片
    switch transactionType {
    case .expense:
      lastUsedExpenseCardId = cardId
    case .income:
      lastUsedIncomeCardId = cardId
    case .transfer:
      // 转账不更新上次使用的卡片
      break
    case .refund:
      // 退款使用收入逻辑
      lastUsedIncomeCardId = cardId
    case .createCard:
      // 创建卡片不更新上次使用的卡片
      break
    case .adjustCard:
      // 调整卡片不更新上次使用的卡片
      break
    }
  }

}
