import Foundation

extension Bundle {
  /// 获取应用版本号（CFBundleShortVersionString）
  var appVersion: String {
    return infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
  }

  /// 获取构建版本号（CFBundleVersion）
  var buildVersion: String {
    return infoDictionary?["CFBundleVersion"] as? String ?? "1"
  }

  /// 获取完整版本信息
  var fullVersion: String {
    return "\(appVersion) (Build \(buildVersion))"
  }
}
