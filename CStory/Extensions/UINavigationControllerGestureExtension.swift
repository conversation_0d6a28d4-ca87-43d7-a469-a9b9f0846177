//
//  UINavigationControllerGestureExtension.swift
//  CStory
//
//  Created by NZUE on 2025/7/12.
//

#if os(iOS)
  import UIKit

  /// 为导航控制器添加返回手势支持
  extension UINavigationController: @retroactive UIGestureRecognizerDelegate {
    /// 配置手势识别器
    override open func viewDidLoad() {
      super.viewDidLoad()
      interactivePopGestureRecognizer?.delegate = self
    }

    /// 控制何时启用滑动返回手势
    /// - Parameter gestureRecognizer: 手势识别器
    /// - Returns: 当导航栈中有多个视图控制器时返回 true
    public func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
      return viewControllers.count > 1
    }
  }
#endif
