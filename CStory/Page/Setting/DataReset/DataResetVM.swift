//
//  DataResetVM.swift
//  CStory
//
//  Created by Augment Agent on 2025-08-11.
//

import Foundation
import SwiftData
import SwiftUI

/// 数据重置页面的ViewModel
///
/// 负责处理数据重置的业务逻辑，包括数据清除、重新初始化等操作。
@MainActor
@Observable
final class DataResetVM {

  // MARK: - Dependencies

  private let modelContext: ModelContext
  private let dataManager: DataManagement

  // MARK: - State Properties

  /// 是否显示确认重置弹窗
  var showingResetConfirmation = false

  /// 是否正在执行重置操作
  var isResetting = false

  /// 重置是否已完成
  var resetCompleted = false

  /// 错误信息
  var errorMessage: String?

  /// 是否显示错误弹窗
  var showingError = false

  // MARK: - Initialization

  init(modelContext: ModelContext, dataManager: DataManagement) {
    self.modelContext = modelContext
    self.dataManager = dataManager
  }

  // MARK: - Public Methods

  /// 显示重置确认弹窗
  func showResetConfirmation() {
    showingResetConfirmation = true
  }

  /// 执行重置操作
  func performReset() {
    isResetting = true
    errorMessage = nil

    // 在后台线程执行数据清除操作
    Task {
      do {
        // 执行真实的数据清除
        try await clearAllDataAndReinitialize()

        // 回到主线程更新UI
        await MainActor.run {
          isResetting = false
          resetCompleted = true

          // 触发成功反馈
          dataManager.hapticManager.trigger(.success)
        }
      } catch {
        // 处理错误
        await MainActor.run {
          isResetting = false
          errorMessage = error.localizedDescription
          showingError = true

          print("❌ 数据重置失败: \(error)")

          // 触发错误反馈
          dataManager.hapticManager.trigger(.error)
        }
      }
    }
  }

  /// 重置状态
  func resetState() {
    showingResetConfirmation = false
    isResetting = false
    resetCompleted = false
    errorMessage = nil
    showingError = false
  }
}

// MARK: - Private Methods

extension DataResetVM {

  /// 清除所有数据并重新初始化
  fileprivate func clearAllDataAndReinitialize() async throws {
    print("🗑️ 开始清除所有数据...")

    // 1. 清除所有交易记录
    let allTransactions = try modelContext.fetch(FetchDescriptor<TransactionModel>())
    print("清除 \(allTransactions.count) 条交易记录...")
    for transaction in allTransactions {
      modelContext.delete(transaction)
    }

    // 2. 清除所有卡片
    let allCards = try modelContext.fetch(FetchDescriptor<CardModel>())
    print("清除 \(allCards.count) 张卡片...")
    for card in allCards {
      modelContext.delete(card)
    }

    // 3. 清除所有分类（先清除子分类，再清除主分类）
    let allSubCategories = try modelContext.fetch(FetchDescriptor<TransactionSubCategoryModel>())
    print("清除 \(allSubCategories.count) 个子分类...")
    for subCategory in allSubCategories {
      modelContext.delete(subCategory)
    }

    let allMainCategories = try modelContext.fetch(FetchDescriptor<TransactionMainCategoryModel>())
    print("清除 \(allMainCategories.count) 个主分类...")
    for mainCategory in allMainCategories {
      modelContext.delete(mainCategory)
    }

    // 4. 清除所有货币
    let allCurrencies = try modelContext.fetch(FetchDescriptor<CurrencyModel>())
    print("清除 \(allCurrencies.count) 种货币...")
    for currency in allCurrencies {
      modelContext.delete(currency)
    }

    // 5. 清除所有聊天消息
    let allChatMessages = try modelContext.fetch(FetchDescriptor<ChatMessageModel>())
    print("清除 \(allChatMessages.count) 条聊天消息...")
    for chatMessage in allChatMessages {
      modelContext.delete(chatMessage)
    }

    // 6. 清除UserDefaults中的设置
    print("清除应用设置...")
    clearUserDefaults()

    // 7. 清除iCloud同步标记
    print("清除iCloud同步标记...")
    clearCloudSyncFlags()

    // 8. 保存更改
    print("保存数据库更改...")
    try modelContext.save()

    // 9. 重新初始化基础数据
    print("重新初始化基础数据...")
    try await reinitializeBaseData()

    print("✅ 数据重置和重新初始化完成")
  }

  /// 清除UserDefaults中的应用设置
  fileprivate func clearUserDefaults() {
    let defaults = UserDefaults.standard

    // 清除初始化标记
    defaults.removeObject(forKey: "com.cstory.localDataInitialized")

    // 清除基础货币设置
    defaults.removeObject(forKey: "baseCurrencyCode")

    // 清除记账设置
    defaults.removeObject(forKey: "defaultTransactionMode")
    defaults.removeObject(forKey: "defaultAIInputMode")
    defaults.removeObject(forKey: "defaultExpenseCardId")
    defaults.removeObject(forKey: "defaultIncomeCardId")

    // 清除其他可能的设置
    defaults.removeObject(forKey: "iCloudSyncEnabled")

    // 同步更改
    defaults.synchronize()
  }

  /// 清除iCloud同步标记
  fileprivate func clearCloudSyncFlags() {
    let cloudStore = NSUbiquitousKeyValueStore.default

    // 清除云端初始化标记
    cloudStore.removeObject(forKey: "com.cstory.cloudDataInitialized")

    // 同步到iCloud
    cloudStore.synchronize()
  }

  /// 重新初始化基础数据
  fileprivate func reinitializeBaseData() async throws {
    print("🔄 开始重新初始化基础数据...")

    // 重新初始化交易类别
    try await initializeTransactionCategories()

    // 重新初始化货币数据
    try await initializeCurrencies()

    // 重新设置初始化标记
    UserDefaults.standard.set(true, forKey: "com.cstory.localDataInitialized")
    NSUbiquitousKeyValueStore.default.set(true, forKey: "com.cstory.cloudDataInitialized")
    NSUbiquitousKeyValueStore.default.synchronize()

    print("✅ 基础数据重新初始化完成")
  }
}

// MARK: - Data Initialization

extension DataResetVM {

  /// 重新初始化交易类别数据
  fileprivate func initializeTransactionCategories() async throws {
    print("🚀 重新初始化交易类别数据...")

    // 从本地JSON加载默认类别数据
    let mainCategories = MainCategoryJSONDecoder.decode(from: "TransactionCategoryDefaults")
    guard !mainCategories.isEmpty else {
      print("❌ 本地JSON文件为空或解析失败，无法创建类别")
      throw DataResetError.categoryInitializationFailed
    }

    print("📊 从JSON加载了\(mainCategories.count)个主分类，开始创建类别...")

    // 创建主类别和子类别
    var totalSubCategories = 0
    for item in mainCategories {
      // 创建主类别
      let mainCategory = TransactionMainCategoryModel(
        id: item.categoryId,
        name: item.name,
        icon: item.iconType,
        order: item.order,
        type: item.transactionType
      )
      modelContext.insert(mainCategory)

      // 创建子类别
      if let subCategories = item.subCategories {
        totalSubCategories += subCategories.count

        subCategories.forEach { subItem in
          let subCategory = TransactionSubCategoryModel(
            id: subItem.categoryId,
            name: subItem.name,
            icon: subItem.iconType,
            order: subItem.order,
            mainId: mainCategory.id
          )
          modelContext.insert(subCategory)
          subCategory.mainCategory = mainCategory
        }
      }
    }

    // 保存类别数据
    try modelContext.save()

    print("✅ 交易类别重新初始化完成:")
    print("   📁 主分类: \(mainCategories.count) 个")
    print("   📄 子分类: \(totalSubCategories) 个")
  }

  /// 重新初始化货币数据
  fileprivate func initializeCurrencies() async throws {
    print("🚀 重新初始化货币数据...")

    // 获取货币名称和符号映射
    let (currencyNames, currencySymbols) = CurrencyService.getCurrencyMappings()

    // 从本地JSON文件读取数据
    guard let fileURL = Bundle.main.url(forResource: "exchange_rates", withExtension: "json") else {
      print("❌ 无法找到exchange_rates.json文件")
      throw DataResetError.currencyInitializationFailed
    }

    // 读取并解析JSON数据
    let jsonData = try Data(contentsOf: fileURL)
    let decoder = JSONDecoder()
    let exchangeRates = try decoder.decode(ExchangeRateAPIResponse.self, from: jsonData)

    print("成功从本地JSON获取到\(exchangeRates.data.rates.count)种货币数据")

    // 货币排序计数器
    var orderCounter = 0

    // 优先添加的常用货币代码列表
    let priorityCurrencies = CurrencyService.getPriorityCurrencies()

    // 创建一个处理过的货币代码集合
    var processedCurrencies = Set<String>()

    // 先添加人民币作为基准货币
    let cny = CurrencyModel(
      name: "人民币",
      code: "CNY",
      symbol: "¥",
      rate: 1.0,
      defaultRate: 1.0,
      isBaseCurrency: true,
      isCustom: false,
      isSelected: true,
      order: orderCounter
    )
    orderCounter += 1
    modelContext.insert(cny)
    processedCurrencies.insert("CNY")

    // 设置人民币为默认本位币
    UserDefaults.standard.set("CNY", forKey: "baseCurrencyCode")

    // 按优先顺序添加其他常用货币
    for code in priorityCurrencies where code != "CNY" {
      if let apiRate = exchangeRates.data.rates[code] {
        let name = currencyNames[code] ?? code
        let symbol = currencySymbols[code] ?? code

        // 计算反向汇率
        var inverseRate = 1.0
        if apiRate > 0 {
          inverseRate = 1.0 / apiRate
          inverseRate = (inverseRate * 1_000_000).rounded() / 1_000_000
        }

        let currency = CurrencyModel(
          name: name,
          code: code,
          symbol: symbol,
          rate: inverseRate,
          defaultRate: inverseRate,
          isBaseCurrency: false,
          isCustom: false,
          isSelected: true,
          order: orderCounter
        )
        orderCounter += 1
        modelContext.insert(currency)
        processedCurrencies.insert(code)
      }
    }

    // 然后添加所有其他货币（按字母顺序）- 与第一次启动保持一致
    let sortedRemainingCurrencies = exchangeRates.data.rates.keys.sorted().filter {
      !processedCurrencies.contains($0)
    }

    print("添加剩余的 \(sortedRemainingCurrencies.count) 种货币...")

    for code in sortedRemainingCurrencies {
      if let apiRate = exchangeRates.data.rates[code] {
        let name = currencyNames[code] ?? code
        let symbol = currencySymbols[code] ?? code

        // 将API返回的汇率转换为反向汇率
        var inverseRate = 1.0
        if apiRate > 0 {
          inverseRate = 1.0 / apiRate
          // 确保保留6位小数精度
          inverseRate = (inverseRate * 1_000_000).rounded() / 1_000_000
        }

        let currency = CurrencyModel(
          name: name,
          code: code,
          symbol: symbol,
          rate: inverseRate,  // 存储"1外币=多少CNY"的反向汇率
          defaultRate: inverseRate,  // 设置默认汇率
          isBaseCurrency: false,
          isCustom: false,
          isSelected: true,  // 默认所有货币都是选择状态
          order: orderCounter
        )
        orderCounter += 1
        modelContext.insert(currency)
      }
    }

    // 保存货币数据
    try modelContext.save()

    print("✅ 货币数据重新初始化完成:")
    print("   💰 货币总数: \(orderCounter) 种")
    print("   🎯 已设置人民币(CNY)为默认本位币")
  }
}

/// 数据重置错误类型
enum DataResetError: Error {
  case categoryInitializationFailed
  case currencyInitializationFailed

  var localizedDescription: String {
    switch self {
    case .categoryInitializationFailed:
      return "交易类别初始化失败"
    case .currencyInitializationFailed:
      return "货币数据初始化失败"
    }
  }
}
