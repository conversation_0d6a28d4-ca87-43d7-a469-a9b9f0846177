//
//  SettingViewVM.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/25.
//

import Foundation
import SafariServices
import SwiftUI

/// 设置页面的数据模型
///
/// 定义设置页面中各个设置项的数据结构，包括图标、标题、操作等信息。
/// 支持不同类型的设置项：导航、外部链接等。
struct SettingItem {
  /// 设置项的唯一标识

  let id = UUID()
  /// 图标名称
  let icon: String
  /// 标题文本
  let title: String
  /// 点击操作类型
  let actionType: SettingActionType

  init(icon: String, title: String, actionType: SettingActionType) {
    self.icon = icon
    self.title = title
    self.actionType = actionType
  }
}

/// 设置项操作类型
///
/// 定义设置项点击后的行为类型，支持页面导航、外部链接等。
enum SettingActionType {
  /// 导航到应用内页面
  case navigation(NavigationDestination)
  /// 打开外部链接
  case externalLink(String)
  /// 自定义操作
  case custom(() -> Void)
  /// 暂未实现的功能
  case notImplemented
}

/// 设置分组
///
/// 将相关的设置项组织在一起，便于在UI中分组显示。
struct SettingSection {
  /// 分组标题
  let title: String
  /// 分组中的设置项
  let items: [SettingItem]
}

/// 设置页面视图模型
///
/// 管理设置页面的业务逻辑和状态，遵循MVVM架构模式。
/// 负责设置项数据的组织、隐藏功能的处理、外部链接的打开等。
///
/// ## 主要功能
/// - 设置项数据管理和组织
/// - 隐藏测试功能的点击序列检测
/// - 外部链接的安全打开
/// - 导航操作的统一处理
///
/// ## 使用示例
/// ```swift
/// @StateObject private var viewModel = SettingViewVM()
///
/// // 在View中使用
/// ForEach(viewModel.settingSections, id: \.title) { section in
///   // 渲染设置分组
/// }
/// ```
///
/// - Author: 咩咩
/// - Since: 2025.7.25
/// - Note: 该类管理所有设置相关的业务逻辑，保持View的简洁性
@MainActor
class SettingVM: ObservableObject {

  // MARK: - Published Properties

  /// 是否显示测试视图
  ///
  /// 控制隐藏测试功能的显示状态。
  /// 通过特定的点击序列（左右左右）可以激活测试功能。
  @Published var showTestView = false

  /// 点击序列记录
  ///
  /// 记录用户的点击序列，用于检测隐藏功能的激活。
  /// 序列长度限制为4，超过时会自动移除最早的记录。
  @Published private var tapSequence: [String] = []

  /// 上次点击时间
  ///
  /// 记录最后一次点击的时间，用于重置点击序列。
  /// 如果两次点击间隔超过3秒，会自动重置序列。
  @Published private var lastTapTime = Date()

  // MARK: - Dependencies

  /// 数据管理器，提供统一的数据访问接口
  private let dataManager: DataManagement

  // MARK: - Initialization

  /// 初始化设置视图模型
  ///
  /// - Parameter dataManager: 数据管理器
  init(dataManager: DataManagement) {
    self.dataManager = dataManager
  }

  // MARK: - Computed Properties

  /// 设置分组数据
  ///
  /// 返回所有设置分组的数据，包括记账设置、数据安全、关于等分组。
  /// 每个分组包含相关的设置项，便于在UI中统一渲染。
  var settingSections: [SettingSection] {
    return [
      // 记账设置分组
      SettingSection(
        title: "记账设置",
        items: [
          SettingItem(
            icon: "edit_icon",
            title: "记账偏好",
            actionType: .navigation(.transactionSettingsView)
          ),
          SettingItem(
            icon: "category_icon",
            title: "分类管理",
            actionType: .navigation(.t_CategoryView)
          ),
          SettingItem(
            icon: "currency_icon",
            title: "货币汇率",
            actionType: .navigation(.currencyRateView)
          ),
        ]
      ),

      // 数据安全分组
      SettingSection(
        title: "数据安全",
        items: [
          SettingItem(
            icon: "cloud_icon",
            title: "iCloud同步",
            actionType: .navigation(.iCloudSyncView)
          ),
          SettingItem(
            icon: "clear_icon",
            title: "重新来过",
            actionType: .navigation(.dataResetView)
          ),
        ]
      ),

      // 关于分组
      SettingSection(
        title: "关于",
        items: [
          SettingItem(
            icon: "category_icon",
            title: "隐私协议",
            actionType: .externalLink("https://cstory.nzue.cc/docs/privacy-policy")
          ),
          SettingItem(
            icon: "currency_icon",
            title: "用户协议",
            actionType: .externalLink("https://cstory.nzue.cc/docs/user-agreement")
          ),
        ]
      ),
    ]
  }

  /// 应用版本信息
  ///
  /// 返回格式化的应用版本信息，用于在设置页面底部显示。
  var appVersionText: String {
    return "CStory记账 \(Bundle.main.appVersion)"
  }

  // MARK: - Public Methods

  /// 处理设置项点击
  ///
  /// 根据设置项的操作类型执行相应的操作，包括导航、打开链接等。
  /// 统一处理所有设置项的点击逻辑，保持代码的一致性。
  ///
  /// - Parameters:
  ///   - item: 被点击的设置项
  ///   - pathManager: 导航管理器
  ///   - dismiss: 关闭当前页面的回调
  func handleSettingItemTap(
    _ item: SettingItem,
    pathManager: PathManagerHelper,
    dismiss: @escaping () -> Void
  ) {
    // 触发触觉反馈
    dataManager.hapticManager.trigger(.impactLight)

    switch item.actionType {
    case .navigation(let destination):
      pathManager.path.append(destination)
      dismiss()

    case .externalLink(let urlString):
      dismiss()
      openExternalLink(urlString)

    case .custom(let action):
      action()
      dismiss()

    case .notImplemented:
      dismiss()
    // 可以在这里添加"功能开发中"的提示
    }
  }

  /// 处理隐藏功能的点击序列
  ///
  /// 检测特定的点击序列（左右左右）来激活隐藏的测试功能。
  /// 序列检测有时间限制，超过3秒会自动重置。
  ///
  /// - Parameter direction: 点击方向（"left" 或 "right"）
  func handleSecretTap(_ direction: String) {
    let now = Date()

    // 如果距离上次点击超过3秒，重置序列
    if now.timeIntervalSince(lastTapTime) > 3.0 {
      tapSequence.removeAll()
    }

    lastTapTime = now
    tapSequence.append(direction)

    // 保持序列长度不超过4
    if tapSequence.count > 4 {
      tapSequence.removeFirst()
    }

    // 检查是否匹配左右左右序列
    if tapSequence == ["left", "right", "left", "right"] {
      dataManager.hapticManager.trigger(.impactMedium)
      showTestView = true
      tapSequence.removeAll()  // 重置序列
    }
  }

  // MARK: - Private Methods

  /// 打开外部链接
  ///
  /// 使用 SFSafariViewController 安全地打开外部链接。
  /// 添加延迟以确保页面关闭动画完成后再打开链接。
  ///
  /// - Parameter urlString: 要打开的URL字符串
  private func openExternalLink(_ urlString: String) {
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
      guard let url = URL(string: urlString) else { return }
      let safariViewController = SFSafariViewController(url: url)
      UIApplication.shared.firstKeyWindow?.rootViewController?.present(
        safariViewController, animated: true
      )
    }
  }
}

// MARK: - UIApplication Extension

extension UIApplication {
  /// 获取应用程序的第一个键窗口
  ///
  /// 用于在设置页面中打开外部链接时获取当前的根视图控制器。
  /// 支持多场景应用，优先选择前台活跃的窗口场景。
  var firstKeyWindow: UIWindow? {
    UIApplication.shared.connectedScenes
      .compactMap { $0 as? UIWindowScene }
      .filter { $0.activationState == .foregroundActive }
      .first?.keyWindow
  }
}
