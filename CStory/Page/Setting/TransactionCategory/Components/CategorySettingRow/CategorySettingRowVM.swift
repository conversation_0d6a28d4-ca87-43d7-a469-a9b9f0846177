//
//  CategorySettingRowVM.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/24.
//

import Combine
import SwiftUI

/// 类别设置行视图模型
///
/// 管理类别行组件的业务逻辑和数据处理，遵循MVVM架构模式。
/// 专用于设置页面的类别管理，负责类别数据的格式化、状态计算和展示逻辑。
///
/// 该类将TransactionMainCategoryModel数据转换为视图层可直接使用的格式，
/// 包括图标显示、文本格式化、状态管理等复杂业务逻辑。
///
/// ## 主要功能
/// - 类别数据格式化和计算
/// - 展开/收起状态管理
/// - 子类别数量文本格式化
/// - 交互事件处理
///
/// ## 使用示例
/// ```swift
/// // 从TransactionMainCategoryModel创建
/// let viewModel = CategorySettingRowVM(
///   from: categoryModel,
///   isExpanded: false
/// )
///
/// // 直接创建
/// let viewModel = CategorySettingRowVM(
///   icon: .emoji("🛍️"),
///   categoryName: "购物",
///   subCategoryCount: 8,
///   hasSubCategories: true,
///   isExpanded: false
/// )
/// ```
///
/// - Author: 咩咩
/// - Since: 2025.7.24
/// - Note: 该类支持动态数据更新，视图会自动响应属性变化
/// - SeeAlso: `CategorySettingRow`, `TransactionMainCategoryModel`
class CategorySettingRowVM: ObservableObject {

  // MARK: - Published Properties

  /// 类别图标
  ///
  /// 显示的类别图标，支持emoji和图片两种类型。
  /// 可为空，表示使用默认图标。
  @Published var icon: IconType

  /// 类别名称
  ///
  /// 格式化后的类别显示名称。
  /// 如"购物"、"餐饮"、"交通"等。
  @Published var categoryName: String

  /// 子类别数量
  ///
  /// 该主类别下包含的子类别数量。
  /// 用于计算显示文本和判断是否有子类别。
  @Published var subCategoryCount: Int

  /// 是否有子类别
  ///
  /// 控制是否显示展开/收起箭头和相关交互。
  /// 当子类别数量大于0时为true。
  @Published var hasSubCategories: Bool

  /// 是否展开状态
  ///
  /// 控制展开/收起状态，影响箭头旋转和子类别显示。
  /// 当为true时，箭头向下旋转90度。
  @Published var isExpanded: Bool

  // MARK: - Computed Properties

  /// 子类别数量显示文本
  ///
  /// 根据子类别数量格式化的显示文本。
  /// 如"5个子类别"、"无子类别"等。
  var subCategoryCountText: String {
    if subCategoryCount > 0 {
      return "\(subCategoryCount)个子类别"
    } else {
      return "无子类别"
    }
  }

  // MARK: - Interaction

  /// 点击事件回调
  ///
  /// 当类别行被点击时执行的闭包，通常用于展开/收起或导航。
  var onTap: (() -> Void)?

  /// 更多操作回调
  ///
  /// 当更多操作按钮被点击时执行的闭包，通常用于显示操作菜单。
  var onMoreAction: (() -> Void)?

  // MARK: - Initialization

  init(
    icon: IconType,
    categoryName: String,
    subCategoryCount: Int,
    hasSubCategories: Bool,
    isExpanded: Bool,
    onTap: (() -> Void)? = nil,
    onMoreAction: (() -> Void)? = nil
  ) {
    self.icon = icon
    self.categoryName = categoryName
    self.subCategoryCount = subCategoryCount
    self.hasSubCategories = hasSubCategories
    self.isExpanded = isExpanded
    self.onTap = onTap
    self.onMoreAction = onMoreAction
  }

  // MARK: - 便利初始化方法

  /// 从TransactionMainCategoryModel创建CategorySettingRowVM
  convenience init(
    from category: TransactionMainCategoryModel,
    isExpanded: Bool = false,
    onTap: (() -> Void)? = nil,
    onMoreAction: (() -> Void)? = nil
  ) {
    let subCategoryCount = category.subCategories?.count ?? 0
    let hasSubCategories = subCategoryCount > 0

    self.init(
      icon: category.icon,
      categoryName: category.name,
      subCategoryCount: subCategoryCount,
      hasSubCategories: hasSubCategories,
      isExpanded: isExpanded,
      onTap: onTap,
      onMoreAction: onMoreAction
    )
  }

  // MARK: - Public Methods

  /// 切换展开/收起状态
  ///
  /// 切换当前的展开状态，用于响应用户点击。
  /// 该方法会触发UI更新，包括箭头旋转动画。
  func toggleExpanded() {
    withAnimation(.spring(response: 0.3)) {
      isExpanded.toggle()
    }
  }

  /// 设置展开状态
  ///
  /// 直接设置展开状态，用于外部控制展开/收起。
  ///
  /// - Parameter expanded: 目标展开状态
  func setExpanded(_ expanded: Bool) {
    withAnimation(.spring(response: 0.3)) {
      isExpanded = expanded
    }
  }

  /// 更新子类别数量
  ///
  /// 更新子类别数量并重新计算相关状态。
  /// 用于响应子类别的增删操作。
  ///
  /// - Parameter count: 新的子类别数量
  func updateSubCategoryCount(_ count: Int) {
    subCategoryCount = count
    hasSubCategories = count > 0

    // 如果没有子类别了，自动收起
    if !hasSubCategories {
      isExpanded = false
    }
  }

}
