//
//  CategorySortVM.swift
//  CStory
//
//  Created by NZUE on 2025/7/24.
//

import SwiftData
import SwiftUI

/// 类别排序模式
enum CategorySortMode {
  /// 主类别排序
  case mainCategory(type: TransactionType)
  /// 子类别排序
  case subCategory(mainCategoryId: String)
}

/// 类别排序视图模型
class CategorySortVM: ObservableObject {
  // MARK: - Properties

  /// 排序模式
  let mode: CategorySortMode
  /// 数据管理器
  let dataManager: DataManagement
  /// 模型上下文
  let modelContext: ModelContext

  /// 主类别列表（用于主类别排序）
  @Published var mainCategories: [TransactionMainCategoryModel] = []
  /// 子类别列表（用于子类别排序）
  @Published var subCategories: [TransactionSubCategoryModel] = []

  // MARK: - Computed Properties

  /// 页面标题
  var title: String {
    switch mode {
    case .mainCategory(let type):
      return "\(type == .expense ? "支出" : "收入")类别排序"
    case .subCategory(let mainCategoryId):
      let mainCategory = dataManager.mainCategories.first { $0.id == mainCategoryId }
      return "\(mainCategory?.name ?? "子")类别排序"
    }
  }

  /// 通用的类别列表（用于UI显示）
  var categories: [Any] {
    switch mode {
    case .mainCategory:
      return mainCategories
    case .subCategory:
      return subCategories
    }
  }

  // MARK: - Initialization

  init(mode: CategorySortMode, dataManager: DataManagement, modelContext: ModelContext) {
    self.mode = mode
    self.dataManager = dataManager
    self.modelContext = modelContext

    loadCategories()
  }

  // MARK: - Methods

  /// 加载类别数据
  private func loadCategories() {
    switch mode {
    case .mainCategory(let type):
      mainCategories = dataManager.mainCategories
        .filter { $0.type == type.rawValue }
        .sorted { $0.order < $1.order }

    case .subCategory(let mainCategoryId):
      guard let mainCategory = dataManager.mainCategories.first(where: { $0.id == mainCategoryId }),
            let subCategories = mainCategory.subCategories else {
        self.subCategories = []
        return
      }
      self.subCategories = Array(subCategories).sorted { $0.order < $1.order }
    }
  }

  /// 移动类别
  func moveCategory(from source: IndexSet, to destination: Int) {
    guard let sourceIndex = source.first else { return }

    switch mode {
    case .mainCategory:
      let movedCategory = mainCategories.remove(at: sourceIndex)
      let insertIndex = destination > sourceIndex ? destination - 1 : destination
      mainCategories.insert(movedCategory, at: insertIndex)

      // 更新order字段
      for (index, category) in mainCategories.enumerated() {
        category.order = index
      }

    case .subCategory:
      let movedCategory = subCategories.remove(at: sourceIndex)
      let insertIndex = destination > sourceIndex ? destination - 1 : destination
      subCategories.insert(movedCategory, at: insertIndex)

      // 更新order字段
      for (index, category) in subCategories.enumerated() {
        category.order = index
      }
    }

    // 保存更改
    saveChanges()
  }

  /// 保存更改
  private func saveChanges() {
    do {
      try modelContext.save()
    } catch {
      print("保存类别排序失败: \(error)")
    }
  }
}
