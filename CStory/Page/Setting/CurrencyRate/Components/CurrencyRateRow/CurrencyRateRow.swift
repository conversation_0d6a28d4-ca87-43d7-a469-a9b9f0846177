//
//  CurrencyRateRow.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/25.
//

import SwiftUI

/// 货币汇率行视图
///
/// 显示单个货币汇率信息的可交互行组件，遵循MVVM架构模式。
/// 专用于货币汇率设置页面，通过CurrencyRateRowVM处理所有数据格式化和业务逻辑。
///
/// 该组件支持显示货币名称、代码、符号、汇率等信息，
/// 并提供选择、自定义标识等交互功能。
///
/// ## 主要功能
/// - 货币基本信息显示（名称、代码、符号）
/// - 汇率值显示和格式化
/// - 选择状态切换
/// - 自定义汇率标识
/// - 点击交互支持
///
/// ## 使用示例
/// ```swift
/// let viewModel = CurrencyRateRowVM(from: currencyModel)
/// viewModel.onTap = { /* 处理点击 */ }
/// CurrencyRateRow(viewModel: viewModel)
/// ```
///
/// - Author: 咩咩
/// - Since: 2025.7.25
/// - Note: 该组件是无状态的，所有状态管理都在ViewModel中
struct CurrencyRateRow: View {

  // MARK: - Properties

  /// 货币行视图模型
  ///
  /// 管理货币显示数据和交互逻辑的视图模型实例。
  /// 包含货币名称、代码、符号、汇率、选择状态等所有显示相关数据。
  /// 视图通过观察此对象的变化来自动更新UI。
  @ObservedObject var viewModel: CurrencyRateRowVM

  // MARK: - 主体视图

  var body: some View {
    Button(action: {
      viewModel.onTap?()
    }) {
      HStack(alignment: .center, spacing: 16) {
        // MARK: 左侧内容 (勾选框)
        Image(systemName: viewModel.isSelected ? "checkmark.circle.fill" : "circle")
          .font(.system(size: 20, weight: .medium))
          .foregroundColor(.cAccentBlue)
          .frame(width: 40, height: 40)
          .onTapGesture {
            viewModel.toggleSelection()
          }

        // MARK: 中间内容 (货币信息)
        VStack(alignment: .leading, spacing: 4) {
          HStack(spacing: 4) {
            Text(viewModel.currencyName)
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(.cBlack)

            if viewModel.isCustom {
              Text("自定义")
                .font(.system(size: 10, weight: .regular))
                .foregroundColor(.cBlack.opacity(0.6))
                .padding(.horizontal, 4)
                .padding(.vertical, 2)
                .background(Color.cAccentBlue.opacity(0.08))
                .cornerRadius(4)
            }
          }

          Text(viewModel.currencyCodeAndSymbol)
            .font(.system(size: 12, weight: .regular))
            .foregroundColor(.cBlack.opacity(0.6))
        }

        Spacer()

        // MARK: 右侧内容 (汇率和箭头)
        HStack(spacing: 12) {
          Text(viewModel.formattedRate)
            .font(.system(size: 14, weight: .regular))
            .foregroundColor(.cBlack.opacity(0.6))

          Image(systemName: "chevron.forward")
            .font(.system(size: 12, weight: .medium))
            .foregroundColor(.cBlack.opacity(0.6))
        }
      }
      .padding(12)
      .background(Color.cWhite)
      .cornerRadius(16)
      .overlay(
        RoundedRectangle(cornerRadius: 16)
          .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
      )
    }
  }
}

// MARK: - Preview Provider

/// 货币行组件预览
///
/// 提供多种典型货币场景的预览，展示组件在不同状态下的显示效果。
/// 包含本位币、自定义汇率、选中状态等多种场景的视觉效果验证。
#if DEBUG
  struct CurrencyRateRow_Previews: PreviewProvider {
    static var previews: some View {
      ScrollView {
        VStack(spacing: 12) {
          Text("货币行预览")
            .font(.largeTitle)
            .bold()
            .padding()

          // 场景1: 本位币
          CurrencyRateRow(
            viewModel: .init(
              currencyName: "人民币",
              currencyCode: "CNY",
              currencySymbol: "¥",
              rate: 1.0,
              isBaseCurrency: true,
              isSelected: true,
              isCustom: false
            ))

          // 场景2: 普通外币
          CurrencyRateRow(
            viewModel: .init(
              currencyName: "美元",
              currencyCode: "USD",
              currencySymbol: "$",
              rate: 7.25,
              isBaseCurrency: false,
              isSelected: true,
              isCustom: false
            ))

          // 场景3: 自定义汇率
          CurrencyRateRow(
            viewModel: .init(
              currencyName: "欧元",
              currencyCode: "EUR",
              currencySymbol: "€",
              rate: 7.89,
              isBaseCurrency: false,
              isSelected: false,
              isCustom: true
            ))

          // 场景4: 未选中状态
          CurrencyRateRow(
            viewModel: .init(
              currencyName: "日元",
              currencyCode: "JPY",
              currencySymbol: "¥",
              rate: 0.048,
              isBaseCurrency: false,
              isSelected: false,
              isCustom: false
            ))

        }
        .padding(.horizontal)
      }
      .background(Color("C_LightBlue"))
    }
  }
#endif
