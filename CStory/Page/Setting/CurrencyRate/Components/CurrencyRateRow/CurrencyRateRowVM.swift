//
//  CurrencyRateRowVM.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/25.
//

import Combine
import SwiftUI

/// 货币汇率行视图模型
///
/// 管理货币行组件的业务逻辑和数据处理，遵循MVVM架构模式。
/// 专用于货币汇率设置页面，负责货币数据的格式化、状态计算和展示逻辑。
///
/// 该类将CurrencyModel数据转换为视图层可直接使用的格式，
/// 包括汇率格式化、状态管理、文本处理等复杂业务逻辑。
///
/// ## 主要功能
/// - 货币数据格式化和计算
/// - 选择状态管理
/// - 汇率值格式化
/// - 交互事件处理
///
/// ## 使用示例
/// ```swift
/// // 从CurrencyModel创建
/// let viewModel = CurrencyRateRowVM(
///   from: currencyModel,
///   isSelected: currency.isSelected,
///   isCustom: currency.isCustom
/// )
///
/// // 直接创建
/// let viewModel = CurrencyRateRowVM(
///   currencyName: "美元",
///   currencyCode: "USD",
///   currencySymbol: "$",
///   rate: 7.25,
///   isBaseCurrency: false,
///   isSelected: true,
///   isCustom: false
/// )
/// ```
///
/// - Author: 咩咩
/// - Since: 2025.7.25
/// - Note: 该类支持动态数据更新，视图会自动响应属性变化
/// - SeeAlso: `CurrencyRateRow`, `CurrencyModel`
class CurrencyRateRowVM: ObservableObject {

  // MARK: - Published Properties

  /// 货币名称
  ///
  /// 格式化后的货币显示名称。
  /// 如"美元"、"欧元"、"人民币"等。
  @Published var currencyName: String

  /// 货币代码
  ///
  /// 标准的三位货币代码。
  /// 如"USD"、"EUR"、"CNY"等。
  @Published var currencyCode: String

  /// 货币符号
  ///
  /// 货币的显示符号。
  /// 如"$"、"€"、"¥"等。
  @Published var currencySymbol: String

  /// 汇率值
  ///
  /// 相对于本位币的汇率值。
  /// 表示1单位该货币等于多少本位币。
  @Published var rate: Double

  /// 是否为本位币
  ///
  /// 标识该货币是否为本位币。
  /// 本位币的汇率固定为1。
  @Published var isBaseCurrency: Bool

  /// 是否选中
  ///
  /// 控制货币的选中状态，影响勾选框显示。
  /// 选中的货币会在应用中可用。
  @Published var isSelected: Bool

  /// 是否为自定义汇率
  ///
  /// 标识该货币是否使用了自定义汇率。
  /// 自定义汇率会显示"自定义"标签。
  @Published var isCustom: Bool

  // MARK: - Computed Properties

  /// 货币代码和符号组合文本
  ///
  /// 格式化的货币代码和符号显示文本。
  /// 如"USD · $"、"EUR · €"等。
  var currencyCodeAndSymbol: String {
    return "\(currencyCode) · \(currencySymbol)"
  }

  /// 格式化的汇率显示文本
  ///
  /// 根据货币类型和汇率值格式化的显示文本。
  /// 本位币显示"1"，其他货币显示格式化的汇率值。
  var formattedRate: String {
    if isBaseCurrency {
      return "1"
    } else if rate > 0 {
      return NumberFormatService.shared.formatExchangeRate(rate, maxDecimals: 6)
    } else {
      return "--"
    }
  }

  // MARK: - Interaction

  /// 点击事件回调
  ///
  /// 当货币行被点击时执行的闭包，通常用于显示货币详情或操作菜单。
  var onTap: (() -> Void)?

  /// 选择状态变更回调
  ///
  /// 当选择状态发生变化时执行的闭包，用于同步数据状态。
  var onSelectionChanged: ((Bool) -> Void)?

  // MARK: - Initialization

  init(
    currencyName: String,
    currencyCode: String,
    currencySymbol: String,
    rate: Double,
    isBaseCurrency: Bool,
    isSelected: Bool,
    isCustom: Bool,
    onTap: (() -> Void)? = nil,
    onSelectionChanged: ((Bool) -> Void)? = nil
  ) {
    self.currencyName = currencyName
    self.currencyCode = currencyCode
    self.currencySymbol = currencySymbol
    self.rate = rate
    self.isBaseCurrency = isBaseCurrency
    self.isSelected = isSelected
    self.isCustom = isCustom
    self.onTap = onTap
    self.onSelectionChanged = onSelectionChanged
  }

  // MARK: - 便利初始化方法

  /// 从CurrencyModel创建CurrencyRateRowVM
  convenience init(
    from currency: CurrencyModel,
    onTap: (() -> Void)? = nil,
    onSelectionChanged: ((Bool) -> Void)? = nil
  ) {
    self.init(
      currencyName: currency.name,
      currencyCode: currency.code,
      currencySymbol: currency.symbol,
      rate: currency.rate,
      isBaseCurrency: currency.isBaseCurrency,
      isSelected: currency.isSelected,
      isCustom: currency.isCustom,
      onTap: onTap,
      onSelectionChanged: onSelectionChanged
    )
  }

  // MARK: - Public Methods

  /// 切换选择状态
  ///
  /// 切换当前的选择状态，用于响应用户点击勾选框。
  /// 该方法会触发UI更新和回调执行。
  func toggleSelection() {
    isSelected.toggle()
    onSelectionChanged?(isSelected)
  }

  /// 设置选择状态
  ///
  /// 直接设置选择状态，用于外部控制选择状态。
  /// 
  /// - Parameter selected: 目标选择状态
  func setSelected(_ selected: Bool) {
    guard isSelected != selected else { return }
    isSelected = selected
    onSelectionChanged?(isSelected)
  }

  /// 更新汇率值
  ///
  /// 更新汇率值并重新计算相关状态。
  /// 用于响应汇率数据的更新。
  ///
  /// - Parameters:
  ///   - newRate: 新的汇率值
  ///   - isCustomRate: 是否为自定义汇率
  func updateRate(_ newRate: Double, isCustomRate: Bool = false) {
    rate = newRate
    isCustom = isCustomRate
  }

  /// 重置为默认汇率
  ///
  /// 将自定义汇率重置为默认汇率。
  /// 用于恢复默认汇率设置。
  ///
  /// - Parameter defaultRate: 默认汇率值
  func resetToDefaultRate(_ defaultRate: Double) {
    rate = defaultRate
    isCustom = false
  }

}
