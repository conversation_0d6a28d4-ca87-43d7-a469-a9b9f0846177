//
//  iCloudSyncView.swift
//  CStory
//
//  Created by AI Assistant on 2025/8/11.
//

import SwiftUI

/// iCloud同步页面
///
/// 提供iCloud数据同步功能的设置和管理界面。
/// 包括同步状态显示、手动同步、同步设置等功能。
struct iCloudSyncView: View {

  // MARK: - Dependencies

  @Environment(\.dataManager) private var dataManager: DataManagement
  @EnvironmentObject private var pathManager: PathManagerHelper

  // MARK: - State

  /// 是否启用iCloud同步
  @State private var iCloudSyncEnabled = false

  /// 同步状态
  @State private var syncStatus: SyncStatus = .idle

  /// 最后同步时间
  @State private var lastSyncTime: Date?

  /// 是否显示同步详情
  @State private var showSyncDetails = false

  // MARK: - Body

  var body: some View {
    VStack(spacing: 0) {
      // MARK: 导航栏
      NavigationBarKit(
        viewModel: NavigationBarKitVM(
          title: "iCloud同步",
          backAction: {
            dataManager.hapticManager.trigger(.impactLight)
            pathManager.path.removeLast()
          }
        )
      )

      // MARK: 主要内容
      ScrollView {
        VStack(spacing: 16) {
          // 同步开关
          syncToggleSection

          // 同步状态
          if iCloudSyncEnabled {
            syncStatusSection
          }

          // 同步设置
          if iCloudSyncEnabled {
            syncSettingsSection
          }

          // 说明信息
          infoSection
        }
        .padding(.horizontal, 16)
        .padding(.top, 24)
        .padding(.bottom, 80)
      }
      .background(Color.cLightBlue)
    }
    .background(Color.cLightBlue)
    .navigationBarTitleDisplayMode(.inline)
    .navigationBarBackButtonHidden(true)
  }

  // MARK: - Subviews

  /// 同步开关区域
  private var syncToggleSection: some View {
    HStack {
      VStack(alignment: .leading, spacing: 4) {
        Text("启用iCloud同步")
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(.cBlack)

        Text("在多设备间同步您的记账数据")
          .font(.system(size: 12, weight: .regular))
          .foregroundColor(.cBlack.opacity(0.6))
      }

      Spacer()

      Toggle("", isOn: $iCloudSyncEnabled)
        .toggleStyle(SwitchToggleStyle(tint: .accentColor))
        .onChange(of: iCloudSyncEnabled) { _, newValue in
          dataManager.hapticManager.trigger(.selection)
          if newValue {
            // 启用同步
            enableiCloudSync()
          } else {
            // 禁用同步
            disableiCloudSync()
          }
        }
    }
    .padding(16)
    .background(Color.cWhite)
    .cornerRadius(16)
    .overlay(
      RoundedRectangle(cornerRadius: 16)
        .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
  }

  /// 同步状态区域
  private var syncStatusSection: some View {
    VStack(spacing: 16) {
      // 状态显示
      HStack {
        Image(systemName: syncStatus.icon)
          .font(.system(size: 18, weight: .medium))
          .foregroundColor(syncStatus.color)

        VStack(alignment: .leading, spacing: 4) {
          Text("同步状态")
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(.cBlack)

          Text(syncStatus.description)
            .font(.system(size: 12, weight: .regular))
            .foregroundColor(.cBlack.opacity(0.6))
        }

        Spacer()

        if syncStatus == .syncing {
          ProgressView()
            .scaleEffect(0.8)
        }
      }

      // 最后同步时间
      if let lastSyncTime = lastSyncTime {
        HStack {
          Text("最后同步")
            .font(.system(size: 12, weight: .regular))
            .foregroundColor(.cBlack.opacity(0.6))

          Spacer()

          Text(formatSyncTime(lastSyncTime))
            .font(.system(size: 12, weight: .regular))
            .foregroundColor(.cBlack.opacity(0.6))
        }
      }

      // 手动同步按钮
      Button(action: {
        dataManager.hapticManager.trigger(.impactLight)
        performManualSync()
      }) {
        HStack {
          Image(systemName: "arrow.clockwise")
            .font(.system(size: 14, weight: .medium))

          Text("立即同步")
            .font(.system(size: 14, weight: .medium))
        }
        .foregroundColor(.accentColor)
        .frame(maxWidth: .infinity)
        .frame(height: 44)
        .background(Color.accentColor.opacity(0.1))
        .cornerRadius(10)
      }
      .disabled(syncStatus == .syncing)
    }
    .padding(16)
    .background(Color.cWhite)
    .cornerRadius(16)
    .overlay(
      RoundedRectangle(cornerRadius: 16)
        .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
  }

  /// 同步设置区域
  private var syncSettingsSection: some View {
    VStack(spacing: 12) {
      // 自动同步
      HStack {
        Text("自动同步")
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.cBlack)

        Spacer()

        Toggle("", isOn: .constant(true))
          .toggleStyle(SwitchToggleStyle(tint: .accentColor))
          .disabled(true)  // 暂时禁用
      }

      Divider()
        .padding(.horizontal, -16)

      // 同步频率
      HStack {
        Text("同步频率")
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.cBlack)

        Spacer()

        Text("实时")
          .font(.system(size: 14, weight: .regular))
          .foregroundColor(.cBlack.opacity(0.6))

        Image(systemName: "chevron.right")
          .font(.system(size: 12, weight: .medium))
          .foregroundColor(.cBlack.opacity(0.6))
      }
    }
    .padding(16)
    .background(Color.cWhite)
    .cornerRadius(16)
    .overlay(
      RoundedRectangle(cornerRadius: 16)
        .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
  }

  /// 说明信息区域
  private var infoSection: some View {
    VStack(alignment: .leading, spacing: 12) {
      HStack {
        Image(systemName: "info.circle.fill")
          .font(.system(size: 16))
          .foregroundColor(.accentColor)

        Text("关于iCloud同步")
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.cBlack)

        Spacer()
      }

      VStack(alignment: .leading, spacing: 8) {
        infoItem("• 数据将安全地存储在您的iCloud账户中")
        infoItem("• 支持在iPhone、iPad等设备间同步")
        infoItem("• 需要登录iCloud账户并开启iCloud Drive")
        infoItem("• 同步可能需要一些时间，请保持网络连接")
      }
    }
    .padding(16)
    .background(Color.cWhite)
    .cornerRadius(16)
    .overlay(
      RoundedRectangle(cornerRadius: 16)
        .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
  }

  /// 信息项
  private func infoItem(_ text: String) -> some View {
    Text(text)
      .font(.system(size: 12, weight: .regular))
      .foregroundColor(.cBlack.opacity(0.6))
  }

  // MARK: - Methods

  /// 启用iCloud同步
  private func enableiCloudSync() {
    syncStatus = .syncing

    // 模拟启用过程
    DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
      syncStatus = .synced
      lastSyncTime = Date()
    }
  }

  /// 禁用iCloud同步
  private func disableiCloudSync() {
    syncStatus = .idle
    lastSyncTime = nil
  }

  /// 执行手动同步
  private func performManualSync() {
    syncStatus = .syncing

    // 模拟同步过程
    DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
      syncStatus = .synced
      lastSyncTime = Date()
    }
  }

  /// 格式化同步时间
  private func formatSyncTime(_ date: Date) -> String {
    let formatter = DateFormatter()
    formatter.dateStyle = .none
    formatter.timeStyle = .short
    return formatter.string(from: date)
  }
}

// MARK: - SyncStatus

/// 同步状态枚举
enum SyncStatus {
  case idle
  case syncing
  case synced
  case error

  var icon: String {
    switch self {
    case .idle: return "cloud"
    case .syncing: return "cloud.fill"
    case .synced: return "checkmark.circle.fill"
    case .error: return "exclamationmark.triangle.fill"
    }
  }

  var color: Color {
    switch self {
    case .idle: return .gray
    case .syncing: return .blue
    case .synced: return .green
    case .error: return .red
    }
  }

  var description: String {
    switch self {
    case .idle: return "未同步"
    case .syncing: return "同步中..."
    case .synced: return "已同步"
    case .error: return "同步失败"
    }
  }
}

// MARK: - Preview

#Preview {
  iCloudSyncView()
    .environment(\.dataManager, DataManagement())
    .environmentObject(PathManagerHelper())
}
