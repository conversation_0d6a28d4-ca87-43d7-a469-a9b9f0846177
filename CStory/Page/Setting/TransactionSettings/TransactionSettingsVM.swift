import Combine
import SwiftUI

/// 记账设置页面视图模型
final class TransactionSettingsVM: ObservableObject {

  // MARK: - Dependencies

  private let defaultsManager = TransactionDefaultsManager.shared

  // MARK: - Published Properties

  @Published var selectedRecordingMode: TransactionDefaultsManager.RecordingMode
  @Published var selectedAIInputMode: TransactionDefaultsManager.AIInputMode
  @Published var defaultExpenseCard: CardModel?
  @Published var defaultIncomeCard: CardModel?
  @Published var availableCards: [CardModel] = []

  // MARK: - Initialization

  init() {
    self.selectedRecordingMode = defaultsManager.defaultRecordingMode
    self.selectedAIInputMode = defaultsManager.aiInputMode
  }

  // MARK: - Public Methods

  /// 加载数据
  func loadData(dataManager: DataManagement) {
    // 加载可用卡片
    availableCards = dataManager.cards.filter { $0.isSelected }

    // 加载默认卡片
    if let expenseCardId = defaultsManager.defaultExpenseCardId {
      defaultExpenseCard = availableCards.first { $0.id == expenseCardId }
    }

    if let incomeCardId = defaultsManager.defaultIncomeCardId {
      defaultIncomeCard = availableCards.first { $0.id == incomeCardId }
    }
  }

  /// 设置记账方式
  func setRecordingMode(_ mode: TransactionDefaultsManager.RecordingMode) {
    selectedRecordingMode = mode
    defaultsManager.defaultRecordingMode = mode
  }

  /// 设置AI输入方式
  func setAIInputMode(_ mode: TransactionDefaultsManager.AIInputMode) {
    selectedAIInputMode = mode
    defaultsManager.aiInputMode = mode
  }

  /// 设置默认支出卡片
  func setDefaultExpenseCard(_ card: CardModel?) {
    defaultExpenseCard = card
    defaultsManager.defaultExpenseCardId = card?.id
  }

  /// 设置默认收入卡片
  func setDefaultIncomeCard(_ card: CardModel?) {
    defaultIncomeCard = card
    defaultsManager.defaultIncomeCardId = card?.id
  }

}
