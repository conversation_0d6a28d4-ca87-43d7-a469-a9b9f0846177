//
//  SelectCoverSheetVM.swift
//  CStory
//
//  Created by NZUE on 2025/8/6.
//

import Foundation
import SwiftUI

/// 卡片背景选择弹窗 ViewModel
///
/// 管理卡片背景选择界面的状态和业务逻辑，包括：
/// - 预设背景列表管理
/// - 背景选择逻辑
/// - 深色背景状态管理
class SelectCoverSheetVM: ObservableObject {

  // MARK: - 属性

  /// 选中的背景类型
  @Binding var selectedType: CardCoverType
  /// 是否为深色背景
  @Binding var isDarkBackground: Bool

  // MARK: - 关闭回调

  /// 关闭弹窗回调
  let onDismiss: () -> Void

  // MARK: - 初始化

  init(
    selectedType: Binding<CardCoverType>,
    isDarkBackground: Binding<Bool>,
    onDismiss: @escaping () -> Void = {}
  ) {
    self._selectedType = selectedType
    self._isDarkBackground = isDarkBackground
    self.onDismiss = onDismiss
  }

  // MARK: - 计算属性

  /// 获取预设背景列表
  var presets: [CardCoverHelper.PresetCoverConfig] {
    return CardCoverHelper.presetCovers
  }

  /// 计算屏幕宽度
  var screenWidth: CGFloat {
    UIScreen.main.bounds.width
  }

  /// 计算卡片宽度 - 每行3个
  var cardWidth: CGFloat {
    (screenWidth - 80) / 3  // 考虑左右边距和间距
  }

  // MARK: - 公共方法

  /// 处理背景选择
  /// - Parameter preset: 选中的预设背景配置
  func handleBackgroundSelection(_ preset: CardCoverHelper.PresetCoverConfig) {
    selectedType = preset.type
    isDarkBackground = preset.isDark
  }

  /// 处理关闭操作
  func handleDismiss() {
    onDismiss()
  }

  /// 检查背景是否被选中
  /// - Parameter preset: 要检查的预设背景配置
  /// - Returns: 是否被选中
  func isBackgroundSelected(_ preset: CardCoverHelper.PresetCoverConfig) -> Bool {
    return selectedType == preset.type
  }
}
