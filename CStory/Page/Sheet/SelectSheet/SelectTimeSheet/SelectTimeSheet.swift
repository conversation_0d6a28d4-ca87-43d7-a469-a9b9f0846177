//
//  SelectTimeSheet.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 时间选择弹窗
///
/// 提供日期和时间选择功能的弹窗组件。
/// 使用滚轮式选择器，支持选择日期和时间（小时、分钟）。
///
/// ## 使用示例
/// ```swift
/// SelectTimeSheet(
///     viewModel: SelectTimeSheetVM(
///         selectedDate: $transactionDate,
///         onConfirm: {
///             // 处理确认逻辑
///         },
///         onCancel: {
///             dismiss()
///         }
///     )
/// )
/// ```
struct SelectTimeSheet: View {
  // MARK: - 属性

  /// ViewModel
  @ObservedObject private var viewModel: SelectTimeSheetVM

  // MARK: - 初始化
  init(viewModel: SelectTimeSheetVM) {
    self.viewModel = viewModel
  }

  var body: some View {
    VStack(spacing: 12) {
      // 标题栏
      SheetTitle(
        title: "选择日期和时间",
        button: "xmark.circle.fill",
        rightButtonAction: {
          viewModel.handleCancel()
        }
      )

      // 日期选择器
      DatePicker(
        "选择时间",
        selection: $viewModel.tempDate,
        displayedComponents: [.date, .hourAndMinute]
      )
      .datePickerStyle(.wheel)
      .labelsHidden()
      .padding(.horizontal, 12)

      // 确认按钮
      Button(action: {
        viewModel.handleConfirm()
      }) {
        Text("确认")
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(.white)
          .frame(maxWidth: .infinity)
          .frame(height: 44)
          .background(Color.cAccentBlue)
          .cornerRadius(12)
      }
      .padding(.horizontal, 16)
      .padding(.bottom, 16)
    }
    .onAppear {
      // 初始化临时日期
      viewModel.resetTempDate()
    }
  }
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct SelectTimeSheet_Previews: PreviewProvider {
    static var previews: some View {
      SelectTimePreviewContainer()
    }
  }

  struct SelectTimePreviewContainer: View {
    @State private var showSheet = false
    @State private var selectedDate = Date()

    var body: some View {
      VStack {
        Button("选择时间") {
          showSheet = true
        }
        .padding()
        .background(Color.cAccentBlue)
        .foregroundColor(.white)
        .cornerRadius(12)

        Text("当前时间: \(selectedDate, formatter: dateFormatter)")
          .padding()
      }
      .frame(maxWidth: .infinity, maxHeight: .infinity)
      .background(Color.cLightBlue)
      .floatingSheet(
        isPresented: $showSheet,
        config: SheetBase(
          maxDetent: .height(400),
          cornerRadius: 24,
          interactiveDimiss: true,
          hPadding: 8,
          bPadding: 4
        )
      ) {
        SelectTimeSheet(
          viewModel: SelectTimeSheetVM(
            selectedDate: $selectedDate,
            onConfirm: { showSheet = false },
            onCancel: { showSheet = false }
          )
        )
      }
    }

    private var dateFormatter: DateFormatter {
      let formatter = DateFormatter()
      formatter.dateStyle = .medium
      formatter.timeStyle = .short
      return formatter
    }
  }
#endif
