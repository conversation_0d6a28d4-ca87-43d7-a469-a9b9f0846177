//
//  SelectTimeSheetVM.swift
//  CStory
//
//  Created by NZUE on 2025/8/6.
//

import Foundation
import SwiftUI

/// 时间选择弹窗 ViewModel
///
/// 管理时间选择界面的状态和业务逻辑，包括：
/// - 日期时间选择状态管理
/// - 临时日期处理逻辑
/// - 确认和取消操作处理
class SelectTimeSheetVM: ObservableObject {

  // MARK: - 属性

  /// 选中的日期绑定
  @Binding var selectedDate: Date

  /// 临时日期（避免直接修改绑定值）
  @Published var tempDate: Date = Date()

  // MARK: - 回调

  /// 确认回调
  let onConfirm: () -> Void
  /// 取消回调
  let onCancel: () -> Void

  // MARK: - 初始化

  init(
    selectedDate: Binding<Date>,
    onConfirm: @escaping () -> Void,
    onCancel: @escaping () -> Void
  ) {
    self._selectedDate = selectedDate
    self.onConfirm = onConfirm
    self.onCancel = onCancel

    // 初始化临时日期
    self.tempDate = selectedDate.wrappedValue
  }

  // MARK: - 公共方法

  /// 处理确认选择
  func handleConfirm() {
    selectedDate = tempDate
    onConfirm()
  }

  /// 处理取消操作
  func handleCancel() {
    onCancel()
  }

  /// 重置临时日期为当前选中的日期
  func resetTempDate() {
    tempDate = selectedDate
  }
}
