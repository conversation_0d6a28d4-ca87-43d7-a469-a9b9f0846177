//
//  SelectCategorySheet.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftData
import SwiftUI

/// 交易分类选择弹窗
///
/// 显示交易分类网格，允许用户选择主分类或子分类。
/// 支持点击主分类展开/收起子分类列表。
///
/// ## 功能特性
/// - 根据交易类型过滤分类
/// - 主分类网格展示
/// - 子分类可折叠列表
/// - 支持初始选中状态
struct SelectCategorySheet: View {
  // MARK: - 属性

  /// ViewModel
  @ObservedObject private var viewModel: SelectCategorySheetVM

  // MARK: - 初始化
  init(viewModel: SelectCategorySheetVM) {
    self.viewModel = viewModel
  }

  var body: some View {
    VStack(spacing: 12) {
      // 标题栏
      SheetTitle(
        title: "选择类别",
        button: "xmark.circle.fill",
        rightButtonAction: {
          viewModel.handleCancel()
        }
      )

      // 类别网格
      ScrollView {
        LazyVStack(alignment: .leading, spacing: 12) {
          // 将分类按每行5个划分
          let rows = viewModel.filteredCategories.chunked(into: 5)
          ForEach(Array(rows.enumerated()), id: \.offset) { rowIndex, row in
            CategoryRow(
              row: row,
              selectedId: viewModel.selectedMainCategory?.id,
              selectedMainCategory: viewModel.selectedMainCategory,
              selectedSubId: viewModel.selectedSubCategory?.id,
              showSubCategories: viewModel.showSubCategories,
              viewModel: viewModel
            )
          }
        }
        .padding(12)
      }

      // 确认按钮
      Button(action: {
        viewModel.handleConfirmSelection()
      }) {
        Text("确定")
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(.white)
          .frame(maxWidth: .infinity)
          .frame(height: 48)
          .background(
            viewModel.hasSelectedCategory
              ? Color.cAccentBlue : Color.cAccentBlue.opacity(0.3)
          )
          .cornerRadius(24)
      }
      .disabled(!viewModel.hasSelectedCategory)
      .padding(.horizontal, 16)
      .padding(.bottom, 12)
    }
  }
}

// MARK: - 分类行视图
/// 分类行视图
///
/// 显示一行主分类图标，并在下方显示对应的子分类列表。
private struct CategoryRow: View {
  let row: [TransactionMainCategoryModel]
  let selectedId: String?
  let selectedMainCategory: TransactionMainCategoryModel?
  let selectedSubId: String?
  let showSubCategories: Bool
  let viewModel: SelectCategorySheetVM

  var body: some View {
    VStack(spacing: 12) {
      // 主分类行
      HStack(spacing: 12) {
        ForEach(row) { category in
          CategoryItemView(
            icon: category.icon,
            name: category.name,
            isSelected: viewModel.isMainCategorySelected(category)
          )
          .onTapGesture {
            viewModel.handleMainCategoryTap(category)
          }
        }
        // 补齐空位
        if row.count < 5 {
          ForEach(0..<(5 - row.count), id: \.self) { _ in
            Spacer().frame(maxWidth: .infinity)
          }
        }
      }
      .padding(.horizontal, 12)

      // 子分类列表
      if shouldShowSubCategories(for: row) {
        SubCategoriesListView(
          subCategories: viewModel.getSubCategories(for: selectedMainCategory!),
          selectedId: selectedSubId,
          onSelect: { subCategory in
            viewModel.handleSubCategoryTap(subCategory)
          }
        )
      }
    }
  }

  /// 判断是否显示子分类
  private func shouldShowSubCategories(for row: [TransactionMainCategoryModel]) -> Bool {
    guard let selectedMainCategory = selectedMainCategory else { return false }
    return showSubCategories
      && row.contains(where: { $0.id == selectedMainCategory.id })
      && viewModel.hasSubCategories(selectedMainCategory)
  }
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct SelectCategorySheet_Previews: PreviewProvider {
    static var previews: some View {
      SelectCategoryPreviewContainer()
    }
  }

  struct SelectCategoryPreviewContainer: View {
    @State private var showSheet = false
    @State private var selectedCategoryId: String? = nil

    var body: some View {
      VStack {
        Button("选择类别") {
          showSheet = true
        }
        .padding()
        .background(Color.cAccentBlue)
        .foregroundColor(.white)
        .cornerRadius(12)

        if let categoryId = selectedCategoryId {
          Text("已选择: \(categoryId)")
            .padding()
        }
      }
      .frame(maxWidth: .infinity, maxHeight: .infinity)
      .background(Color.cLightBlue)
      .floatingSheet(
        isPresented: $showSheet,
        config: SheetBase(
          maxDetent: .fraction(0.8),
          cornerRadius: 24,
          interactiveDimiss: true,
          hPadding: 8,
          bPadding: 4
        )
      ) {
        SelectCategorySheet(
          viewModel: SelectCategorySheetVM(
            transactionType: .expense,
            selectedCategoryId: $selectedCategoryId,
            categories: Self.createSampleCategories(),
            subCategories: Self.createSampleSubCategories(),
            dataManager: Self.createPreviewDataManager(),
            onCategorySelected: { categoryId in
              selectedCategoryId = categoryId
              showSheet = false
            },
            onCancel: { showSheet = false }
          )
        )
      }
    }

    /// 创建示例主类别
    static func createSampleCategories() -> [TransactionMainCategoryModel] {
      [
        TransactionMainCategoryModel(
          id: "expense_food",
          name: "餐饮",
          icon: .emoji("🍽️"),
          order: 0,
          type: TransactionType.expense.rawValue
        ),
        TransactionMainCategoryModel(
          id: "expense_transport",
          name: "交通",
          icon: .emoji("🚗"),
          order: 1,
          type: TransactionType.expense.rawValue
        ),
        TransactionMainCategoryModel(
          id: "income_salary",
          name: "工资",
          icon: .emoji("💰"),
          order: 0,
          type: TransactionType.income.rawValue
        ),
      ]
    }

    /// 创建示例子类别
    static func createSampleSubCategories() -> [TransactionSubCategoryModel] {
      [
        TransactionSubCategoryModel(
          id: "food_breakfast",
          name: "早餐",
          icon: .emoji("🥐"),
          order: 0,
          mainId: "expense_food"
        ),
        TransactionSubCategoryModel(
          id: "transport_taxi",
          name: "打车",
          icon: .emoji("🚕"),
          order: 0,
          mainId: "expense_transport"
        ),
      ]
    }

    /// 创建预览用的DataManagement
    static func createPreviewDataManager() -> DataManagement {
      return DataManagement(
        cards: [],
        mainCategories: createSampleCategories(),
        subCategories: createSampleSubCategories(),
        currencies: [],
        recentTransactions: [],
        allTransactions: []
      )
    }
  }
#endif
