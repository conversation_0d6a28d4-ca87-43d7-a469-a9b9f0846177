//
//  SelectCurrencySheetVM.swift
//  CStory
//
//  Created by NZUE on 2025/8/6.
//

import Foundation
import SwiftUI

/// 货币选择弹窗 ViewModel
///
/// 管理货币选择界面的状态和业务逻辑，包括：
/// - 多种显示模式的货币筛选
/// - 本位币优先显示逻辑
/// - 货币选择状态管理
/// - 选择完成回调处理
class SelectCurrencySheetVM: ObservableObject {

  // MARK: - 属性

  /// 当前选中的货币代码
  @Binding var selectedCurrencyCode: String
  /// 货币符号绑定
  @Binding var currencySymbol: String
  /// 显示模式
  let mode: String
  /// 数据管理器
  private let dataManager: DataManagement

  // MARK: - 简单转换模式参数

  /// 本位币代码（用于简单转换模式）
  let baseCurrencyCode: String?
  /// 备选货币代码（用于简单转换模式）
  let alternativeCurrencyCode: String?

  // MARK: - 回调

  /// 选择完成回调
  let onSelect: ((String) -> Void)?
  /// 本位币选择完成回调（用于本位币选择模式）
  let onBaseCurrencySelect: ((CurrencyModel) -> Void)?
  /// 关闭弹窗回调
  let onDismiss: () -> Void

  // MARK: - 初始化

  init(
    selectedCurrencyCode: Binding<String>,
    currencySymbol: Binding<String>,
    mode: String = "all",
    baseCurrencyCode: String? = nil,
    alternativeCurrencyCode: String? = nil,
    dataManager: DataManagement,
    onSelect: ((String) -> Void)? = nil,
    onBaseCurrencySelect: ((CurrencyModel) -> Void)? = nil,
    onDismiss: @escaping () -> Void = {}
  ) {
    self._selectedCurrencyCode = selectedCurrencyCode
    self._currencySymbol = currencySymbol
    self.mode = mode
    self.baseCurrencyCode = baseCurrencyCode
    self.alternativeCurrencyCode = alternativeCurrencyCode
    self.dataManager = dataManager
    self.onSelect = onSelect
    self.onBaseCurrencySelect = onBaseCurrencySelect
    self.onDismiss = onDismiss
  }

  // MARK: - 计算属性

  /// 获取本位币代码
  var defaultBaseCurrencyCode: String {
    baseCurrencyCode ?? UserDefaults.standard.string(forKey: "baseCurrencyCode") ?? "CNY"
  }

  /// 获取本位币对象
  var baseCurrency: CurrencyModel? {
    dataManager.currencies.first { $0.code == defaultBaseCurrencyCode }
  }

  /// 获取卡片中使用的货币列表
  var cardCurrencies: [CurrencyModel] {
    // 从所有卡片中提取货币代码
    let cardCurrencyCodes = Set(dataManager.cards.map { $0.currency })
    // 根据这些代码查找对应的货币对象
    return dataManager.currencies.filter { currency in
      cardCurrencyCodes.contains(currency.code)
    }
  }

  /// 根据模式确定显示的货币列表
  var currencies: [CurrencyModel] {
    switch mode {
    case "baseCurrencySelection":
      return dataManager.currencies.sorted(by: { $0.order < $1.order })

    case "simpleConversion":
      return getSimpleConversionCurrencies()

    case "baseAndCard":
      return getBaseAndCardCurrencies()

    default:  // "all"
      return getAllCurrencies()
    }
  }

  // MARK: - 私有方法

  /// 获取简单转换模式的货币列表
  private func getSimpleConversionCurrencies() -> [CurrencyModel] {
    var result: [CurrencyModel] = []

    // 添加本位币
    if let base = dataManager.currencies.first(where: { $0.code == defaultBaseCurrencyCode }) {
      result.append(base)
    }

    // 添加备选货币（如果与本位币不同）
    if let altCode = alternativeCurrencyCode,
      altCode != defaultBaseCurrencyCode,
      let altCurrency = dataManager.currencies.first(where: { $0.code == altCode })
    {
      result.append(altCurrency)
    }

    return result
  }

  /// 获取基础货币和卡片货币模式的货币列表
  private func getBaseAndCardCurrencies() -> [CurrencyModel] {
    var currencySet = Set<String>()
    var result: [CurrencyModel] = []

    // 先添加本位币（如果存在）
    if let base = baseCurrency {
      result.append(base)
      currencySet.insert(base.code)
    }

    // 然后添加卡片货币（去重）
    for currency in cardCurrencies {
      if !currencySet.contains(currency.code) {
        result.append(currency)
        currencySet.insert(currency.code)
      }
    }

    return result.sorted(by: { $0.order < $1.order })
  }

  /// 获取所有模式的货币列表
  private func getAllCurrencies() -> [CurrencyModel] {
    var result: [CurrencyModel] = []
    var currencySet = Set<String>()

    // 1. 首先添加本位币（如果存在）
    if let base = baseCurrency {
      result.append(base)
      currencySet.insert(base.code)
    }

    // 2. 添加当前选中的货币（如果不是本位币）
    if !selectedCurrencyCode.isEmpty {
      let selectedCurrency = dataManager.currencies.first { $0.code == selectedCurrencyCode }
      if let selected = selectedCurrency, !currencySet.contains(selected.code) {
        result.append(selected)
        currencySet.insert(selected.code)
      }
    }

    // 3. 添加其他标记为isSelected=true的货币
    for currency in dataManager.currencies {
      if currency.isSelected && !currencySet.contains(currency.code) {
        result.append(currency)
        currencySet.insert(currency.code)
      }
    }

    return result.sorted(by: { $0.order < $1.order })
  }

  // MARK: - 公共方法

  /// 处理关闭操作
  @MainActor
  func handleDismiss() {
    dataManager.hapticManager.trigger(.impactLight)
    onDismiss()
  }

  /// 处理货币选择
  /// - Parameter currency: 被选中的货币
  @MainActor
  func handleCurrencySelection(_ currency: CurrencyModel) {
    dataManager.hapticManager.trigger(.selection)

    if mode == "baseCurrencySelection" {
      // 本位币选择模式
      if !isBaseCurrency(currency) {
        onBaseCurrencySelect?(currency)
        onDismiss()
      }
    } else {
      // 其他模式
      let oldValue = selectedCurrencyCode
      selectedCurrencyCode = currency.code
      currencySymbol = currency.symbol
      // 只有当货币代码真正变化时才调用回调
      if oldValue != currency.code {
        onSelect?(currency.code)
      }
      onDismiss()
    }
  }

  /// 判断是否为本位币
  /// - Parameter currency: 要检查的货币
  /// - Returns: 是否为本位币
  func isBaseCurrency(_ currency: CurrencyModel) -> Bool {
    currency.code == defaultBaseCurrencyCode
  }

  /// 检查货币是否被选中
  /// - Parameter currency: 要检查的货币
  /// - Returns: 是否被选中
  func isCurrencySelected(_ currency: CurrencyModel) -> Bool {
    if mode == "baseCurrencySelection" {
      return isBaseCurrency(currency)
    } else {
      return currency.code == selectedCurrencyCode
    }
  }

  /// 获取货币显示名称
  /// - Parameter currency: 货币对象
  /// - Returns: 显示名称
  func getCurrencyDisplayName(_ currency: CurrencyModel) -> String {
    if mode == "simpleConversion" {
      return isBaseCurrency(currency) ? "本位币" : "卡片货币"
    } else {
      return currency.name
    }
  }

  /// 检查是否应该显示"当前"标识
  /// - Parameter currency: 要检查的货币
  /// - Returns: 是否显示"当前"标识
  func shouldShowCurrentLabel(_ currency: CurrencyModel) -> Bool {
    return mode == "baseCurrencySelection" && isBaseCurrency(currency)
  }
}
