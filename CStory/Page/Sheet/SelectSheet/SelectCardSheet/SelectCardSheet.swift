//
//  SelectCardSheet.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftData
import SwiftUI

/// 卡片选择弹窗
///
/// 显示用户的卡片列表，允许用户选择一张卡片。
/// 包含卡片列表、添加新卡片按钮和确认按钮。
///
/// ## 功能特性
/// - 显示所有已选中的卡片
/// - 支持添加新卡片
/// - 单选模式
/// - 触觉反馈
/// - 实时搜索功能
struct SelectCardSheet: View {
  // MARK: - 属性

  /// ViewModel
  @ObservedObject private var viewModel: SelectCardSheetVM

  /// 搜索文本
  @State private var searchText = ""

  // MARK: - 依赖注入

  /// 路由管理器
  @EnvironmentObject private var pathManager: PathManagerHelper

  // MARK: - 初始化
  init(viewModel: SelectCardSheetVM) {
    self.viewModel = viewModel
  }

  var body: some View {
    NavigationView {
      VStack(spacing: 12) {
        // 标题栏
        SheetTitle(
          title: "选择卡片",
          button: "xmark.circle.fill",
          rightButtonAction: {
            viewModel.handleCancel()
          }
        )

        // 卡片列表
        ScrollView {
          LazyVStack(spacing: 12) {
            // 显示过滤后的卡片
            ForEach(filteredCards) { card in
              CardRow(
                viewModel: viewModel.createCardRowVM(for: card)
              )
            }

            // 添加卡片按钮
            AddCardButton(
              viewModel: viewModel.createAddCardButtonVM(pathManager: pathManager)
            )
          }
          .padding(12)
        }

        // 确认按钮
        Button(action: {
          viewModel.handleConfirmSelection()
        }) {
          Text("确定")
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .frame(height: 48)
            .background(
              viewModel.canConfirmSelection
                ? Color.cAccentBlue : Color.cAccentBlue.opacity(0.3)
            )
            .cornerRadius(24)
        }
        .disabled(!viewModel.canConfirmSelection)
        .padding(.horizontal, 16)
        .padding(.bottom, 12)
      }
      .searchable(text: $searchText, prompt: "搜索卡片名称或银行")
    }
  }

  // MARK: - 计算属性

  /// 过滤后的卡片列表
  private var filteredCards: [CardModel] {
    if searchText.isEmpty {
      return viewModel.selectableCards
    } else {
      return viewModel.selectableCards.filter { card in
        card.name.localizedCaseInsensitiveContains(searchText)
          || card.bankName.localizedCaseInsensitiveContains(searchText)
          || card.cardNumber.localizedCaseInsensitiveContains(searchText)
      }
    }
  }

}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct SelectCardSheet_Previews: PreviewProvider {
    static var previews: some View {
      SelectCardPreviewContainer()
    }
  }

  struct SelectCardPreviewContainer: View {
    @State private var showSheet = false
    @State private var selectedCardId: UUID? = nil

    var body: some View {
      VStack {
        Button("选择卡片") {
          showSheet = true
        }
        .padding()
        .background(Color.cAccentBlue)
        .foregroundColor(.white)
        .cornerRadius(12)

        if let cardId = selectedCardId {
          Text("已选择卡片: \(cardId.uuidString.prefix(8))...")
            .padding()
        }
      }
      .frame(maxWidth: .infinity, maxHeight: .infinity)
      .background(Color.cLightBlue)
      .floatingSheet(
        isPresented: $showSheet,
        config: SheetBase(
          maxDetent: .fraction(0.6),
          cornerRadius: 24,
          interactiveDimiss: true,
          hPadding: 8,
          bPadding: 4
        )
      ) {
        SelectCardSheet(
          viewModel: SelectCardSheetVM(
            selectedCardId: $selectedCardId,
            cards: Self.createSampleCards(),
            dataManager: Self.createPreviewDataManager(),
            onCardSelected: { cardId in
              selectedCardId = cardId
              showSheet = false
            },
            onCancel: { showSheet = false }
          )
        )
      }
    }

    /// 创建示例卡片
    static func createSampleCards() -> [CardModel] {
      let now = Date()
      return [
        CardModel(
          id: UUID(), order: 0, isCredit: false, isSelected: true,
          name: "招商银行储蓄卡", remark: "", currency: "CNY", symbol: "¥",
          balance: 5000.0, credit: 0, isStatistics: true, cover: "Card_CS_1",
          bankName: "招商银行", cardNumber: "1234", isFixedDueDay: true,
          createdAt: now, updatedAt: now
        ),
        CardModel(
          id: UUID(), order: 1, isCredit: true, isSelected: true,
          name: "建设银行信用卡", remark: "", currency: "CNY", symbol: "¥",
          balance: -2000.0, credit: 10000, isStatistics: true, cover: "Card_CS_2",
          bankName: "建设银行", cardNumber: "5678", isFixedDueDay: true,
          createdAt: now, updatedAt: now
        ),
      ]
    }

    /// 创建预览用的DataManagement
    static func createPreviewDataManager() -> DataManagement {
      return DataManagement(
        cards: createSampleCards(),
        mainCategories: [],
        subCategories: [],
        currencies: [],
        recentTransactions: [],
        allTransactions: []
      )
    }
  }
#endif
