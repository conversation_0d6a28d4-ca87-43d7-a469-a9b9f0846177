//
//  SheetTitle.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 弹窗标题组件
///
/// 用于在弹窗顶部显示标题和右侧按钮（通常是关闭按钮）。
/// 提供统一的弹窗标题样式，包括字体、颜色和间距。
///
/// ## 使用示例
/// ```swift
/// SheetTitle(
///     title: "选择卡片",
///     button: "xmark",
///     rightButtonAction: {
///         dismiss()
///     }
/// )
/// ```
struct SheetTitle: View {
  // MARK: - 配置属性

  /// 标题文本
  let title: String

  /// 按钮图标名称（SF Symbol）
  let button: String

  /// 右侧按钮点击事件
  let rightButtonAction: (() -> Void)?

  // MARK: - 初始化

  init(
    title: String,
    button: String,
    rightButtonAction: (() -> Void)? = nil
  ) {
    self.title = title
    self.button = button
    self.rightButtonAction = rightButtonAction
  }

  // MARK: - 视图主体

  var body: some View {
    HStack {
      // 标题
      Text(title)
        .font(.system(size: 18, weight: .medium))
        .foregroundColor(.cBlack)

      Spacer()

      // 右侧按钮
      Button(action: {
        rightButtonAction?()
      }) {
        Image(systemName: button)
          .foregroundColor(.cBlack.opacity(0.2))
          .font(.system(size: 18, weight: .medium))
      }
    }
    .padding(.horizontal, 16)
    .padding(.top, 12)
  }
}
