//
//  CurrencyActionSheetVM.swift
//  CStory
//
//  Created by NZUE on 2025/8/8.
//

import SwiftData
import SwiftUI

/// 货币操作弹窗 ViewModel
///
/// 管理货币操作界面的状态和业务逻辑，包括：
/// - 设为本位币操作
/// - 自定义汇率输入
/// - 操作完成回调处理
final class CurrencyActionSheetVM: ObservableObject {

  // MARK: - Published Properties

  /// 自定义汇率输入文本
  @Published var customRateText: String = ""

  /// 是否显示数字键盘
  @Published var showNumericKeypad: Bool = false

  // MARK: - Properties

  /// 当前操作的货币
  let currency: CurrencyModel

  /// 数据管理器
  private let dataManager: DataManagement

  /// 关闭弹窗回调
  let dismiss: () -> Void

  /// 设为本位币回调
  let onSetAsBaseCurrency: ((CurrencyModel) -> Void)?

  /// 自定义汇率保存回调
  let onSaveCustomRate: ((CurrencyModel, String) -> Void)?

  /// 恢复默认汇率回调
  let onRestoreDefaultRate: ((CurrencyModel) -> Void)?

  // MARK: - Initialization

  init(
    currency: CurrencyModel,
    dataManager: DataManagement,
    dismiss: @escaping () -> Void,
    onSetAsBaseCurrency: ((CurrencyModel) -> Void)? = nil,
    onSaveCustomRate: ((CurrencyModel, String) -> Void)? = nil,
    onRestoreDefaultRate: ((CurrencyModel) -> Void)? = nil
  ) {
    self.currency = currency
    self.dataManager = dataManager
    self.dismiss = dismiss
    self.onSetAsBaseCurrency = onSetAsBaseCurrency
    self.onSaveCustomRate = onSaveCustomRate
    self.onRestoreDefaultRate = onRestoreDefaultRate

    // 初始化自定义汇率文本
    if let customRate = currency.customRate {
      customRateText = formatRateWithSixDecimals(customRate)
    } else {
      customRateText = formatRateWithSixDecimals(currency.rate)
    }
  }

  // MARK: - Computed Properties

  /// 操作选项配置
  var operationOptions: [OperationOption] {
    var options: [OperationOption] = []

    // 设为本位币选项（只有非本位币才显示）
    if !currency.isBaseCurrency {
      options.append(
        OperationOption(
          title: "设为本位币",
          icon: "star.fill",
          action: .setAsBaseCurrency
        )
      )
    }

    // 自定义汇率选项（只有非本位币才显示）
    if !currency.isBaseCurrency {
      options.append(
        OperationOption(
          title: "自定义汇率",
          icon: "pencil",
          action: .customRate
        )
      )
    }

    // 恢复默认汇率选项（只有自定义汇率的非本位币才显示）
    if !currency.isBaseCurrency && currency.isCustom {
      options.append(
        OperationOption(
          title: "恢复默认",
          icon: "arrow.counterclockwise",
          action: .restoreDefault
        )
      )
    }

    return options
  }

  /// 当前汇率显示文本
  var currentRateText: String {
    return "1 \(currency.code) = \(currency.formattedRate()) \(baseCurrencyCode)"
  }

  /// 本位币代码
  private var baseCurrencyCode: String {
    UserDefaults.standard.string(forKey: "baseCurrencyCode") ?? "CNY"
  }

  /// 是否可以保存自定义汇率
  var canSaveCustomRate: Bool {
    guard !currency.isBaseCurrency,
      let newRate = Double(customRateText),
      newRate > 0
    else {
      return false
    }

    // 检查是否与当前汇率不同
    return abs(newRate - currency.rate) > 0.000001
  }

  // MARK: - Public Methods

  /// 处理操作选项点击
  /// - Parameter option: 被点击的操作选项
  @MainActor
  func handleOptionTap(_ option: OperationOption) {
    dataManager.hapticManager.trigger(.selection)

    switch option.action {
    case .setAsBaseCurrency:
      handleSetAsBaseCurrency()
    case .customRate:
      handleCustomRateInput()
    case .restoreDefault:
      handleRestoreDefaultRate()
    }
  }

  /// 处理关闭操作
  @MainActor
  func handleDismiss() {
    dataManager.hapticManager.trigger(.impactLight)
    dismiss()
  }

  /// 处理自定义汇率保存
  @MainActor
  func handleSaveCustomRate() {
    guard canSaveCustomRate else { return }

    dataManager.hapticManager.trigger(.impactMedium)
    onSaveCustomRate?(currency, customRateText)
    showNumericKeypad = false
    dismiss()
  }

  // MARK: - Private Methods

  /// 处理设为本位币
  private func handleSetAsBaseCurrency() {
    onSetAsBaseCurrency?(currency)
    dismiss()
  }

  /// 处理自定义汇率输入
  private func handleCustomRateInput() {
    // 如果自定义汇率文本为空，使用当前汇率初始化
    if customRateText.isEmpty {
      let displayRate = currency.rate > 0 ? currency.rate : currency.defaultRate
      customRateText = formatRateWithSixDecimals(displayRate)
    }
    showNumericKeypad = true
  }

  /// 处理恢复默认汇率
  private func handleRestoreDefaultRate() {
    onRestoreDefaultRate?(currency)
    dismiss()
  }

  /// 格式化汇率为6位小数
  private func formatRateWithSixDecimals(_ rate: Double) -> String {
    let formatted = String(format: "%.6f", rate)
    var result = formatted
    while result.hasSuffix("0") && result.contains(".") {
      result.removeLast()
    }
    if result.hasSuffix(".") {
      result.removeLast()
    }
    return result
  }
}

// MARK: - Supporting Types

/// 操作选项
struct OperationOption {
  let title: String
  let icon: String
  let action: OperationAction
}

/// 操作类型
enum OperationAction {
  case setAsBaseCurrency
  case customRate
  case restoreDefault
}
