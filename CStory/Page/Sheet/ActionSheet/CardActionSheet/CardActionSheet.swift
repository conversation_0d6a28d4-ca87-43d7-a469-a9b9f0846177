//
//  CardActionSheet.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 卡片操作弹窗
///
/// 提供卡片相关的操作选项，包括编辑、删除等功能。
/// 支持两种删除模式：删除卡片及其所有交易记录，或仅删除卡片本身。
/// 使用 MVVM 架构，通过 CardActionSheetVM 管理业务逻辑。
struct CardActionSheet: View {

  // MARK: - ViewModel

  /// 视图模型
  @StateObject private var viewModel: CardActionSheetVM

  // MARK: - 初始化

  /// 初始化卡片操作弹窗
  /// - Parameters:
  ///   - viewModel: 卡片操作弹窗视图模型
  init(viewModel: CardActionSheetVM) {
    self._viewModel = StateObject(wrappedValue: viewModel)
  }

  // MARK: - 主体

  var body: some View {
    VStack(spacing: 12) {
      // 标题栏
      SheetTitle(
        title: "卡片操作",
        button: "xmark.circle.fill",
        rightButtonAction: viewModel.handleDismiss
      )

      // MARK: 操作选项列表
      ForEach(Array(viewModel.actionOptions.enumerated()), id: \.offset) { index, option in
        SheetRow(
          title: option.title,
          icon: option.icon,
          isDestructive: option.isDestructive
        ) {
          option.action()
        }
      }

      Spacer()
    }
  }
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct CardActionSheet_Previews: PreviewProvider {
    static var previews: some View {
      CardActionPreviewContainer()
    }
  }

  struct CardActionPreviewContainer: View {
    @State private var showSheet = false

    var body: some View {
      VStack {
        Button("显示卡片操作菜单") {
          showSheet = true
        }
        .padding()
        .background(Color.cAccentBlue)
        .foregroundColor(.white)
        .cornerRadius(12)
      }
      .frame(maxWidth: .infinity, maxHeight: .infinity)
      .background(Color.cLightBlue)
      .floatingSheet(
        isPresented: $showSheet,
        config: SheetBase(
          maxDetent: .height(250),
          cornerRadius: 24,
          interactiveDimiss: true,
          hPadding: 8,
          bPadding: 4
        )
      ) {
        CardActionSheet(
          viewModel: CardActionSheetVM(
            dataManager: DataManagement(),
            onEditCard: { print("编辑卡片") },
            onDeleteWithTransactions: { print("删除卡片和交易") },
            onDeleteCardOnly: { print("仅删除卡片") },
            dismiss: { showSheet = false }
          )
        )
      }
    }
  }
#endif
