//
//  CardActionSheetVM.swift
//  CStory
//
//  Created by NZUE on 2025/8/6.
//

import Foundation
import SwiftUI

/// 卡片操作弹窗 ViewModel
///
/// 管理卡片操作弹窗的状态和逻辑，包括操作选项配置和用户交互处理。
/// 提供编辑、删除等操作的统一入口，并支持触觉反馈。
@MainActor
class CardActionSheetVM: ObservableObject {

  // MARK: - 操作选项定义

  /// 操作选项结构
  struct ActionOption {
    let title: String
    let icon: String
    let isDestructive: Bool
    let action: () -> Void
  }

  // MARK: - 属性

  /// 编辑卡片回调
  let onEditCard: () -> Void

  /// 删除卡片和交易记录回调
  let onDeleteWithTransactions: () -> Void

  /// 仅删除卡片回调
  let onDeleteCardOnly: () -> Void

  /// 关闭弹窗回调
  let dismiss: () -> Void

  /// 数据管理器
  private let dataManager: DataManagement

  // MARK: - 初始化

  init(
    dataManager: DataManagement,
    onEditCard: @escaping () -> Void,
    onDeleteWithTransactions: @escaping () -> Void,
    onDeleteCardOnly: @escaping () -> Void,
    dismiss: @escaping () -> Void
  ) {
    self.dataManager = dataManager
    self.onEditCard = onEditCard
    self.onDeleteWithTransactions = onDeleteWithTransactions
    self.onDeleteCardOnly = onDeleteCardOnly
    self.dismiss = dismiss
  }

  // MARK: - 计算属性

  /// 操作选项配置
  var actionOptions: [ActionOption] {
    [
      ActionOption(
        title: "编辑卡片",
        icon: "pencil",
        isDestructive: false,
        action: {
          self.dataManager.hapticManager.trigger(.impactLight)
          self.onEditCard()
          self.dismiss()
        }
      ),
      ActionOption(
        title: "删除卡片和交易记录",
        icon: "trash",
        isDestructive: true,
        action: {
          self.dataManager.hapticManager.trigger(.warning)
          self.onDeleteWithTransactions()
          self.dismiss()
        }
      ),
      ActionOption(
        title: "仅删除卡片",
        icon: "trash",
        isDestructive: true,
        action: {
          self.dataManager.hapticManager.trigger(.warning)
          self.onDeleteCardOnly()
          self.dismiss()
        }
      ),
    ]
  }

  // MARK: - 公共方法

  /// 处理关闭操作
  func handleDismiss() {
    dataManager.hapticManager.trigger(.impactLight)
    dismiss()
  }
}
