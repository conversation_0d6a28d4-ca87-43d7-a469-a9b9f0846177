//
//  CategoryActionSheetVM.swift
//  CStory
//
//  Created by NZUE on 2025/8/6.
//

import Foundation
import SwiftUI

/// 交易分类操作弹窗 ViewModel
///
/// 管理交易分类操作弹窗的状态和逻辑，包括编辑分类信息、排序类别、
/// 迁移账单到其他分类、删除分类等功能的统一管理。
@MainActor
class CategoryActionSheetVM: ObservableObject {

  // MARK: - 操作选项定义

  /// 操作选项结构
  struct ActionOption {
    let title: String
    let icon: String
    let isDestructive: Bool
    let action: () -> Void
  }

  // MARK: - 属性

  /// 编辑分类回调
  let onEdit: () -> Void

  /// 排序类别回调
  let onSort: () -> Void

  /// 迁移账单回调
  let onMigrate: () -> Void

  /// 删除分类回调
  let onDelete: () -> Void

  /// 关闭弹窗回调
  let dismiss: () -> Void

  /// 数据管理器
  private let dataManager: DataManagement

  // MARK: - 初始化

  init(
    dataManager: DataManagement,
    onEdit: @escaping () -> Void,
    onSort: @escaping () -> Void,
    onMigrate: @escaping () -> Void,
    onDelete: @escaping () -> Void,
    dismiss: @escaping () -> Void
  ) {
    self.dataManager = dataManager
    self.onEdit = onEdit
    self.onSort = onSort
    self.onMigrate = onMigrate
    self.onDelete = onDelete
    self.dismiss = dismiss
  }

  // MARK: - 计算属性

  /// 操作选项配置
  var actionOptions: [ActionOption] {
    [
      ActionOption(
        title: "编辑类别",
        icon: "pencil",
        isDestructive: false,
        action: {
          self.dataManager.hapticManager.trigger(.impactLight)
          self.onEdit()
          self.dismiss()
        }
      ),
      ActionOption(
        title: "类别排序",
        icon: "arrow.up.arrow.down",
        isDestructive: false,
        action: {
          self.dataManager.hapticManager.trigger(.impactLight)
          self.onSort()
          self.dismiss()
        }
      ),
      ActionOption(
        title: "迁移账单",
        icon: "arrow.triangle.2.circlepath",
        isDestructive: false,
        action: {
          self.dataManager.hapticManager.trigger(.impactMedium)
          self.onMigrate()
          self.dismiss()
        }
      ),
      ActionOption(
        title: "删除类别",
        icon: "trash",
        isDestructive: true,
        action: {
          self.dataManager.hapticManager.trigger(.warning)
          self.onDelete()
          self.dismiss()
        }
      ),
    ]
  }

  // MARK: - 公共方法

  /// 处理关闭操作
  func handleDismiss() {
    dataManager.hapticManager.trigger(.impactLight)
    dismiss()
  }
}
