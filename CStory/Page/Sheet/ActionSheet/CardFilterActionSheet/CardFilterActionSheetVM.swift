//
//  CardFilterActionSheetVM.swift
//  CStory
//
//  Created by NZUE on 2025/8/6.
//

import Foundation
import SwiftUI

/// 卡片筛选操作弹窗 ViewModel
///
/// 管理卡片类型筛选功能，支持显示所有卡片、仅储蓄卡或仅信用卡。
/// 提供筛选选项的状态管理和用户交互处理。
class CardFilterActionSheetVM: ObservableObject {

  // MARK: - 筛选选项定义

  /// 筛选选项结构
  struct FilterOption {
    let title: String
    let filter: Bool?
    let icon: String
  }

  // MARK: - 属性

  /// 当前选中的筛选条件
  /// - nil: 显示所有卡片
  /// - false: 仅显示储蓄卡
  /// - true: 仅显示信用卡
  @Binding var selectedFilter: Bool?

  /// 关闭弹窗回调
  let dismiss: () -> Void

  /// 筛选条件改变回调
  let onFilterChanged: ((Bool?) -> Void)?

  /// 数据管理器
  private let dataManager: DataManagement

  // MARK: - 初始化

  init(
    dataManager: DataManagement,
    selectedFilter: Binding<Bool?>,
    dismiss: @escaping () -> Void,
    onFilterChanged: ((Bool?) -> Void)? = nil
  ) {
    self.dataManager = dataManager
    self._selectedFilter = selectedFilter
    self.dismiss = dismiss
    self.onFilterChanged = onFilterChanged
  }

  // MARK: - 计算属性

  /// 筛选选项配置
  let filterOptions: [FilterOption] = [
    FilterOption(title: "所有卡片", filter: nil, icon: "creditcard.fill"),
    FilterOption(title: "储蓄卡", filter: false, icon: "banknote.fill"),
    FilterOption(title: "信用卡", filter: true, icon: "creditcard"),
  ]

  // MARK: - 公共方法

  /// 处理选项点击
  /// - Parameter option: 被点击的筛选选项
  @MainActor
  func handleOptionTap(_ option: FilterOption) {
    // 触觉反馈
    dataManager.hapticManager.trigger(.selection)

    if let onFilterChanged = onFilterChanged {
      // 如果提供了回调，使用回调处理
      onFilterChanged(option.filter)
    } else {
      // 否则直接更新绑定值并关闭
      selectedFilter = option.filter
      dismiss()
    }
  }

  /// 处理关闭操作
  @MainActor
  func handleDismiss() {
    dataManager.hapticManager.trigger(.impactLight)
    dismiss()
  }

  /// 检查选项是否被选中
  /// - Parameter option: 要检查的筛选选项
  /// - Returns: 是否被选中
  func isOptionSelected(_ option: FilterOption) -> Bool {
    return option.filter == selectedFilter
  }
}
