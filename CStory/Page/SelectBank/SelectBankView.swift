//
//  SelectBankView.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/19.
//

import SwiftUI

/// 银行选择测试视图
///
/// 参考正式项目SelectBankView的UI结构，使用SelectBankTestVM处理业务逻辑。
/// 完全复制原版的UI样式、搜索功能、分组列表和交互逻辑。
struct SelectBankView: View {

  // MARK: - 属性

  @ObservedObject private var viewModel: SelectBankVM
  @Environment(\.dismiss) private var dismiss
  @EnvironmentObject private var pathManager: PathManagerHelper

  // MARK: - 初始化

  init(viewModel: SelectBankVM, isCredit: Bool, mainCategory: CardCategoryResponse) {
    self.viewModel = viewModel
  }

  // MARK: - 主体视图

  var body: some View {
    VStack(spacing: 0) {
      // 顶部导航栏
      NavigationBarKit.backOnly(title: "选择\(viewModel.mainCategory.name)") {
        dismiss()
      }

      // 搜索栏
      SearchBarKit(
        searchText: $viewModel.searchText,
        placeholder: "搜索银行名称"
      )

      // 银行列表内容
      bankListContent
    }
    .overlay {
      // 卡片创建overlay（占位实现）
      if viewModel.showingCardDetail, let selectedBank = viewModel.selectedSubCategory {
        cardCreationOverlay(bank: selectedBank)
      }
    }
    .navigationBarTitleDisplayMode(.inline)
    .navigationBarBackButtonHidden(true)
    .background(Color.cLightBlue)

  }

  // MARK: - 子视图构建器

  /// 银行列表内容
  private var bankListContent: some View {
    Group {
      if !viewModel.isDataLoaded {
        // 加载状态
        loadingView
      } else {
        // 银行列表
        bankScrollView
      }
    }
  }

  /// 加载视图
  private var loadingView: some View {
    VStack {
      ProgressView()
        .scaleEffect(1.2)
      Text("加载银行列表...")
        .font(.system(size: 16))
        .foregroundColor(.cBlack.opacity(0.6))
        .padding(.top, 8)
    }
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .background(Color.cLightBlue)
  }

  /// 银行滚动视图
  private var bankScrollView: some View {
    ScrollViewReader { proxy in
      ScrollView {
        LazyVStack(spacing: 0, pinnedViews: [.sectionHeaders]) {
          if viewModel.searchText.isEmpty {
            // 显示分组列表
            ForEach(viewModel.sectionTitles, id: \.self) { section in
              if let banksInSection = viewModel.groupedBanks[section], !banksInSection.isEmpty {
                Section(header: sectionHeaderView(section)) {
                  ForEach(banksInSection, id: \.name) { bank in
                    BankRowTestView(
                      bank: bank,
                      bankImage: viewModel.bankImages[bank.displayName],
                      defaultBankIcon: viewModel.getBankImageData(for: bank),
                      action: {
                        viewModel.handleBankSelection(bank)
                      },
                      dataManager: viewModel.dataManager
                    )
                    .task {
                      if viewModel.bankImages[bank.displayName] == nil {
                        await viewModel.loadBankImage(for: bank)
                      }
                    }
                  }
                }
                .id(section)
              }
            }
          } else {
            // 搜索结果
            searchResultsView
          }
        }
      }
      .background(Color.cLightBlue)
      .overlay(alignment: .trailing) {
        // A-Z 索引条
        if viewModel.searchText.isEmpty {
          SectionIndexTestView(
            sectionTitles: viewModel.sectionTitles,
            indexLetters: viewModel.indexLetters,
            onSectionSelected: { section in
              withAnimation(.easeInOut(duration: 0.2)) {
                proxy.scrollTo(section, anchor: .top)
              }
            },
            dataManager: viewModel.dataManager
          )
          .padding(.trailing, 4)
        }
      }
    }
  }

  /// 搜索结果视图
  private var searchResultsView: some View {
    Group {
      if viewModel.filteredBanks.isEmpty {
        VStack {
          Image(systemName: "magnifyingglass")
            .font(.system(size: 40))
            .foregroundColor(.cBlack.opacity(0.3))
          Text("未找到相关银行")
            .font(.system(size: 16))
            .foregroundColor(.cBlack.opacity(0.6))
            .padding(.top, 8)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.top, 100)
      } else {
        ForEach(viewModel.filteredBanks, id: \.name) { bank in
          BankRowTestView(
            bank: bank,
            bankImage: viewModel.bankImages[bank.displayName],
            defaultBankIcon: viewModel.getBankImageData(for: bank),
            action: {
              viewModel.handleBankSelection(bank)
            },
            dataManager: viewModel.dataManager
          )
          .task {
            if viewModel.bankImages[bank.displayName] == nil {
              await viewModel.loadBankImage(for: bank)
            }
          }
        }
      }
    }
  }

  /// 分组标题视图
  private func sectionHeaderView(_ title: String) -> some View {
    HStack {
      Text(title)
        .font(.system(size: 15, weight: .semibold))
        .foregroundColor(.cBlack.opacity(0.8))
        .textCase(nil)
      Spacer()
    }
    .padding(.horizontal, 20)
    .padding(.vertical, 8)
    .background(
      Color.cLightBlue.opacity(0.8)
        .background(.ultraThinMaterial)
    )
  }

  /// 卡片创建overlay（使用正式的CardDetailView）
  private func cardCreationOverlay(bank: CardSubCategoryResponse) -> some View {
    CardDetailView(
      card: nil,
      cardNamespace: nil,
      showingCardDetail: $viewModel.showingCardDetail,
      pathManager: pathManager,
      transactions: viewModel.dataManager.allTransactions,
      currencies: viewModel.dataManager.currencies,
      animationMode: .createCard,
      isCreatingCard: true,
      isCredit: viewModel.isCredit,
      mainCategory: viewModel.mainCategory,
      subCategory: bank,
      onCardCreated: { newCard in
        print("✅ 卡片创建成功: \(newCard.name)")
        // 创建完成后返回到入口页面（移除SelectBankView和CardCategoryView）
        pathManager.path.removeLast(2)
      },
      dataManager: viewModel.dataManager
    )
  }
}

// MARK: - 支持组件

/// 银行行视图组件（测试版）
struct BankRowTestView: View {
  let bank: CardSubCategoryResponse
  let bankImage: Data?
  let defaultBankIcon: Data?
  let action: () -> Void
  let dataManager: DataManagement

  var body: some View {
    Button(action: {
      dataManager.hapticManager.trigger(.impactLight)
      action()
    }) {
      HStack(spacing: 15) {
        // 银行图标
        Group {
          if let imageData = bankImage,
            let uiImage = UIImage(data: imageData)
          {
            Image(uiImage: uiImage)
              .resizable()
              .scaledToFit()
          } else if let defaultData = defaultBankIcon,
            let defaultImage = UIImage(data: defaultData)
          {
            Image(uiImage: defaultImage)
              .resizable()
              .scaledToFit()
          } else {
            Rectangle()
              .fill(Color.cAccentBlue.opacity(0.1))
              .overlay(
                Image(systemName: "building.columns")
                  .font(.system(size: 18, weight: .medium))
                  .foregroundColor(.cAccentBlue.opacity(0.6))
              )
          }
        }
        .frame(width: 44, height: 44)
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .background(
          RoundedRectangle(cornerRadius: 12)
            .fill(Color.cBlack).opacity(0.02)
        )

        // 银行名称
        Text(bank.displayName)
          .font(.system(size: 17, weight: .regular))
          .foregroundColor(.cBlack)
          .multilineTextAlignment(.leading)
          .lineLimit(1)

        Spacer()
      }
      .padding(.horizontal, 20)
      .padding(.vertical, 14)
      .background(Color.cWhite)
      .contentShape(Rectangle())
      .overlay(
        // 底部分隔线
        Rectangle()
          .fill(Color.cBlack.opacity(0.05))
          .frame(height: 0.5)
          .padding(.leading, 79),  // 对齐银行名称
        alignment: .bottom
      )
    }
    .buttonStyle(PlainButtonStyle())
  }
}

/// A-Z 索引视图组件（测试版）
struct SectionIndexTestView: View {
  let sectionTitles: [String]
  let indexLetters: [String]
  let onSectionSelected: (String) -> Void
  let dataManager: DataManagement

  @State private var dragLocation: CGPoint = .zero
  @State private var isDragging = false
  @State private var lastSelectedIndex = -1

  var body: some View {
    VStack(spacing: 1) {
      ForEach(Array(indexLetters.enumerated()), id: \.offset) { index, letter in
        Text(letter)
          .font(.system(size: 11, weight: .medium))
          .foregroundColor(
            sectionTitles.contains(letter)
              ? Color.cAccentBlue
              : Color.cBlack.opacity(0.4)
          )
          .frame(width: 20, height: 18)
          .background(
            // 高亮当前选中的字母
            isDragging && getCurrentIndex() == index
              ? Color.cAccentBlue.opacity(0.2)
              : Color.clear
          )
          .cornerRadius(9)
          .scaleEffect(
            isDragging && getCurrentIndex() == index ? 1.2 : 1.0
          )
          .animation(.easeInOut(duration: 0.1), value: isDragging)
          .animation(.easeInOut(duration: 0.1), value: getCurrentIndex())
      }
    }
    .padding(.vertical, 8)
    .padding(.horizontal, 4)
    .background(
      RoundedRectangle(cornerRadius: 12)
        .fill(Color.cWhite.opacity(0.8))
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    )
    .gesture(
      DragGesture(minimumDistance: 0)
        .onChanged { value in
          dragLocation = value.location
          if !isDragging {
            isDragging = true
          }

          let currentIndex = getCurrentIndex()
          if currentIndex != lastSelectedIndex && currentIndex >= 0
            && currentIndex < indexLetters.count
          {
            let letter = indexLetters[currentIndex]
            if sectionTitles.contains(letter) {
              dataManager.hapticManager.trigger(.impactLight)
              onSectionSelected(letter)
              lastSelectedIndex = currentIndex
            }
          }
        }
        .onEnded { _ in
          isDragging = false
          lastSelectedIndex = -1
        }
    )
    .onTapGesture { location in
      dragLocation = location
      let currentIndex = getCurrentIndex()
      if currentIndex >= 0 && currentIndex < indexLetters.count {
        let letter = indexLetters[currentIndex]
        if sectionTitles.contains(letter) {
          dataManager.hapticManager.trigger(.impactLight)
          onSectionSelected(letter)
        }
      }
    }
  }

  private func getCurrentIndex() -> Int {
    let itemHeight: CGFloat = 19  // 18 + 1 spacing
    let totalHeight = CGFloat(indexLetters.count) * itemHeight
    let relativeY = dragLocation.y - 8  // 减去 padding

    if relativeY < 0 { return 0 }
    if relativeY >= totalHeight { return indexLetters.count - 1 }

    return Int(relativeY / itemHeight)
  }
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct SelectBankView_Previews: PreviewProvider {
    static var previews: some View {
      let mainCategory = CardCategoryResponse(
        id: 1,
        name: "银行卡",
        imageUrl: "银行卡_icon",
        subCategories: []
      )
      SelectBankView(
        viewModel: SelectBankVM(
          isCredit: false,
          mainCategory: mainCategory,
          dataManager: DataManagement()
        ),
        isCredit: false,
        mainCategory: mainCategory
      )
    }
  }
#endif
