//
//  CardCategoryTestVM.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/19.
//

import Foundation
import SwiftUI

/// 卡片分类测试视图模型
///
/// 核心逻辑：
/// 1. 从JSON文件加载卡片分类数据
/// 2. 管理信用卡/储蓄卡类型切换
/// 3. 处理分类选择逻辑
/// 4. 提供UI所需的格式化数据
final class CardCategoryVM: ObservableObject {

  // MARK: - 数据源

  /// 卡片类别数据
  @Published var categories: CardCategoriesResponse?

  // MARK: - UI状态

  /// 当前选中的资产类型（true=信用卡，false=储蓄卡）
  @Published var isCredit: Bool = false

  /// 选中的分类（用于创建卡片）
  @Published var selectedCategory: CardCategoryResponse?

  /// 是否显示卡片创建详情
  @Published var showingCardDetail: Bool = false

  // MARK: - Dependencies

  /// 数据管理器，提供统一的数据访问接口
  let dataManager: DataManagement

  // MARK: - 计算属性

  /// 当前显示的分类列表
  var currentCategories: [CardCategoryResponse] {
    guard let categories = categories else { return [] }
    return isCredit ? categories.creditCategories : categories.savingCategories
  }

  // MARK: - 初始化

  /// 初始化卡片分类视图模型
  ///
  /// - Parameter dataManager: 数据管理器
  init(dataManager: DataManagement) {
    self.dataManager = dataManager
    loadCategories()
  }

  // MARK: - 公共方法

  /// 更新卡片类型
  /// - Parameter creditType: 是否为信用卡
  @MainActor
  func updateCardType(_ creditType: Bool) {
    if isCredit != creditType {
      withAnimation(.easeInOut(duration: 0.2)) {
        isCredit = creditType
        dataManager.hapticManager.trigger(.selection)
      }
    }
  }

  /// 处理分类选择
  /// - Parameter category: 选中的分类
  /// - Parameter pathManager: 导航路径管理器
  @MainActor
  func handleCategorySelection(_ category: CardCategoryResponse, pathManager: PathManagerHelper) {
    dataManager.hapticManager.trigger(.selection)

    // 只有银行卡和信用卡才需要选择子分类，跳转到银行选择页面
    if category.name == "银行卡" || category.name == "信用卡" {
      pathManager.path.append(
        NavigationDestination.selectBankView(
          isCredit: isCredit,
          mainCategory: category
        )
      )
    } else {
      // 其他类别直接创建卡片
      selectedCategory = category
      withAnimation(.interactiveSpring(response: 0.3, dampingFraction: 0.8, blendDuration: 0.4)) {
        showingCardDetail = true
      }
    }
  }

  /// 关闭卡片创建详情
  func dismissCardDetail() {
    withAnimation(.interactiveSpring(response: 0.3, dampingFraction: 0.8, blendDuration: 0.4)) {
      showingCardDetail = false
    }
    // 重置选中的分类
    selectedCategory = nil
  }

  // MARK: - 私有方法

  /// 加载类别数据
  private func loadCategories() {
    categories = CardCategoryJSONDecoder.decode(from: "CardsCategoryDefaults")
  }
}
