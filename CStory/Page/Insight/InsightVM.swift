//
//  InsightVM.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/21.
//

import Charts
import Combine
import SwiftUI

// MARK: - Data Models

/// 资产数据点模型
/// 用于表示资产变动图表中的单个数据点
struct CardDataPoint: Identifiable {
  /// 唯一标识符
  let id = UUID()
  /// 数据点日期
  let date: Date
  /// 数据点金额
  let amount: Double
}

/// 交易数据点模型
/// 用于表示交易变动图表中的单个数据点
struct TransactionDataPoint: Identifiable {
  /// 唯一标识符
  let id = UUID()
  /// 数据点日期
  let date: Date
  /// 数据点金额
  let amount: Double
}

/// 收支统计数据模型
/// 用于表示当前时间段的收支统计信息
struct FinancialSummary {
  /// 总支出
  let totalExpense: Double
  /// 总收入
  let totalIncome: Double
  /// 结余（收入-支出）
  let balance: Double
  /// 记账次数
  let transactionCount: Int
}

/// 分类统计数据模型
/// 用于表示分类占比信息
struct CategoryStat: Identifiable {
  /// 唯一标识符
  let id = UUID()
  /// 分类名称
  let name: String
  /// 分类金额
  let amount: Double
  /// 分类占比（0-1）
  let percentage: Double
  /// 分类颜色
  let color: Color
}

// MARK: - Enums

/// 交易图表类型
/// 定义了交易图表可以展示的不同数据维度
enum TransactionChartType {
  case expense
  /// 支出数据
  case income
  /// 收入数据
  case netFlow
  /// 净流入数据

  /// 图表颜色
  var color: Color {
    switch self {
    case .expense: return Color.cAccentRed
    case .income: return Color.cAccentGreen
    case .netFlow: return Color.cAccentBlue
    }
  }

  /// 图表标题
  var title: String {
    switch self {
    case .expense: return "支出"
    case .income: return "收入"
    case .netFlow: return "净流入"
    }
  }
}

/// 资产图表类型
/// 定义了资产图表可以展示的不同数据维度
enum CardChartType {
  case total
  /// 总资产数据
  case debt
  /// 负债数据
  case net
  /// 净资产数据

  /// 图表颜色
  var color: Color {
    switch self {
    case .total: return Color.cAccentGreen
    case .debt: return Color.cAccentRed
    case .net: return Color.cAccentBlue
    }
  }

  /// 图表标题
  var title: String {
    switch self {
    case .total: return "总资产"
    case .debt: return "负债"
    case .net: return "净资产"
    }
  }
}

/// 分类显示模式枚举
enum CategoryDisplayMode: String, CaseIterable {
  case expense = "支出分类"
  case income = "收入分类"
  case all = "全部占比"

  var icon: String {
    switch self {
    case .expense: return "minus.circle.fill"
    case .income: return "plus.circle.fill"
    case .all: return "chart.pie.fill"
    }
  }

  var color: Color {
    switch self {
    case .expense: return Color.cAccentRed
    case .income: return Color.cAccentGreen
    case .all: return Color.cAccentBlue
    }
  }
}

/// 数据分析页面 ViewModel
///
/// 符合 New 架构的 MVVM 模式，使用 DataManagement 作为数据源
/// 负责处理所有数据分析相关的业务逻辑和数据格式化
@MainActor
class InsightVM: ObservableObject {

  // MARK: - Dependencies

  private let dataManager: DataManagement

  // MARK: - Published Properties

  @Published var isLoading = false
  @Published var errorMessage: String?

  // 时间选择相关 - 使用 TimeControlVM 管理
  @Published var timeControlVM: TimeControlVM

  // 财务摘要数据
  @Published var financialSummary = FinancialSummary(
    totalExpense: 0,
    totalIncome: 0,
    balance: 0,
    transactionCount: 0
  )

  // 图表数据
  @Published var totalCardPoints: [CardDataPoint] = []
  @Published var debtPoints: [CardDataPoint] = []
  @Published var netCardPoints: [CardDataPoint] = []
  @Published var expensePoints: [TransactionDataPoint] = []
  @Published var incomePoints: [TransactionDataPoint] = []
  @Published var flowPoints: [TransactionDataPoint] = []

  // 分类统计数据
  @Published var expenseStats: [CategoryStat] = []
  @Published var incomeStats: [CategoryStat] = []

  // UI 状态
  @Published var selectedCardType: CardChartType = .total
  @Published var selectedTransactionType: TransactionChartType = .expense
  @Published var categoryDisplayMode: CategoryDisplayMode = .expense
  @Published var selectedCardPoint: CardDataPoint?
  @Published var selectedTransactionPoint: TransactionDataPoint?
  @Published var animationProgress: Double = 0.0

  // MARK: - Private Properties

  private var cancellables = Set<AnyCancellable>()

  // MARK: - Computed Properties

  /// 当前日期范围（使用时间控制器的计算结果）
  var dateRange: (start: Date, end: Date) {
    return timeControlVM.dateRange
  }

  /// 当前选择的时间周期（从时间控制器获取）
  var selectedPeriod: TransactionTimePeriod {
    return timeControlVM.selectedPeriod
  }

  /// 当前选择的日期（从时间控制器获取）
  var currentDate: Date {
    return timeControlVM.currentDate
  }

  /// 当前卡片图表数据
  var currentCardData: [CardDataPoint] {
    switch selectedCardType {
    case .total: return totalCardPoints
    case .debt: return debtPoints
    case .net: return netCardPoints
    }
  }

  /// 当前交易图表数据
  var currentTransactionData: [TransactionDataPoint] {
    switch selectedTransactionType {
    case .expense: return expensePoints
    case .income: return incomePoints
    case .netFlow: return flowPoints
    }
  }

  /// 当前分类统计数据
  var currentCategoryStats: [CategoryStat] {
    switch categoryDisplayMode {
    case .expense: return expenseStats
    case .income: return incomeStats
    case .all: return combineAllStats()
    }
  }

  // MARK: - Initialization

  init(dataManager: DataManagement) {
    self.dataManager = dataManager

    // 初始化时间控制器 - 先不设置回调
    self.timeControlVM = TimeControlVM(selectedPeriod: .month, currentDate: Date()) { _, _ in }

    // 设置时间控制器的回调
    self.timeControlVM.onDateChange = { [weak self] date, period in
      self?.loadData()
    }

    setupDataObservers()
    loadData()
  }

  // MARK: - Public Methods

  /// 加载数据
  func loadData() {
    isLoading = true
    errorMessage = nil

    Task {
      await loadCardData()
      await loadTransactionData()
      await loadFinancialSummary()
      await loadCategoryStats()

      isLoading = false
    }
  }

  /// 更新选中的数据点
  func updateSelectedPoints() {
    if let latestCardPoint = currentCardData.last {
      selectedCardPoint = latestCardPoint
    }
    if let latestTransactionPoint = currentTransactionData.last {
      selectedTransactionPoint = latestTransactionPoint
    }
  }

  /// 切换卡片图表类型
  func selectCardType(_ type: CardChartType) {
    selectedCardType = type
    if let currentDate = selectedCardPoint?.date {
      selectedCardPoint = currentCardData.first {
        Calendar.current.isDate($0.date, inSameDayAs: currentDate)
      }
    }
  }

  /// 切换交易图表类型
  func selectTransactionType(_ type: TransactionChartType) {
    selectedTransactionType = type
    if let currentDate = selectedTransactionPoint?.date {
      selectedTransactionPoint = currentTransactionData.first {
        Calendar.current.isDate($0.date, inSameDayAs: currentDate)
      }
    }
  }

  /// 切换分类显示模式
  func selectCategoryMode(_ mode: CategoryDisplayMode) {
    categoryDisplayMode = mode
  }

  // MARK: - Private Methods

  /// 设置数据观察者
  private func setupDataObservers() {
    // 由于 DataManagement 使用 @Observable，数据变化会通过外部重新创建 ViewModel 来处理
    // 这里暂时不需要设置观察者，数据更新由上层视图控制
  }

  /// 加载卡片数据
  private func loadCardData() async {
    let cards = dataManager.cards
    let transactions = dataManager.allTransactions
    let currencies = dataManager.currencies
    let dateRange = self.dateRange
    let period = selectedPeriod

    let calendar = Calendar.current
    let (startDate, endDate) = dateRange

    print("\n=== 资产变动计算调试信息 ===")
    print(
      "⏰ 时间范围: \(startDate.formatted(.dateTime.day().month().year())) 至 \(endDate.formatted(.dateTime.day().month().year()))"
    )

    var tempTotalPoints: [CardDataPoint] = []
    var tempDebtPoints: [CardDataPoint] = []
    var tempNetPoints: [CardDataPoint] = []

    // 使用新的资产计算服务
    let currentResult = AssetCalculationService.shared.calculateAssets(
      cards: cards,
      currencies: currencies,
      baseCurrencyCode: UserDefaults.standard.string(forKey: "baseCurrencyCode") ?? "CNY"
    )
    let currentTotalCards = currentResult.totalAsset
    let currentDebts = currentResult.totalLiability

    print(
      "💰 当前总资产: \(currentTotalCards), 当前总负债: \(currentDebts), 净资产: \(currentTotalCards - currentDebts)"
    )

    var currentDate = startDate
    while currentDate < endDate {
      if currentDate > Date() {
        // 未来日期直接使用当前值
        tempTotalPoints.append(CardDataPoint(date: currentDate, amount: currentTotalCards))
        tempDebtPoints.append(CardDataPoint(date: currentDate, amount: currentDebts))
        tempNetPoints.append(
          CardDataPoint(date: currentDate, amount: currentTotalCards - currentDebts))

        print(
          "📅 未来日期 \(currentDate.formatted(.dateTime.day().month())): 直接使用当前值 - 资产:\(currentTotalCards) 负债:\(currentDebts)"
        )
      } else {
        // 为历史日期计算资产数据，使用 BalanceRecalculationService 的统一逻辑
        print("🕰️ 计算历史日期 \(currentDate.formatted(.dateTime.day().month())) 的资产状况")

        let result = BalanceRecalculationService.shared.calculateGlobalAssetsAtTimePoint(
          cards: cards,
          targetDate: currentDate,
          transactions: transactions,
          currencies: currencies
        )

        let historicalAssets = result.totalAssets
        let historicalDebts = result.totalDebts

        tempTotalPoints.append(CardDataPoint(date: currentDate, amount: historicalAssets))
        tempDebtPoints.append(CardDataPoint(date: currentDate, amount: historicalDebts))
        tempNetPoints.append(
          CardDataPoint(date: currentDate, amount: historicalAssets - historicalDebts))

        print("✅ 历史资产计算完成 - 资产:\(historicalAssets) 负债:\(historicalDebts) 净资产:\(result.netAssets)")
      }

      switch period {
      case .week, .month:
        currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate)!
      case .year:
        currentDate = calendar.date(byAdding: .month, value: 1, to: currentDate)!
      }
    }

    totalCardPoints = tempTotalPoints
    debtPoints = tempDebtPoints
    netCardPoints = tempNetPoints

    print("=== 资产变动计算完成 ✅ ===")
  }

  /// 加载交易数据
  private func loadTransactionData() async {
    let transactions = dataManager.allTransactions
    let dateRange = self.dateRange
    let period = selectedPeriod

    let calendar = Calendar.current
    let (startDate, endDate) = dateRange

    var dailyExpense: [Date: Double] = [:]
    var dailyIncome: [Date: Double] = [:]

    let dateComponents: Set<Calendar.Component> =
      period == .year ? [.year, .month] : [.year, .month, .day]

    // 获取本位币代码
    let baseCurrencyCode = UserDefaults.standard.string(forKey: "baseCurrencyCode") ?? "CNY"

    for transaction in transactions {
      guard transaction.transactionDate >= startDate && transaction.transactionDate < endDate else {
        continue
      }
      let transactionDate = calendar.date(
        from: calendar.dateComponents(dateComponents, from: transaction.transactionDate))!

      // 使用统一的汇率转换服务，转换到本位币
      let amounts = TransactionCalculationService.shared.getTransactionIncomeExpenseAmounts(
        transaction: transaction,
        targetCurrency: baseCurrencyCode
      )

      // 累加到对应日期
      dailyExpense[transactionDate, default: 0] += amounts.expense
      dailyIncome[transactionDate, default: 0] += amounts.income
    }

    var tempExpensePoints: [TransactionDataPoint] = []
    var tempIncomePoints: [TransactionDataPoint] = []
    var tempFlowPoints: [TransactionDataPoint] = []

    var currentDate = startDate
    while currentDate < endDate {
      let expense = dailyExpense[currentDate, default: 0]
      let income = dailyIncome[currentDate, default: 0]
      let netFlow = income - expense

      tempExpensePoints.append(TransactionDataPoint(date: currentDate, amount: expense))
      tempIncomePoints.append(TransactionDataPoint(date: currentDate, amount: income))
      tempFlowPoints.append(TransactionDataPoint(date: currentDate, amount: netFlow))

      switch period {
      case .week, .month:
        currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate)!
      case .year:
        currentDate = calendar.date(byAdding: .month, value: 1, to: currentDate)!
      }
    }

    expensePoints = tempExpensePoints
    incomePoints = tempIncomePoints
    flowPoints = tempFlowPoints
  }

  /// 加载财务摘要
  private func loadFinancialSummary() async {
    let transactions = dataManager.allTransactions
    let dateRange = self.dateRange

    let (startDate, endDate) = dateRange
    // 按日期范围过滤交易
    let periodTransactions = transactions.filter {
      $0.transactionDate >= startDate && $0.transactionDate < endDate
    }

    var totalExpense: Double = 0
    var totalIncome: Double = 0
    var transactionCount = 0

    // 获取本位币代码
    let baseCurrencyCode = UserDefaults.standard.string(forKey: "baseCurrencyCode") ?? "CNY"

    for transaction in periodTransactions {
      // 使用统一的汇率转换服务，转换到本位币
      let amounts = TransactionCalculationService.shared.getTransactionIncomeExpenseAmounts(
        transaction: transaction,
        targetCurrency: baseCurrencyCode
      )

      totalIncome += amounts.income
      totalExpense += amounts.expense

      // 只有收入和支出交易计入交易次数
      switch transaction.transactionType {
      case .expense, .income:
        transactionCount += 1
      case .transfer, .refund, .createCard, .adjustCard:
        break
      }
    }

    let balance = totalIncome - totalExpense

    financialSummary = FinancialSummary(
      totalExpense: totalExpense,
      totalIncome: totalIncome,
      balance: balance,
      transactionCount: transactionCount
    )
  }

  /// 加载分类统计
  private func loadCategoryStats() async {
    let transactions = dataManager.allTransactions
    let mainCategories = dataManager.mainCategories
    let subCategories = dataManager.subCategories
    let dateRange = self.dateRange

    let (startDate, endDate) = dateRange
    // 按日期范围过滤交易
    let periodTransactions = transactions.filter {
      $0.transactionDate >= startDate && $0.transactionDate < endDate
    }

    // 计算支出分类统计
    var expenseByCategory: [String: Double] = [:]
    var incomeByCategory: [String: Double] = [:]

    // 获取本位币代码
    let baseCurrencyCode = UserDefaults.standard.string(forKey: "baseCurrencyCode") ?? "CNY"

    for transaction in periodTransactions {
      let categoryName = getCategoryName(
        for: transaction, mainCategories: mainCategories, subCategories: subCategories)

      // 使用统一的汇率转换服务，转换到本位币
      let amounts = TransactionCalculationService.shared.getTransactionIncomeExpenseAmounts(
        transaction: transaction,
        targetCurrency: baseCurrencyCode
      )

      // 只统计实际的收入和支出交易
      switch transaction.transactionType {
      case .expense:
        expenseByCategory[categoryName, default: 0] += amounts.expense
      case .income:
        incomeByCategory[categoryName, default: 0] += amounts.income
      case .transfer, .refund, .createCard, .adjustCard:
        break
      }
    }

    // 生成支出分类统计
    let totalExpense = expenseByCategory.values.reduce(0, +)
    if totalExpense > 0 {
      let colors = [
        Color.cAccentRed, Color.cAccentBlue, Color.cAccentGreen, Color.orange,
        Color.purple,
      ]
      expenseStats = Array(
        expenseByCategory.sorted { $0.value > $1.value }.enumerated().map { index, item in
          CategoryStat(
            name: item.key,
            amount: item.value,
            percentage: item.value / totalExpense,
            color: colors[index % colors.count]
          )
        })
    } else {
      expenseStats = []
    }

    // 生成收入分类统计
    let totalIncome = incomeByCategory.values.reduce(0, +)
    if totalIncome > 0 {
      let colors = [
        Color.cAccentGreen, Color.cAccentBlue, Color.cAccentRed, Color.orange,
        Color.purple,
      ]
      incomeStats = Array(
        incomeByCategory.sorted { $0.value > $1.value }.enumerated().map { index, item in
          CategoryStat(
            name: item.key,
            amount: item.value,
            percentage: item.value / totalIncome,
            color: colors[index % colors.count]
          )
        })
    } else {
      incomeStats = []
    }
  }

  /// 获取交易的分类名称
  private func getCategoryName(
    for transaction: TransactionModel,
    mainCategories: [TransactionMainCategoryModel],
    subCategories: [TransactionSubCategoryModel]
  ) -> String {
    // 检查交易是否有分类ID
    guard let categoryId = transaction.transactionCategoryId else {
      return "其他"
    }

    // 首先尝试从子类别查找
    if let subCategory = subCategories.first(where: { $0.id == categoryId }) {
      return subCategory.name
    }

    // 如果在子类别中没找到，则在主类别中查找
    if let mainCategory = mainCategories.first(where: { $0.id == categoryId }) {
      return mainCategory.name
    }

    // 如果都没有找到，返回默认名称
    return "其他"
  }

  /// 合并所有分类统计
  private func combineAllStats() -> [CategoryStat] {
    var combinedStats: [String: Double] = [:]

    // 合并支出数据
    for stat in expenseStats {
      combinedStats[stat.name, default: 0] += stat.amount
    }

    // 合并收入数据
    for stat in incomeStats {
      combinedStats[stat.name, default: 0] += stat.amount
    }

    let totalAmount = combinedStats.values.reduce(0, +)
    guard totalAmount > 0 else { return [] }

    let colors = [
      Color.cAccentBlue, Color.cAccentGreen, Color.cAccentRed,
      Color.orange, Color.purple, Color.pink, Color.cyan, Color.yellow,
    ]

    return Array(
      combinedStats.sorted { $0.value > $1.value }.enumerated().map { index, item in
        CategoryStat(
          name: item.key,
          amount: item.value,
          percentage: item.value / totalAmount,
          color: colors[index % colors.count]
        )
      })
  }

}
