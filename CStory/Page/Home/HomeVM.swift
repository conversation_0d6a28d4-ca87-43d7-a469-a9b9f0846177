import Combine
import SwiftUI

/// 卡片显示数据结构，用于UI层展示
struct HomeCardDisplayData: Identifiable {
  /// 卡片唯一标识符
  let id: UUID

  /// 卡片余额（已转换为本位货币）
  let balance: Double

  /// 货币符号（本位货币符号）
  let symbol: String

  /// 是否为信用卡
  let isCredit: Bool

  /// 卡片背景图片名称（由CardCoverHelper处理）
  let coverImageName: String

  /// 文本颜色（根据背景深浅计算）
  let textColor: Color

  /// 银行Logo数据（可选）
  let bankLogo: Data?
}

/// 主页视图模型
///
/// 简化版本：使用计算属性直接从 DataManager 获取数据，避免重复计算和复杂的更新机制
struct HomeVM {

  // MARK: - Dependencies

  let dataManager: DataManagement
  let onTransactionTap: ((TransactionModel) -> Void)?

  // MARK: - Computed Properties - 资产数据

  /// 资产计算结果
  var assetData: AssetCalculationService.AssetCalculationResult {
    AssetCalculationService.shared.calculateAssets(
      cards: dataManager.cards,
      currencies: dataManager.currencies
    )
  }

  /// 净资产金额
  var netAssetAmount: Double { assetData.netAsset }

  /// 货币符号
  var netAssetSymbol: String { assetData.currencySymbol }

  /// 总资产数据
  var totalAssetData: (title: String, amount: Double, currencySymbol: String, iconName: String) {
    (
      title: "总资产",
      amount: assetData.totalAsset,
      currencySymbol: assetData.currencySymbol,
      iconName: "TotalCardsIcon"
    )
  }

  /// 总负债数据
  var totalLiabilityData: (title: String, amount: Double, currencySymbol: String, iconName: String)
  {
    (
      title: "总负债",
      amount: assetData.totalLiability,
      currencySymbol: assetData.currencySymbol,
      iconName: "TotalLiabilitiesIcon"
    )
  }

  // MARK: - Computed Properties - 卡片数据

  /// 首页显示的卡片数据
  var homeCardDisplayData: [HomeCardDisplayData] {
    dataManager.cards
      .filter { $0.isSelected }
      .sorted { $0.order < $1.order }
      .map { card in
        let coverType = CardCoverHelper.shared.getCoverType(from: card.cover)
        let coverImageName = CardCoverHelper.shared.getCoverImageName(for: coverType)
        let isDark = CardCoverHelper.shared.isCoverDark(for: coverType)

        return HomeCardDisplayData(
          id: card.id,
          balance: card.balance,
          symbol: card.symbol,
          isCredit: card.isCredit,
          coverImageName: coverImageName,
          textColor: isDark ? .white : .cBlack,
          bankLogo: card.bankLogo
        )
      }
  }

  // MARK: - Computed Properties - 圆环数据

  /// 圆环进度数据
  var circularProgressData: (savingsRatio: Double, creditRatio: Double, cashFlowScore: Double) {
    let baseCurrencyCode = UserDefaults.standard.string(forKey: "baseCurrencyCode") ?? "CNY"
    let validCards = dataManager.cards.filter { $0.isSelected && $0.isStatistics }

    // 一次遍历计算储蓄和信用卡数据
    let (savingsNetValue, totalCreditLimit, usedCreditAmount) = validCards.reduce((0.0, 0.0, 0.0)) {
      result, card in
      let currencyRate = CurrencyService.shared.getCurrencyRate(
        from: card.currency,
        to: baseCurrencyCode,
        currencies: dataManager.currencies
      )

      if card.isCredit {
        // 信用卡：计算额度和已用金额
        let limit = card.credit * currencyRate
        let usedAmount = card.balance < 0 ? abs(card.balance) * currencyRate : 0
        return (result.0, result.1 + limit, result.2 + usedAmount)
      } else {
        // 储蓄卡：只计算正余额
        let positiveBalance = card.balance > 0 ? card.balance * currencyRate : 0
        return (result.0 + positiveBalance, result.1, result.2)
      }
    }

    // 计算各项比率
    let savingsRatio =
      assetData.totalAsset > 0 ? min(1.0, savingsNetValue / assetData.totalAsset) : 0.0
    let creditRatio =
      totalCreditLimit > 0
      ? min(1.0, max(0.0, (totalCreditLimit - usedCreditAmount) / totalCreditLimit)) : 0.0
    let cashFlowScore = calculateCashFlowHealthScore()

    return (savingsRatio: savingsRatio, creditRatio: creditRatio, cashFlowScore: cashFlowScore)
  }

  // MARK: - Computed Properties - 交易数据

  /// 最近7天的交易数据（按日期分组）
  var recentTransactionDayGroups: [TransactionDayGroupWithRowVM] {
    return TransactionListDataService.shared.getTransactionListData(
      dataManager: dataManager,
      recentDays: 7,
      onTransactionTap: onTransactionTap
    )
  }

  // MARK: - Private Methods - 辅助方法

  /// 计算现金流健康度（最近30天收支比）
  private func calculateCashFlowHealthScore() -> Double {
    let result = TransactionListDataService.shared.calculateIncomeExpenseStatistics(
      dataManager: dataManager,
      recentDays: 30
    )

    let totalCashFlow = result.totalIncome + result.totalExpense
    return totalCashFlow > 0 ? min(1.0, result.totalIncome / totalCashFlow) : 0.0
  }

}
