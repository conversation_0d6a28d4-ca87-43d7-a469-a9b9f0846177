//
//  DividerTitleKit.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 分割线标题组件
///
/// 组合了ActionButton和虚线分割的标题组件，常用于列表分组标题。
/// 左侧为可点击的标题按钮，右侧延伸虚线至边缘。
///
/// ## 设计特点
/// - 左侧标题可点击（当提供action时）
/// - 右侧虚线自动延伸填满剩余空间
/// - 统一的上下间距设计
/// - 可自定义颜色样式
///
/// ## 使用示例
/// ```swift
/// // 可点击的分组标题
/// DividerTitleKit(title: "收入分类") {
///     navigateToIncomeCategories()
/// }
///
/// // 纯展示的分组标题
/// DividerTitleKit(
///     title: "最近交易",
///     textColor: .gray
/// )
/// ```
struct DividerTitleKit: View {
  let title: String
  let textColor: Color
  let strokeColor: Color
  let action: (() -> Void)?

  /// 初始化分割线标题组件
  /// - Parameters:
  ///   - title: 标题文本
  ///   - textColor: 文字颜色，默认为黑色
  ///   - strokeColor: 虚线颜色，默认为主题色12%透明度
  ///   - action: 点击动作，nil时标题不可点击
  init(
    title: String,
    textColor: Color = Color.cBlack,
    strokeColor: Color = Color.accentColor.opacity(0.12),
    action: (() -> Void)? = nil
  ) {
    self.title = title
    self.textColor = textColor
    self.strokeColor = strokeColor
    self.action = action
  }

  var body: some View {
    HStack(spacing: 0) {
      // 左侧标题按钮
      ActionButton(
        title: title,
        action: action,
        isEnabled: action != nil,
        textColor: textColor,
        strokeStyle: .dashed,
        strokeColor: strokeColor,
        showPressedState: false
      )

      // 右侧虚线延伸
      DividerKit.dashed(color: strokeColor)
    }
    .padding(.top, 24)
    .padding(.bottom, 12)
  }
}

// MARK: - 预览

#Preview {
  VStack(spacing: 0) {
    // 基础用法
    DividerTitleKit(title: "分类管理") {
      print("分类管理点击")
    }

    // 示例内容
    ForEach(0..<3) { index in
      HStack {
        Circle()
          .fill(Color.blue.opacity(0.1))
          .frame(width: 44, height: 44)
          .overlay(
            Text("\(index + 1)")
              .foregroundColor(.blue)
          )

        VStack(alignment: .leading) {
          Text("项目 \(index + 1)")
            .font(.headline)
          Text("项目描述")
            .font(.caption)
            .foregroundColor(.gray)
        }

        Spacer()
      }
      .padding(.horizontal, 16)
      .padding(.vertical, 8)
    }

    // 不可点击标题
    DividerTitleKit(
      title: "历史记录",
      textColor: .gray,
      strokeColor: .gray.opacity(0.3)
    )

    // 示例内容
    ForEach(0..<2) { index in
      HStack {
        Text("历史项目 \(index + 1)")
          .foregroundColor(.gray)
        Spacer()
      }
      .padding(.horizontal, 16)
      .padding(.vertical, 8)
    }

    // 自定义样式
    DividerTitleKit(
      title: "重要提醒",
      textColor: .red,
      strokeColor: .red.opacity(0.3)
    ) {
      print("重要提醒点击")
    }
  }
  .background(Color.gray.opacity(0.05))
}
