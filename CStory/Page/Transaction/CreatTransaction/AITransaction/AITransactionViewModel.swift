//
//  AITransactionViewModel.swift
//  CStory
//
//  Created by NZUE on 2025/7/17.
//

import AVFoundation
import Combine
import PhotosUI
import Speech
import SwiftData
import SwiftUI

/// AI交易视图模型
///
/// 职责：
/// - 管理AI聊天对话状态
/// - 处理多模态输入（文本、语音、图片）
/// - 与ChatService交互
/// - 管理语音识别功能
/// - 处理交易创建逻辑
final class AITransactionViewModel: ObservableObject {

  // MARK: - Properties

  /// 数据管理器
  private let dataManager: DataManagement

  /// 数据模型上下文
  private let modelContext: ModelContext

  // MARK: - AI功能状态
  @Published var userInput: String = ""
  @Published var messages: [ChatMessageModel] = []
  @Published var isLoading: Bool = false
  @Published var showAlert = false
  @Published var alertMessage = ""
  @Published var showMenu: Bool = false
  @Published var showKeyboard: Bool = true
  @Published var selectedImages: [UIImage] = []
  @Published var showImagePicker = false
  @Published var showCamera = false
  @Published var chatService: ChatService?
  @Published var scrollPosition = ScrollPosition(edge: .bottom)
  @Published var keyboardHeight: CGFloat = 0
  @Published var showToast = false
  @Published var toastMessage = ""

  // MARK: - Speech Recognition
  @Published var isRecording = false
  @Published var speechPermissionStatus: SFSpeechRecognizerAuthorizationStatus = .notDetermined

  // MARK: - Integrated Helpers
  private let speechRecognitionManager = SpeechRecognitionHelper()
  private let permissionManager = PermissionManagerHelper()

  // MARK: - UI State (for View coordination)
  @Published var isOutOfBounds: Bool = false
  @Published var pressStartTime: Date?
  // MARK: - Initialization

  init(dataManager: DataManagement, modelContext: ModelContext) {
    self.dataManager = dataManager
    self.modelContext = modelContext
    setupSpeechRecognition()
    setupPermissions()

    // 监听语音识别管理器的状态变化
    speechRecognitionManager.$isRecording
      .receive(on: DispatchQueue.main)
      .assign(to: \.isRecording, on: self)
      .store(in: &cancellables)

    // 监听转录文本变化
    speechRecognitionManager.$transcribedText
      .receive(on: DispatchQueue.main)
      .sink { [weak self] text in
        if self?.isRecording == true && !text.isEmpty {
          self?.userInput = text
        }
      }
      .store(in: &cancellables)

    // 初始化 ChatService
    self.chatService = ChatService(modelContext: modelContext)
  }

  private var cancellables = Set<AnyCancellable>()

  // MARK: - 公共方法

  /// 发送消息
  func sendMessage() {
    guard
      !userInput.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || !selectedImages.isEmpty
    else { return }
    guard let chatService = chatService else { return }

    // 保存用户消息
    let userMessage = chatService.saveUserMessage(content: userInput, images: selectedImages)
    messages.append(userMessage)

    let currentInput = userInput
    let currentImages = selectedImages

    // 清空输入
    userInput = ""
    selectedImages = []
    isLoading = true

    // 调用 AI 服务
    Task {
      do {
        // 在主线程上获取系统提示词
        let systemPrompt = await MainActor.run {
          let modelContext = self.modelContext
          return AIPromptService.shared.generateSystemPrompt(modelContext: modelContext)
        }

        guard !systemPrompt.isEmpty else {
          throw NSError(
            domain: "AITransactionViewModel", code: 1,
            userInfo: [NSLocalizedDescriptionKey: "ModelContext 未初始化"])
        }

        let response = try await AIService.shared.sendMessage(
          text: currentInput,
          images: currentImages,
          systemPrompt: systemPrompt
        )

        await MainActor.run {
          self.isLoading = false
          self.handleAIResponse(response)
        }
      } catch {
        await MainActor.run {
          self.isLoading = false
          self.showError("AI 服务错误: \(error.localizedDescription)")
        }
      }
    }
  }

  /// 开始语音识别（统一管理）
  func startRecording() {
    guard checkAllPermissions() else {
      showError("语音识别权限不足，请在设置中开启麦克风和语音识别权限")
      return
    }

    pressStartTime = Date()
    isOutOfBounds = false

    // 使用集成的语音识别管理器
    speechRecognitionManager.startRecording()
  }

  /// 停止语音识别（统一管理）
  func stopRecording() {
    // 停止集成的语音识别管理器
    let finalText = speechRecognitionManager.stopRecording()

    // 处理识别结果
    if !finalText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
      && shouldProcessRecording()
    {
      userInput = finalText
      sendMessage()
    } else {
      // 如果没有有效文本或不应该处理，确保清空输入
      userInput = ""
    }

    isOutOfBounds = false
  }

  /// 取消语音识别
  func cancelRecording() {
    speechRecognitionManager.cancelRecording()
    // 取消时确保清空所有输入
    userInput = ""
    isOutOfBounds = false
  }

  /// 更新录音边界状态
  func updateRecordingBounds(_ location: CGPoint, screenBounds: CGRect) {
    let bounds = CGRect(
      x: 0, y: 0,
      width: screenBounds.width - 100,
      height: 56
    )
    let newIsOutOfBounds = !bounds.contains(location)
    isOutOfBounds = newIsOutOfBounds
  }

  /// 添加图片
  func addImages(_ images: [UIImage]) {
    selectedImages.append(contentsOf: images)
  }

  /// 移除图片
  func removeImage(at index: Int) {
    guard index < selectedImages.count else { return }
    selectedImages.remove(at: index)
  }

  /// 清空对话
  func clearMessages() {
    messages.removeAll()
    userInput = ""
    selectedImages = []
  }

  /// 显示提示消息
  func showToast(message: String) {
    toastMessage = message
    showToast = true

    DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
      self.showToast = false
    }
  }

  // MARK: - 权限管理

  /// 检查所有必要权限
  func checkAllPermissions() -> Bool {
    return checkSpeechPermissions() && checkMicrophonePermission()
  }

  /// 检查语音识别权限
  func checkSpeechPermissions() -> Bool {
    let speechAuthStatus = SFSpeechRecognizer.authorizationStatus()
    switch speechAuthStatus {
    case .authorized:
      return true
    case .denied, .restricted:
      showPermissionAlert(for: .speech)
      return false
    case .notDetermined:
      // 权限未确定，可以尝试请求
      return true
    @unknown default:
      return false
    }
  }

  /// 检查麦克风权限
  func checkMicrophonePermission() -> Bool {
    let microphoneAuthStatus = AVAudioApplication.shared.recordPermission
    switch microphoneAuthStatus {
    case .granted:
      return true
    case .denied:
      showPermissionAlert(for: .microphone)
      return false
    case .undetermined:
      // 权限未确定，可以尝试请求
      return true
    @unknown default:
      return false
    }
  }

  /// 检查相机权限
  func checkCameraPermission() -> Bool {
    switch permissionManager.cameraPermission {
    case .authorized:
      return true
    case .denied, .restricted:
      showPermissionAlert(for: .camera)
      return false
    case .notDetermined:
      // 权限未确定，可以尝试请求
      return true
    @unknown default:
      return false
    }
  }

  /// 检查相册权限
  func checkPhotoPermission() -> Bool {
    switch permissionManager.photoLibraryPermission {
    case .authorized, .limited:
      return true
    case .denied, .restricted:
      showPermissionAlert(for: .photos)
      return false
    case .notDetermined:
      // 权限未确定，可以尝试请求
      return true
    @unknown default:
      return false
    }
  }

  /// 显示权限提醒弹窗
  private func showPermissionAlert(for permission: PermissionType) {
    showAlert = true
    alertMessage = getPermissionMessage(for: permission)
  }

  /// 获取权限提醒消息
  private func getPermissionMessage(for permission: PermissionType) -> String {
    switch permission {
    case .microphone:
      return "语音录制需要麦克风权限。请前往设置 > 隐私与安全性 > 麦克风，开启本应用的麦克风权限。"
    case .speech:
      return "语音转文字需要语音识别权限。请前往设置 > 隐私与安全性 > 语音识别，开启本应用的语音识别权限。"
    case .camera:
      return "拍照功能需要相机权限。请前往设置 > 隐私与安全性 > 相机，开启本应用的相机权限。"
    case .photos:
      return "选择照片需要相册权限。请前往设置 > 隐私与安全性 > 照片，开启本应用的相册权限。"
    }
  }

  /// 请求所有权限
  func requestAllPermissions() async {
    await permissionManager.requestAllPermissions()
  }

  // MARK: - 私有方法

  /// 设置语音识别
  private func setupSpeechRecognition() {
    speechPermissionStatus = SFSpeechRecognizer.authorizationStatus()

    if speechPermissionStatus == .notDetermined {
      requestSpeechPermission()
    }
  }

  /// 设置权限管理
  private func setupPermissions() {
    // 权限管理器会自动初始化
  }

  /// 判断是否应该处理录音结果
  private func shouldProcessRecording() -> Bool {
    guard let startTime = pressStartTime else { return false }
    let duration = Date().timeIntervalSince(startTime)
    return duration >= 0.5 && !isOutOfBounds
  }

  /// 请求语音权限
  private func requestSpeechPermission() {
    SFSpeechRecognizer.requestAuthorization { [weak self] status in
      DispatchQueue.main.async {
        self?.speechPermissionStatus = status
      }
    }
  }

  /// 处理AI响应
  private func handleAIResponse(_ response: String) {
    guard let chatService = chatService else { return }

    // 尝试解析并创建交易
    let createdTransactions = parseAndCreateTransactions(from: response)

    // 保存AI消息，关联创建的交易ID
    let transactionIds = createdTransactions?.map { $0.id } ?? []
    let aiMessage = chatService.saveAIMessage(content: response, transactionIds: transactionIds)
    messages.append(aiMessage)

    // 显示创建结果
    if let transactions = createdTransactions, !transactions.isEmpty {
      showToast(message: "成功创建 \(transactions.count) 笔交易！")
    }
  }

  /// 解析AI响应并创建交易
  private func parseAndCreateTransactions(from response: String) -> [TransactionModel]? {
    let dataManager = dataManager
    let modelContext = modelContext

    // 解析JSON响应
    guard let data = response.data(using: .utf8),
      let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
      let transactionsData = json["transactions"] as? [[String: Any]]
    else {
      return nil
    }

    // 处理空数组的情况
    if transactionsData.isEmpty {
      print("✅ AI 返回空交易数组，无需创建交易")
      return []
    }

    return createTransactionsFromJSON(
      transactionsData, dataManager: dataManager, modelContext: modelContext)
  }

  /// 从JSON数据创建交易
  private func createTransactionsFromJSON(
    _ transactionsData: [[String: Any]], dataManager: DataManagement, modelContext: ModelContext
  ) -> [TransactionModel]? {
    var createdTransactions: [TransactionModel] = []
    var affectedCardIds: [UUID?] = []

    for transactionData in transactionsData {
      guard let transactionTypeString = transactionData["transactionType"] as? String,
        let transactionType = TransactionType(rawValue: transactionTypeString),
        let amount = transactionData["amount"] as? Double,
        amount > 0
      else {
        continue
      }

      let transactionDate: Date
      if let timestamp = transactionData["timestamp"] as? Int, timestamp > 0 {
        transactionDate = Date(timeIntervalSince1970: TimeInterval(timestamp))
      } else {
        transactionDate = Date()
      }

      let categoryId = transactionData["categoryId"] as? String
      let cardId: UUID?
      if let cardIdString = transactionData["cardId"] as? String, !cardIdString.isEmpty {
        cardId = UUID(uuidString: cardIdString)
      } else {
        cardId = nil
      }

      let note = transactionData["note"] as? String ?? ""
      let currency = transactionData["currency"] as? String ?? "CNY"
      let discount: Double =
        transactionType == .expense ? (transactionData["discount"] as? Double ?? 0.0) : 0.0

      if let transaction = createTransaction(
        type: transactionType,
        amount: amount,
        categoryId: categoryId,
        cardId: cardId,
        currency: currency,
        note: note,
        date: transactionDate,
        discount: discount,
        dataManager: dataManager
      ) {
        createdTransactions.append(transaction)
        affectedCardIds.append(transaction.fromCardId)
        affectedCardIds.append(transaction.toCardId)
      }
    }

    if !createdTransactions.isEmpty {
      saveTransactions(
        createdTransactions, affectedCardIds: affectedCardIds, dataManager: dataManager,
        modelContext: modelContext)
      return createdTransactions
    }

    return nil
  }

  /// 创建单个交易
  private func createTransaction(
    type: TransactionType,
    amount: Double,
    categoryId: String?,
    cardId: UUID?,
    currency: String,
    note: String,
    date: Date,
    discount: Double = 0.0,
    dataManager: DataManagement
  ) -> TransactionModel? {

    let baseCurrencyCode = UserDefaults.standard.string(forKey: "baseCurrencyCode") ?? "CNY"
    let transactionCurrency = dataManager.currencies.first { $0.code == currency }
    let baseCurrency = dataManager.currencies.first { $0.code == baseCurrencyCode }
    let currencySymbol = transactionCurrency?.symbol ?? baseCurrency?.symbol ?? "¥"

    switch type {
    case .expense:
      let fromCardId = cardId ?? dataManager.cards.first?.id
      let fromCard =
        dataManager.cards.first(where: { $0.id == fromCardId }) ?? dataManager.cards.first
      let cardCurrency = fromCard?.currency ?? baseCurrencyCode
      let expenseToCardRate = CurrencyService.shared.getCurrencyRate(
        from: currency, to: cardCurrency, currencies: dataManager.currencies)
      let expenseToBaseRate = CurrencyService.shared.getCurrencyRate(
        from: currency, to: baseCurrencyCode, currencies: dataManager.currencies)

      return TransactionModel(
        id: UUID(),
        transactionType: .expense,
        transactionCategoryId: categoryId,
        fromCardId: fromCardId,
        toCardId: nil,
        discountAmount: discount > 0 ? discount : nil,
        transactionAmount: amount,
        currency: currency,
        symbol: currencySymbol,
        expenseToCardRate: expenseToCardRate,
        expenseToBaseRate: expenseToBaseRate,
        incomeToCardRate: 1.0,
        incomeToBaseRate: 1.0,
        isStatistics: true,
        remark: note,
        transactionDate: date,
        createdAt: Date(),
        updatedAt: Date()
      )

    case .income:
      let toCardId = cardId ?? dataManager.cards.first?.id
      let toCard = dataManager.cards.first(where: { $0.id == toCardId }) ?? dataManager.cards.first
      let cardCurrency = toCard?.currency ?? baseCurrencyCode
      let incomeToCardRate = CurrencyService.shared.getCurrencyRate(
        from: currency, to: cardCurrency, currencies: dataManager.currencies)
      let incomeToBaseRate = CurrencyService.shared.getCurrencyRate(
        from: currency, to: baseCurrencyCode, currencies: dataManager.currencies)

      return TransactionModel(
        id: UUID(),
        transactionType: .income,
        transactionCategoryId: categoryId,
        fromCardId: nil,
        toCardId: toCardId,
        transactionAmount: amount,
        currency: currency,
        symbol: currencySymbol,
        expenseToCardRate: 1.0,
        expenseToBaseRate: 1.0,
        incomeToCardRate: incomeToCardRate,
        incomeToBaseRate: incomeToBaseRate,
        isStatistics: true,
        remark: note,
        transactionDate: date,
        createdAt: Date(),
        updatedAt: Date()
      )

    case .transfer:
      let cardUUID = cardId ?? dataManager.cards.first?.id
      let card = dataManager.cards.first(where: { $0.id == cardUUID }) ?? dataManager.cards.first
      let cardCurrency = card?.currency ?? baseCurrencyCode
      let expenseToCardRate = CurrencyService.shared.getCurrencyRate(
        from: currency, to: cardCurrency, currencies: dataManager.currencies)
      let expenseToBaseRate = CurrencyService.shared.getCurrencyRate(
        from: currency, to: baseCurrencyCode, currencies: dataManager.currencies)

      return TransactionModel(
        id: UUID(),
        transactionType: .transfer,
        transactionCategoryId: "SYS_TRANSFER",
        fromCardId: cardUUID,
        toCardId: cardUUID,
        transactionAmount: amount,
        currency: currency,
        symbol: currencySymbol,
        expenseToCardRate: expenseToCardRate,
        expenseToBaseRate: expenseToBaseRate,
        incomeToCardRate: expenseToCardRate,
        incomeToBaseRate: expenseToBaseRate,
        isStatistics: true,
        remark: note,
        transactionDate: date,
        createdAt: Date(),
        updatedAt: Date()
      )

    default:
      let card = dataManager.cards.first(where: { $0.id == cardId })
      let cardCurrency = card?.currency ?? baseCurrencyCode
      let toCardRate = CurrencyService.shared.getCurrencyRate(
        from: currency, to: cardCurrency, currencies: dataManager.currencies)
      let toBaseRate = CurrencyService.shared.getCurrencyRate(
        from: currency, to: baseCurrencyCode, currencies: dataManager.currencies)

      return TransactionModel(
        id: UUID(),
        transactionType: type,
        transactionCategoryId: categoryId,
        fromCardId: type == .refund ? nil : cardId,
        toCardId: type == .refund ? cardId : nil,
        transactionAmount: amount,
        currency: currency,
        symbol: currencySymbol,
        expenseToCardRate: type == .refund ? 1.0 : toCardRate,
        expenseToBaseRate: type == .refund ? 1.0 : toBaseRate,
        incomeToCardRate: type == .refund ? toCardRate : 1.0,
        incomeToBaseRate: type == .refund ? toBaseRate : 1.0,
        isStatistics: true,
        remark: note,
        transactionDate: date,
        createdAt: Date(),
        updatedAt: Date()
      )
    }
  }

  /// 保存交易到数据库
  private func saveTransactions(
    _ transactions: [TransactionModel], affectedCardIds: [UUID?], dataManager: DataManagement,
    modelContext: ModelContext
  ) {
    do {
      for transaction in transactions {
        modelContext.insert(transaction)
      }

      try modelContext.save()

      BalanceRecalculationService.shared.recalculateBalances(
        for: affectedCardIds,
        modelContext: modelContext,
        currencies: dataManager.currencies,
        operation: "AI交易创建"
      )

    } catch {
      showError("保存交易记录失败: \(error.localizedDescription)")
    }
  }

  /// 显示错误
  private func showError(_ message: String) {
    alertMessage = message
    showAlert = true
  }
}

// MARK: - 扩展：便利方法

extension AITransactionViewModel {

  /// 是否可以发送消息
  var canSendMessage: Bool {
    return !userInput.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
      || !selectedImages.isEmpty
  }

  /// 是否有输入内容
  var hasInput: Bool {
    return !userInput.isEmpty || !selectedImages.isEmpty
  }

  /// 语音识别是否可用
  var speechRecognitionAvailable: Bool {
    return speechPermissionStatus == .authorized
      && speechRecognitionManager.speechRecognizer?.isAvailable == true
  }
}

// MARK: - Permission Types

enum PermissionType {
  case microphone
  case speech
  case camera
  case photos
}
