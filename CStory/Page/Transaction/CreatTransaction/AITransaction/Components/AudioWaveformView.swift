//
//  AudioWaveformView.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 音频波形视图
///
/// 在AI聊天界面进行语音输入时，显示动态的音频波形效果。
/// 根据音频级别实时更新波形高度，并添加随机性使效果更自然。
///
/// ## 参数
/// - audioLevel: 音频级别（0-1），控制波形的整体高度
struct AudioWaveformView: View {
  /// 音频级别（0-1）
  let audioLevel: Double
  /// 波形柱子数量
  let barCount: Int = 25

  var body: some View {
    HStack(spacing: 2) {
      // 创建波形柱子
      ForEach(0..<barCount, id: \.self) { index in
        RoundedRectangle(cornerRadius: 1.5)
          .fill(Color.cWhite)
          .frame(width: 3, height: barHeight(for: index))
          // 添加延迟动画以创建波浪效果
          .animation(
            .easeInOut(duration: 0.15)
              .delay(Double(index) * 0.03),
            value: audioLevel
          )
      }
    }
  }

  /// 计算每个柱子的高度
  /// - Parameter index: 柱子索引
  /// - Returns: 柱子高度
  private func barHeight(for index: Int) -> CGFloat {
    let centerIndex = Double(barCount - 1) / 2.0
    let distance = abs(Double(index) - centerIndex)
    let normalizedDistance = distance / centerIndex

    // 基础高度
    let baseHeight: CGFloat = 4

    // 最大高度
    let maxHeight: CGFloat = 24

    // 根据音频级别和位置计算高度
    let audioMultiplier = max(0.3, min(1.0, audioLevel * 10))

    // 中间的柱子更高，两边的柱子更低
    let positionMultiplier = 1.0 - (normalizedDistance * 0.6)

    // 添加随机性使波形更自然
    let randomFactor = 0.8 + Double.random(in: 0...0.4)

    let finalHeight =
      baseHeight + (maxHeight - baseHeight) * audioMultiplier * positionMultiplier * randomFactor

    return CGFloat(finalHeight)
  }
}

#Preview {
  @Previewable @State var audioLevel: Double = 0.5

  return VStack(spacing: 20) {
    AudioWaveformView(audioLevel: audioLevel)
      .background(Color.black.opacity(0.8))
      .cornerRadius(20)
      .padding()

    Slider(value: $audioLevel, in: 0...1)
      .padding()
  }
}
