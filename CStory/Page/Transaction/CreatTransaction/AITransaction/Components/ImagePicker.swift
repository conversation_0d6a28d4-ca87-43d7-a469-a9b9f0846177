//
//  ImagePicker.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import PhotosUI
import SwiftUI

/// 图片选择器组件
///
/// 封装PHPickerViewController，提供从Photos相册选择多张图片的功能。
/// 支持最多9张图片的选择，并将选中的图片返回到绑定的数组中。
///
/// ## 使用示例
/// ```swift
/// @State private var selectedImages: [UIImage] = []
/// @State private var showImagePicker = false
///
/// .sheet(isPresented: $showImagePicker) {
///     ImagePicker(selectedImages: $selectedImages)
/// }
/// ```
struct ImagePicker: UIViewControllerRepresentable {
  /// 选中的图片数组
  @Binding var selectedImages: [UIImage]
  /// 视图显示状态
  @Environment(\.presentationMode) var presentationMode

  /// 创建并配置PHPickerViewController
  func makeUIViewController(context: Context) -> PHPickerViewController {
    var config = PHPickerConfiguration()
    config.selectionLimit = 9  // 最多选择9张图片
    config.filter = .images    // 只显示图片

    let picker = PHPickerViewController(configuration: config)
    picker.delegate = context.coordinator
    return picker
  }

  /// 更新视图控制器（本组件不需要更新）
  func updateUIViewController(_ uiViewController: PHPickerViewController, context: Context) {
    // 不需要更新
  }

  /// 创建协调器
  func makeCoordinator() -> Coordinator {
    Coordinator(self)
  }

  /// 协调器类，处理PHPicker的委托事件
  class Coordinator: NSObject, PHPickerViewControllerDelegate {
    let parent: ImagePicker

    init(_ parent: ImagePicker) {
      self.parent = parent
    }

    /// 处理用户完成图片选择
    /// - Parameters:
    ///   - picker: 图片选择器
    ///   - results: 选中的图片结果
    func picker(_ picker: PHPickerViewController, didFinishPicking results: [PHPickerResult]) {
      // 关闭选择器
      parent.presentationMode.wrappedValue.dismiss()

      // 处理每个选中的图片
      for result in results {
        if result.itemProvider.canLoadObject(ofClass: UIImage.self) {
          result.itemProvider.loadObject(ofClass: UIImage.self) { image, error in
            if let image = image as? UIImage {
              // 在主线程更新UI
              DispatchQueue.main.async {
                self.parent.selectedImages.append(image)
              }
            }
          }
        }
      }
    }
  }
}
