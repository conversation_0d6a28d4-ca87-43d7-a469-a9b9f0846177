//
//  CameraPicker.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 相机拍照组件
///
/// 封装UIImagePickerController，提供调用系统相机拍摄照片的功能。
/// 拍摄完成后通过回调闭包返回拍摄的图片。
///
/// ## 使用示例
/// ```swift
/// @State private var showCamera = false
///
/// .sheet(isPresented: $showCamera) {
///     CameraPicker(selectedImage: { image in
///         // 处理拍摄的图片
///         selectedImages.append(image)
///     })
/// }
/// ```
struct CameraPicker: UIViewControllerRepresentable {
  /// 图片选中回调
  let selectedImage: (UIImage) -> Void
  /// 视图显示状态
  @Environment(\.presentationMode) var presentationMode

  /// 创建并配置UIImagePickerController
  func makeUIViewController(context: Context) -> UIImagePickerController {
    let picker = UIImagePickerController()
    picker.sourceType = .camera  // 设置为相机模式
    picker.delegate = context.coordinator
    return picker
  }

  /// 更新视图控制器（本组件不需要更新）
  func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

  /// 创建协调器
  func makeCoordinator() -> Coordinator {
    Coordinator(self)
  }

  /// 协调器类，处理UIImagePickerController的委托事件
  class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    let parent: CameraPicker

    init(_ parent: CameraPicker) {
      self.parent = parent
    }

    /// 处理用户完成拍照
    /// - Parameters:
    ///   - picker: 图片选择器
    ///   - info: 包含拍摄图片的字典
    func imagePickerController(
      _ picker: UIImagePickerController,
      didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey: Any]
    ) {
      // 获取原始图片
      if let image = info[.originalImage] as? UIImage {
        parent.selectedImage(image)
      }
      // 关闭相机界面
      parent.presentationMode.wrappedValue.dismiss()
    }

    /// 处理用户取消拍照
    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
      // 关闭相机界面
      parent.presentationMode.wrappedValue.dismiss()
    }
  }
}
