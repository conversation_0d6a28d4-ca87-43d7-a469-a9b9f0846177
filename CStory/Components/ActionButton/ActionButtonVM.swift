//
//  ActionButtonVM.swift
//  CStory
//
//  Created by NZUE on 2025/7/22.
//

import SwiftUI

/// 边框样式枚举
///
/// 定义按钮组件支持的边框线条样式
enum BorderStyle {
  /// 实线边框
  case solid
  /// 虚线边框
  case dashed
}

/// 操作按钮视图模型
///
/// 管理ActionButton组件的业务逻辑和状态，遵循MVVM架构模式。
/// 负责按钮的显示内容、交互状态和样式配置的管理。
///
/// 该类提供了全面的按钮状态管理，包括文本、颜色、启用状态、
/// 边框样式等，并支持点击事件和触觉反馈。
///
/// ## 主要功能
/// - 按钮文本和样式管理
/// - 启用/禁用状态控制
/// - 边框样式配置
/// - 点击事件处理
/// - 触觉反馈管理
///
/// ## 使用示例
/// ```swift
/// // 基本创建
/// let viewModel = ActionButtonVM(title: "查看全部") {
///   print("按钮被点击")
/// }
///
/// // 完整配置
/// let viewModel = ActionButtonVM(
///   title: "保存",
///   isEnabled: true,
///   textColor: .blue,
///   strokeStyle: .dashed,
///   strokeColor: .gray
/// ) {
///   // 点击处理
/// }
/// ```
///
/// - Author: NZUE
/// - Version: 2.0 (新架构)
/// - Since: 2025.7.22
@MainActor
final class ActionButtonVM: ObservableObject {

  // MARK: - Published Properties

  /// 按钮文本
  @Published var title: String

  /// 是否启用
  @Published var isEnabled: Bool

  /// 文字颜色
  @Published var textColor: Color

  /// 描边样式
  @Published var strokeStyle: BorderStyle

  /// 描边颜色
  @Published var strokeColor: Color

  /// 是否显示按压状态
  @Published var showPressedState: Bool

  /// 字体大小
  @Published var fontSize: CGFloat

  /// 字体粗细
  @Published var fontWeight: Font.Weight

  /// 水平内边距
  @Published var horizontalPadding: CGFloat

  /// 垂直内边距
  @Published var verticalPadding: CGFloat

  /// 圆角半径
  @Published var cornerRadius: CGFloat

  /// 触觉反馈管理器
  private let hapticManager = HapticFeedbackManager.shared

  // MARK: - Private Properties

  /// 点击回调
  private var action: (() -> Void)?

  // MARK: - Computed Properties

  /// 按钮字体
  var buttonFont: Font {
    .system(size: fontSize, weight: fontWeight)
  }

  /// 描边样式配置
  var strokeStyleConfig: SwiftUI.StrokeStyle {
    strokeStyle == .dashed
      ? SwiftUI.StrokeStyle(lineWidth: 1, dash: [5])
      : SwiftUI.StrokeStyle(lineWidth: 1)
  }

  /// 是否可以点击
  var canTap: Bool {
    isEnabled && action != nil
  }

  // MARK: - Initialization

  /// 标准初始化
  init(
    title: String,
    action: (() -> Void)? = nil,
    isEnabled: Bool = true,
    textColor: Color = Color.cBlack.opacity(0.6),
    strokeStyle: BorderStyle = .solid,
    strokeColor: Color = Color.accentColor.opacity(0.08),
    showPressedState: Bool = true
  ) {
    self.title = title
    self.action = action
    self.isEnabled = isEnabled
    self.textColor = textColor
    self.strokeStyle = strokeStyle
    self.strokeColor = strokeColor
    self.showPressedState = showPressedState

    // 默认样式配置
    self.fontSize = 13
    self.fontWeight = .regular
    self.horizontalPadding = 12
    self.verticalPadding = 6
    self.cornerRadius = 100
  }

  // MARK: - Public Methods

  /// 处理按钮点击
  func handleButtonTap() {
    guard canTap else { return }

    // 触发触觉反馈
    hapticManager.trigger(.impactLight)

    // 执行回调
    action?()
  }

  /// 更新按钮文本
  func updateTitle(_ newTitle: String) {
    title = newTitle
  }

  /// 更新点击回调
  func updateAction(_ newAction: (() -> Void)?) {
    action = newAction
  }

  /// 设置启用状态
  func setEnabled(_ enabled: Bool) {
    isEnabled = enabled
  }

  /// 更新文本样式
  func updateTextStyle(
    color: Color? = nil,
    fontSize: CGFloat? = nil,
    fontWeight: Font.Weight? = nil
  ) {
    if let color = color {
      self.textColor = color
    }
    if let fontSize = fontSize {
      self.fontSize = fontSize
    }
    if let fontWeight = fontWeight {
      self.fontWeight = fontWeight
    }
  }

  /// 更新边框样式
  func updateStrokeStyle(
    style: BorderStyle? = nil,
    color: Color? = nil
  ) {
    if let style = style {
      self.strokeStyle = style
    }
    if let color = color {
      self.strokeColor = color
    }
  }

  /// 更新内边距
  func updatePadding(
    horizontal: CGFloat? = nil,
    vertical: CGFloat? = nil
  ) {
    if let horizontal = horizontal {
      self.horizontalPadding = horizontal
    }
    if let vertical = vertical {
      self.verticalPadding = vertical
    }
  }

  /// 更新圆角半径
  func updateCornerRadius(_ radius: CGFloat) {
    cornerRadius = radius
  }

  /// 设置按压状态显示
  func setShowPressedState(_ show: Bool) {
    showPressedState = show
  }

  // MARK: - Factory Methods

  /// 创建查看全部按钮
  static func viewAllButton(action: @escaping () -> Void) -> ActionButtonVM {
    return ActionButtonVM(
      title: "查看全部",
      action: action
    )
  }

  /// 创建虚线样式按钮
  static func dashedButton(
    title: String,
    action: @escaping () -> Void
  ) -> ActionButtonVM {
    return ActionButtonVM(
      title: title,
      action: action,
      strokeStyle: .dashed
    )
  }

  /// 创建标签样式（不可点击）
  static func labelStyle(
    title: String,
    textColor: Color = Color.cBlack.opacity(0.6)
  ) -> ActionButtonVM {
    return ActionButtonVM(
      title: title,
      action: nil,
      isEnabled: false,
      textColor: textColor,
      showPressedState: false
    )
  }

  /// 创建自定义颜色按钮
  static func customColor(
    title: String,
    action: @escaping () -> Void,
    textColor: Color,
    strokeColor: Color
  ) -> ActionButtonVM {
    return ActionButtonVM(
      title: title,
      action: action,
      textColor: textColor,
      strokeColor: strokeColor
    )
  }
}
