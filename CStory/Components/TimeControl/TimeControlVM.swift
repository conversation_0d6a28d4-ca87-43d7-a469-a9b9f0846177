//
//  TimeControlVM.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/18.
//

import Combine
import SwiftUI

// MARK: - 时间选择视图模型

/// 时间选择管理器
@MainActor
final class TimeControlVM: ObservableObject {
  /// 当前选择的时间周期
  @Published var selectedPeriod: TransactionTimePeriod = .month {
    didSet { onDateChange?(currentDate, selectedPeriod) }
  }
  /// 当前选择的日期
  @Published var currentDate = Date() {
    didSet { onDateChange?(currentDate, selectedPeriod) }
  }

  /// 时间变化回调
  var onDateChange: ((Date, TransactionTimePeriod) -> Void)?

  // MARK: - 初始化

  /// 默认初始化
  init() {}

  /// 便利初始化方法
  init(
    selectedPeriod: TransactionTimePeriod = .month,
    currentDate: Date = Date(),
    onDateChange: @escaping (Date, TransactionTimePeriod) -> Void
  ) {
    self.selectedPeriod = selectedPeriod
    self.currentDate = currentDate
    self.onDateChange = onDateChange
  }

  /// 计算日期范围
  var dateRange: (start: Date, end: Date) {
    let calendar = Calendar.current
    let now = currentDate

    switch selectedPeriod {
    case .week:
      let weekStart = calendar.date(
        from: calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: now))!
      let weekEnd = calendar.date(byAdding: .weekOfYear, value: 1, to: weekStart)!
      return (weekStart, weekEnd)
    case .month:
      let components = calendar.dateComponents([.year, .month], from: now)
      let monthStart = calendar.date(from: components)!
      let monthEnd = calendar.date(byAdding: .month, value: 1, to: monthStart)!
      return (monthStart, monthEnd)
    case .year:
      let components = calendar.dateComponents([.year], from: now)
      let yearStart = calendar.date(from: components)!
      let yearEnd = calendar.date(byAdding: .year, value: 1, to: yearStart)!
      return (yearStart, yearEnd)
    }
  }

  /// 移动日期
  func moveDate(forward: Bool) {
    let calendar = Calendar.current
    let newDate: Date
    switch selectedPeriod {
    case .week:
      newDate =
        calendar.date(byAdding: .weekOfYear, value: forward ? 1 : -1, to: currentDate)
        ?? currentDate
    case .month:
      newDate =
        calendar.date(byAdding: .month, value: forward ? 1 : -1, to: currentDate) ?? currentDate
    case .year:
      newDate =
        calendar.date(byAdding: .year, value: forward ? 1 : -1, to: currentDate) ?? currentDate
    }
    currentDate = newDate
  }

  /// 重置到当前日期
  func resetToCurrentDate() {
    currentDate = Date()
  }

  /// 切换时间周期（循环切换）
  func toggleTimePeriod() {
    selectedPeriod =
      switch selectedPeriod {
      case .month: .year
      case .year: .week
      case .week: .month
      }
  }

  /// 检查是否为当前日期
  var isCurrentDate: Bool {
    Calendar.current.isDate(currentDate, inSameDayAs: Date())
  }

  /// 是否应该显示"回到今日"按钮
  var shouldShowBackToTodayButton: Bool {
    let calendar = Calendar.current
    let today = Date()

    switch selectedPeriod {
    case .week:
      let currentWeek = calendar.dateComponents(
        [.yearForWeekOfYear, .weekOfYear], from: currentDate)
      let todayWeek = calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: today)
      return currentWeek.yearForWeekOfYear != todayWeek.yearForWeekOfYear
        || currentWeek.weekOfYear != todayWeek.weekOfYear

    case .month:
      let currentMonth = calendar.dateComponents([.year, .month], from: currentDate)
      let todayMonth = calendar.dateComponents([.year, .month], from: today)
      return currentMonth.year != todayMonth.year || currentMonth.month != todayMonth.month

    case .year:
      let currentYear = calendar.component(.year, from: currentDate)
      let todayYear = calendar.component(.year, from: today)
      return currentYear != todayYear
    }
  }

}
