//
//  IconView.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//  Refactored by 咩咩 on 2025/7/22.
//

import SwiftUI

/// 通用图标视图组件
///
/// 基于MVVM架构的图标显示组件，统一处理IconType类型的显示。
/// 支持emoji和图片两种类型，提供一致的图标显示效果。
///
/// ## 设计特点
/// - 遵循MVVM架构模式，业务逻辑与UI分离
/// - 支持emoji和图片两种图标类型
/// - 可配置大小、背景色、圆角、选中状态
/// - 支持点击交互和动画效果
/// - 自动处理图片缩放和裁剪
///
/// ## 使用场景
/// - 交易分类图标显示
/// - 银行logo展示
/// - 按钮图标
/// - 列表项图标
/// - 状态指示器
///
/// ## 使用示例
/// ```swift
/// // 使用ViewModel
/// let viewModel = IconViewVM.emoji("🚀", size: 60)
/// IconView(viewModel: viewModel)
///
/// // 可选择的图标
/// let selectableVM = IconViewVM.selectable(
///   icon: .emoji("💰"),
///   isSelected: true,
///   onTap: { print("图标被点击") }
/// )
/// IconView(viewModel: selectableVM)
///
/// // 使用工厂方法
/// IconView(viewModel: .image(imageData, size: 40))
/// ```
struct IconView: View {

  // MARK: - Properties

  @ObservedObject var viewModel: IconViewVM

  /// 数据管理器
  @Environment(\.dataManager) private var dataManager

  // MARK: - Initialization

  /// 使用ViewModel初始化
  /// - Parameter viewModel: 图标视图模型
  init(viewModel: IconViewVM) {
    self.viewModel = viewModel
  }

  // MARK: - Body

  var body: some View {
    Group {
      if viewModel.onTap != nil {
        // 可点击版本
        Button(action: {
          dataManager.hapticManager.trigger(.impactLight)
          viewModel.handleTap()
        }) {
          iconContent
        }
      } else {
        // 纯展示版本
        iconContent
      }
    }
  }

  // MARK: - Private Views

  private var iconContent: some View {
    Group {
      switch viewModel.icon {
      case .emoji(let emoji):
        // Emoji 显示
        Text(emoji)
          .font(.system(size: viewModel.actualFontSize))

      case .image(let data):
        // 图片显示
        if let uiImage = UIImage(data: data) {
          Image(uiImage: uiImage)
            .resizable()
            .aspectRatio(contentMode: .fill)
            .clipped()
        } else {
          // 图片加载失败时的占位符
          Image("question")
            .resizable()
            .aspectRatio(contentMode: .fill)
            .clipped()
            .opacity(0.6)
        }
      }
    }
    .frame(width: viewModel.size, height: viewModel.size)
    .background(viewModel.selectedBackgroundColor)
    .overlay(
      // 边框显示
      Group {
        if viewModel.shouldShowBorder {
          RoundedRectangle(cornerRadius: viewModel.cornerRadius)
            .strokeBorder(
              viewModel.borderColor,
              lineWidth: viewModel.borderWidth
            )
        }
      }
    )
    .cornerRadius(viewModel.cornerRadius)
  }
}

// MARK: - 预览

#Preview {
  ScrollView {
    VStack(spacing: 30) {

      // MARK: 基础使用
      VStack(alignment: .leading, spacing: 15) {
        Text("基础用法")
          .font(.title2)
          .fontWeight(.bold)

        HStack(spacing: 20) {
          IconView(viewModel: .emoji("😀"))
          IconView(viewModel: .emoji("🚗", size: 32))
          IconView(viewModel: .emoji("💰", size: 56))
          IconView(viewModel: .emoji("🎉", size: 40))
        }
      }

      Divider()

      // MARK: 不同样式
      VStack(alignment: .leading, spacing: 15) {
        Text("不同样式")
          .font(.title2)
          .fontWeight(.bold)

        HStack(spacing: 20) {
          IconView(viewModel: .emoji("📦", style: .square))
          IconView(viewModel: .emoji("📍", style: .default))
          IconView(viewModel: .emoji("🌈", style: .rounded))
          IconView(viewModel: .emoji("⭐", style: .circular))
          IconView(viewModel: .emoji("❤️", style: .clear))
        }
      }

      Divider()

      // MARK: 可选择状态
      VStack(alignment: .leading, spacing: 15) {
        Text("可选择状态")
          .font(.title2)
          .fontWeight(.bold)

        HStack(spacing: 20) {
          IconView(viewModel: IconViewVM.selectable(
            icon: .emoji("🔥"),
            isSelected: false,
            onTap: { print("🔥 被点击") }
          ))

          IconView(viewModel: IconViewVM.selectable(
            icon: .emoji("⚡"),
            isSelected: true,
            onTap: { print("⚡ 被点击") }
          ))

          IconView(viewModel: IconViewVM.selectable(
            icon: .emoji("🌟"),
            size: 60,
            isSelected: false,
            onTap: { print("🌟 被点击") }
          ))
        }
      }

      Divider()

      // MARK: 图片图标
      VStack(alignment: .leading, spacing: 15) {
        Text("图片图标")
          .font(.title2)
          .fontWeight(.bold)

        if let starImage = UIImage(systemName: "star.fill")?.withTintColor(.orange, renderingMode: .alwaysOriginal),
           let heartImage = UIImage(systemName: "heart.fill")?.withTintColor(.red, renderingMode: .alwaysOriginal),
           let starData = starImage.pngData(),
           let heartData = heartImage.pngData() {

          HStack(spacing: 20) {
            IconView(viewModel: .image(starData))
            IconView(viewModel: .image(heartData, size: 32))
            IconView(viewModel: IconViewVM.selectable(
              icon: .image(starData),
              size: 56,
              isSelected: true,
              onTap: { print("星星图片被点击") }
            ))
          }
        }
      }

      Divider()

      // MARK: 不同尺寸
      VStack(alignment: .leading, spacing: 15) {
        Text("不同尺寸")
          .font(.title2)
          .fontWeight(.bold)

        HStack(alignment: .center, spacing: 20) {
          IconView(viewModel: .emoji("🎯", size: 20))
          IconView(viewModel: .emoji("🎯", size: 30))
          IconView(viewModel: .emoji("🎯", size: 44))
          IconView(viewModel: .emoji("🎯", size: 60))
          IconView(viewModel: .emoji("🎯", size: 80))
        }
      }

      Divider()

      // MARK: 交互演示
      VStack(alignment: .leading, spacing: 15) {
        Text("交互演示")
          .font(.title2)
          .fontWeight(.bold)

        InteractiveIconDemo()
      }
    }
    .padding()
  }
  .background(Color(.systemGroupedBackground))
}

// MARK: - 交互演示组件

private struct InteractiveIconDemo: View {
  @State private var selectedIcons: Set<String> = []

  private let icons = ["🎮", "🎵", "🎨", "📚", "⚽", "🍕"]

  var body: some View {
    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 15) {
      ForEach(icons, id: \.self) { emoji in
        IconView(viewModel: IconViewVM.selectable(
          icon: .emoji(emoji),
          size: 60,
          isSelected: selectedIcons.contains(emoji),
          onTap: {
            withAnimation(.spring()) {
              if selectedIcons.contains(emoji) {
                selectedIcons.remove(emoji)
              } else {
                selectedIcons.insert(emoji)
              }
            }
          }
        ))
      }
    }
  }
}
