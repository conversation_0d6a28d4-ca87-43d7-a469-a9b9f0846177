//
//  VariableBlurView.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI
import UIKit
import CoreImage.CIFilterBuiltins
import QuartzCore

/// 渐变模糊方向
///
/// 定义模糊效果的渐变方向，从模糊到清晰的过渡方向
public enum VariableBlurDirection {
  /// 顶部模糊，底部清晰
  case blurredTopClearBottom
  /// 底部模糊，顶部清晰
  case blurredBottomClearTop
}

/// 渐变模糊视图
///
/// 提供从一端到另一端渐变的模糊效果，常用于列表顶部或底部的淡出效果。
/// 基于 Core Animation 的私有 API 实现高性能的实时模糊。
///
/// ## 功能特性
/// - 支持垂直方向的渐变模糊
/// - 可调节最大模糊半径
/// - 可设置模糊起始偏移
/// - 实时响应内容变化
///
/// ## 使用示例
/// ```swift
/// ZStack {
///     ScrollView {
///         // 内容
///     }
///     
///     VStack {
///         VariableBlurView(
///             maxBlurRadius: 20,
///             direction: .blurredTopClearBottom
///         )
///         .frame(height: 100)
///         
///         Spacer()
///     }
/// }
/// ```
///
/// ## 注意事项
/// - 使用了私有 API，可能在未来的 iOS 版本中失效
/// - 性能优于标准的 UIBlurEffect
/// - 仅支持垂直方向的渐变
public struct VariableBlurView: UIViewRepresentable {
  /// 最大模糊半径
  public var maxBlurRadius: CGFloat = 20

  /// 模糊方向
  public var direction: VariableBlurDirection = .blurredTopClearBottom

  /// 模糊起始偏移量
  ///
  /// 控制模糊渐变的起始位置。
  /// - 0: 从完全清晰开始渐变到模糊
  /// - 负值（如 -0.1）: 从较大的模糊值开始，创建更柔和的过渡
  /// - 正值: 延迟模糊效果的开始
  public var startOffset: CGFloat = 0
  
  /// 创建 UIKit 视图
  public func makeUIView(context: Context) -> VariableBlurUIView {
    VariableBlurUIView(
      maxBlurRadius: maxBlurRadius, 
      direction: direction, 
      startOffset: startOffset
    )
  }
  
  /// 更新 UIKit 视图
  public func updateUIView(_ uiView: VariableBlurUIView, context: Context) {
    // 视图创建后不需要更新
  }
}

/// 渐变模糊 UIKit 实现
///
/// 使用 Core Animation 的私有 API 实现高性能的渐变模糊效果。
/// 通过自定义 CAFilter 和渐变蒙版实现从清晰到模糊的平滑过渡。
///
/// 基于 https://github.com/jtrivedi/VariableBlurView
open class VariableBlurUIView: UIVisualEffectView {
  
  /// 初始化渐变模糊视图
  /// - Parameters:
  ///   - maxBlurRadius: 最大模糊半径
  ///   - direction: 模糊方向
  ///   - startOffset: 起始偏移量
  public init(
    maxBlurRadius: CGFloat = 20, direction: VariableBlurDirection = .blurredTopClearBottom,
    startOffset: CGFloat = 0
  ) {
    super.init(effect: UIBlurEffect(style: .regular))

    // 使用 CAFilter 私有类创建 variableBlur 滤镜
    guard let CAFilter = NSClassFromString("CAFilter") as? NSObject.Type else {
      print("[VariableBlur] Error: Can't find CAFilter class")
      return
    }
    guard
      let variableBlur = CAFilter.self.perform(
        NSSelectorFromString("filterWithType:"), with: "variableBlur"
      ).takeUnretainedValue() as? NSObject
    else {
      print("[VariableBlur] Error: CAFilter can't create filterWithType: variableBlur")
      return
    }

    // 创建渐变蒙版图像
    // 蒙版的 alpha 值决定每个像素的模糊程度：
    // - alpha = 1.0: 最大模糊
    // - alpha = 0.0: 完全清晰
    let gradientImage = makeGradientImage(startOffset: startOffset, direction: direction)

    // 配置 variableBlur 滤镜参数
    variableBlur.setValue(maxBlurRadius, forKey: "inputRadius")
    variableBlur.setValue(gradientImage, forKey: "inputMaskImage")
    variableBlur.setValue(true, forKey: "inputNormalizeEdges")

    // 获取 UIVisualEffectView 的 CABackdropLayer
    // 这个特殊的图层能够对其下方的内容应用实时滤镜
    let backdropLayer = subviews.first?.layer
    
    // 替换默认的模糊滤镜为自定义的 variableBlur
    backdropLayer?.filters = [variableBlur]
    
    // 隐藏视觉效果视图的暗化/色调视图
    // 避免在模糊边缘出现硬边界线
    for subview in subviews.dropFirst() {
      subview.alpha = 0
    }
  }

  required public init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  /// 视图添加到窗口时调用
  open override func didMoveToWindow() {
    // 设置正确的屏幕缩放比例
    // 修复在高分辨率屏幕上模糊边缘的像素化问题
    guard let window, let backdropLayer = subviews.first?.layer else { return }
    backdropLayer.setValue(window.screen.scale, forKey: "scale")
  }
  
  /// 特征集合变化时调用
  open override func traitCollectionDidChange(_ previousTraitCollection: UITraitCollection?) {
    // 注意：调用 super 会导致崩溃，因此这里故意不调用
  }
  
  /// 创建渐变蒙版图像
  /// - Parameters:
  ///   - width: 图像宽度
  ///   - height: 图像高度
  ///   - startOffset: 起始偏移量
  ///   - direction: 渐变方向
  /// - Returns: 渐变蒙版 CGImage
  private func makeGradientImage(
    width: CGFloat = 100, height: CGFloat = 100, startOffset: CGFloat,
    direction: VariableBlurDirection
  ) -> CGImage {
    // 使用 Core Image 创建线性渐变
    let ciGradientFilter = CIFilter.linearGradient()
    ciGradientFilter.color0 = CIColor.black  // 模糊区域
    ciGradientFilter.color1 = CIColor.clear  // 清晰区域
    
    // 设置渐变点
    ciGradientFilter.point0 = CGPoint(x: 0, y: height)
    ciGradientFilter.point1 = CGPoint(x: 0, y: startOffset * height)
    
    // 根据方向调整渐变点
    if case .blurredBottomClearTop = direction {
      ciGradientFilter.point0.y = 0
      ciGradientFilter.point1.y = height - ciGradientFilter.point1.y
    }
    
    // 生成渐变图像
    return CIContext().createCGImage(
      ciGradientFilter.outputImage!, 
      from: CGRect(x: 0, y: 0, width: width, height: height)
    )!
  }
}

// MARK: - 预览

#Preview {
  ScrollView {
    VStack(spacing: 30) {
      
      // MARK: 基础效果展示
      VStack(alignment: .leading, spacing: 15) {
        Text("基础模糊效果")
          .font(.title2)
          .fontWeight(.bold)
        
        ZStack {
          // 背景内容
          VStack(spacing: 10) {
            ForEach(0..<10, id: \.self) { index in
              HStack {
                Circle()
                  .fill(Color.blue)
                  .frame(width: 30, height: 30)
                Text("示例内容行 \(index + 1)")
                  .font(.system(size: 16, weight: .medium))
                Spacer()
              }
              .padding(.horizontal)
            }
          }
          .background(Color.white)
          .cornerRadius(16)
          
          // 顶部模糊遮罩
          VStack {
            VariableBlurView(
              maxBlurRadius: 20,
              direction: .blurredTopClearBottom
            )
            .frame(height: 80)
            
            Spacer()
          }
        }
        .frame(height: 200)
      }
      
      Divider()
      
      // MARK: 不同方向的模糊
      VStack(alignment: .leading, spacing: 15) {
        Text("不同模糊方向")
          .font(.title2)
          .fontWeight(.bold)
        
        HStack(spacing: 20) {
          // 顶部模糊
          ZStack {
            VStack(spacing: 8) {
              ForEach(0..<6, id: \.self) { index in
                Rectangle()
                  .fill(Color.green.opacity(0.7))
                  .frame(height: 20)
              }
            }
            .background(Color.white)
            .cornerRadius(12)
            
            VStack {
              VariableBlurView(
                maxBlurRadius: 15,
                direction: .blurredTopClearBottom
              )
              .frame(height: 60)
              
              Spacer()
            }
          }
          .frame(height: 120)
          
          // 底部模糊
          ZStack {
            VStack(spacing: 8) {
              ForEach(0..<6, id: \.self) { index in
                Rectangle()
                  .fill(Color.orange.opacity(0.7))
                  .frame(height: 20)
              }
            }
            .background(Color.white)
            .cornerRadius(12)
            
            VStack {
              Spacer()
              
              VariableBlurView(
                maxBlurRadius: 15,
                direction: .blurredBottomClearTop
              )
              .frame(height: 60)
            }
          }
          .frame(height: 120)
        }
      }
      
      Divider()
      
      // MARK: 不同模糊强度
      VStack(alignment: .leading, spacing: 15) {
        Text("不同模糊强度")
          .font(.title2)
          .fontWeight(.bold)
        
        HStack(spacing: 15) {
          // 弱模糊
          ZStack {
            VStack(spacing: 4) {
              ForEach(0..<8, id: \.self) { _ in
                Rectangle()
                  .fill(Color.purple.opacity(0.6))
                  .frame(height: 15)
              }
            }
            .background(Color.white)
            .cornerRadius(12)
            
            VStack {
              VariableBlurView(
                maxBlurRadius: 5,  // 弱模糊
                direction: .blurredTopClearBottom
              )
              .frame(height: 50)
              
              Spacer()
            }
          }
          .frame(height: 120)
          
          // 中模糊
          ZStack {
            VStack(spacing: 4) {
              ForEach(0..<8, id: \.self) { _ in
                Rectangle()
                  .fill(Color.red.opacity(0.6))
                  .frame(height: 15)
              }
            }
            .background(Color.white)
            .cornerRadius(12)
            
            VStack {
              VariableBlurView(
                maxBlurRadius: 15,  // 中模糊
                direction: .blurredTopClearBottom
              )
              .frame(height: 50)
              
              Spacer()
            }
          }
          .frame(height: 120)
          
          // 强模糊
          ZStack {
            VStack(spacing: 4) {
              ForEach(0..<8, id: \.self) { _ in
                Rectangle()
                  .fill(Color.teal.opacity(0.6))
                  .frame(height: 15)
              }
            }
            .background(Color.white)
            .cornerRadius(12)
            
            VStack {
              VariableBlurView(
                maxBlurRadius: 30,  // 强模糊
                direction: .blurredTopClearBottom
              )
              .frame(height: 50)
              
              Spacer()
            }
          }
          .frame(height: 120)
        }
      }
      
      Divider()
      
      // MARK: 起始偏移效果
      VStack(alignment: .leading, spacing: 15) {
        Text("起始偏移效果")
          .font(.title2)
          .fontWeight(.bold)
        
        HStack(spacing: 15) {
          // 无偏移
          ZStack {
            coloredContent(color: .mint)
            
            VStack {
              VariableBlurView(
                maxBlurRadius: 20,
                direction: .blurredTopClearBottom,
                startOffset: 0  // 无偏移
              )
              .frame(height: 70)
              
              Spacer()
            }
          }
          .frame(height: 140)
          .overlay(
            VStack {
              Text("无偏移")
                .font(.caption)
                .foregroundColor(.white)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.black.opacity(0.7))
                .cornerRadius(8)
              Spacer()
            }
          )
          
          // 负偏移（柔和过渡）
          ZStack {
            coloredContent(color: .cyan)
            
            VStack {
              VariableBlurView(
                maxBlurRadius: 20,
                direction: .blurredTopClearBottom,
                startOffset: -0.1  // 负偏移
              )
              .frame(height: 70)
              
              Spacer()
            }
          }
          .frame(height: 140)
          .overlay(
            VStack {
              Text("负偏移")
                .font(.caption)
                .foregroundColor(.white)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.black.opacity(0.7))
                .cornerRadius(8)
              Spacer()
            }
          )
          
          // 正偏移（延迟效果）
          ZStack {
            coloredContent(color: .indigo)
            
            VStack {
              VariableBlurView(
                maxBlurRadius: 20,
                direction: .blurredTopClearBottom,
                startOffset: 0.2  // 正偏移
              )
              .frame(height: 70)
              
              Spacer()
            }
          }
          .frame(height: 140)
          .overlay(
            VStack {
              Text("正偏移")
                .font(.caption)
                .foregroundColor(.white)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.black.opacity(0.7))
                .cornerRadius(8)
              Spacer()
            }
          )
        }
      }
      
      Divider()
      
      // MARK: 实际应用场景
      VStack(alignment: .leading, spacing: 15) {
        Text("实际应用场景")
          .font(.title2)
          .fontWeight(.bold)
        
        // 模拟浮动操作按钮背景
        ZStack {
          ScrollView {
            LazyVStack(spacing: 12) {
              ForEach(0..<20, id: \.self) { index in
                HStack {
                  Circle()
                    .fill(Color.blue.gradient)
                    .frame(width: 40, height: 40)
                  
                  VStack(alignment: .leading, spacing: 4) {
                    Text("交易记录 \(index + 1)")
                      .font(.system(size: 16, weight: .medium))
                    Text("2025年7月\(index + 1)日")
                      .font(.system(size: 14))
                      .foregroundColor(.gray)
                  }
                  
                  Spacer()
                  
                  Text("¥\(Int.random(in: 10...999))")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.green)
                }
                .padding()
                .background(Color.white)
                .cornerRadius(12)
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
              }
            }
            .padding()
          }
          
          // 底部模糊遮罩（模拟浮动按钮区域）
          VStack {
            Spacer()
            
            VariableBlurView(
              maxBlurRadius: 15,
              direction: .blurredBottomClearTop,
              startOffset: -0.05
            )
            .frame(height: 80)
          }
          .overlay(
            VStack {
              Spacer()
              
              // 模拟浮动操作按钮
              HStack {
                Spacer()
                Button("保存") {
                  print("保存")
                }
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(Color.blue)
                .cornerRadius(25)
                Spacer()
              }
              .padding(.bottom, 20)
            }
          )
        }
        .frame(height: 300)
        .background(Color(.systemGroupedBackground))
        .cornerRadius(16)
      }
    }
    .padding()
  }
  .background(Color(.systemGroupedBackground))
}

// MARK: - 辅助函数

private func coloredContent(color: Color) -> some View {
  VStack(spacing: 6) {
    ForEach(0..<10, id: \.self) { index in
      HStack {
        Rectangle()
          .fill(color.opacity(0.8))
          .frame(width: 40, height: 12)
        Rectangle()
          .fill(color.opacity(0.6))
          .frame(height: 12)
      }
    }
  }
  .background(Color.white)
  .cornerRadius(12)
}
