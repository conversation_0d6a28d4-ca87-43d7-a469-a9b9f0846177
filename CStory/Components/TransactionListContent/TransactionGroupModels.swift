//
//  TransactionGroupModels.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/29.
//

import SwiftUI

// MARK: - 交易日期分组数据结构

/// 交易日期分组数据（包含完整的TransactionRowVM）
///
/// 用于交易列表的日期分组显示，包含某一天的所有交易及其收支统计，使用完整的TransactionRowVM。
/// 这个结构体被多个组件共享使用：
/// - TransactionListContent：统一的交易列表组件
/// - TransactionRecordView：交易记录页面
/// - CardDetailView：卡片详情页面
/// - HomeView：首页最近交易
struct TransactionDayGroupWithRowVM: Identifiable {
  /// 分组唯一标识符
  let id = UUID()

  /// 格式化的日期文本（如"今天"、"昨天"、"1月15日"）
  let dateText: String

  /// 当日收入总额（本位货币）
  let dayIncome: Double

  /// 当日支出总额（本位货币）
  let dayExpense: Double

  /// 当日交易的完整ViewModel数组
  let transactionRowVMs: [TransactionRowVM]
}

/// 交易日期分组数据（简化版，保留兼容性）
///
/// 用于不需要完整TransactionRowVM的场景，提供简化的交易数据结构。
/// 主要用于向后兼容和特定的UI显示需求。
struct TransactionDayGroupData: Identifiable {
  /// 分组唯一标识符
  let id = UUID()

  /// 格式化的日期文本（如"今天"、"昨天"、"1月15日"）
  let dateText: String

  /// 当日收入总额（本位货币）
  let dayIncome: Double

  /// 当日支出总额（本位货币）
  let dayExpense: Double

  /// 当日交易的简化数据（用于UI显示）
  let transactions: [(category: String, time: String, amount: Double, card: String)]
}
