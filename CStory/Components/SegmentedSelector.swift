//
//  SegmentedSelector.swift
//  CStory
//
//  Created by AI Assistant on 2025/8/1.
//

import SwiftUI

/// 通用分段选择器组件
///
/// 提供两个或多个选项之间的切换功能，支持：
/// - 动画切换效果
/// - 触觉反馈
/// - 按压动画
struct SegmentedSelector: View {

  // MARK: - Properties

  let options: [String]
  @Binding var selectedIndex: Int
  let onSelectionChanged: ((Int) -> Void)?

  /// 动画命名空间
  @Namespace private var animation

  /// 当前按压的选项索引
  @State private var pressingIndex: Int?

  // MARK: - Initialization

  init(
    options: [String],
    selectedIndex: Binding<Int>,
    onSelectionChanged: ((Int) -> Void)? = nil
  ) {
    self.options = options
    self._selectedIndex = selectedIndex
    self.onSelectionChanged = onSelectionChanged
  }

  // MARK: - Body

  var body: some View {
    HStack(spacing: 8) {
      ForEach(Array(options.enumerated()), id: \.offset) { index, option in
        optionButton(for: option, at: index)
      }
    }
    .padding(.horizontal, 2)
    .padding(.vertical, 2)
    .background(Color.cWhite)
    .cornerRadius(16)
    .overlay(
      RoundedRectangle(cornerRadius: 16)
        .strokeBorder(Color.cAccentBlue.opacity(0.08), lineWidth: 1)
    )
    .padding(.horizontal, 16)
    .padding(.vertical, 12)
  }

  // MARK: - Private Methods

  /// 创建选项按钮
  private func optionButton(for option: String, at index: Int) -> some View {
    Button {
      selectOption(at: index)
    } label: {
      Text(option)
        .font(.system(size: 16, weight: .medium))
        .foregroundColor(
          selectedIndex == index ? Color.cWhite : Color.cBlack.opacity(0.6)
        )
        .frame(maxWidth: .infinity)
        .frame(height: 34)
        .background(
          ZStack {
            if selectedIndex == index {
              Color.cAccentBlue
                .cornerRadius(14)
                .matchedGeometryEffect(id: "background", in: animation)
            }
          }
        )
        .scaleEffect(pressingIndex == index ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: pressingIndex)
    }
    .onLongPressGesture(
      minimumDuration: 0, maximumDistance: .infinity,
      pressing: { pressing in
        if pressing {
          pressingIndex = index
        } else {
          pressingIndex = nil
        }
      }, perform: {})
  }

  /// 选择选项
  private func selectOption(at index: Int) {
    guard selectedIndex != index else { return }

    // 触觉反馈
    HapticFeedbackManager.shared.trigger(.selection)

    // 动画切换
    withAnimation(.easeInOut(duration: 0.2)) {
      selectedIndex = index
    }

    // 回调通知
    onSelectionChanged?(index)
  }
}

// MARK: - TransactionType Extension

extension TransactionType {
  /// 显示名称
  var displayName: String {
    switch self {
    case .expense:
      return "支出"
    case .income:
      return "收入"
    case .transfer:
      return "转账"
    case .refund:
      return "退款"
    case .createCard:
      return "创建卡片"
    case .adjustCard:
      return "调整余额"
    }
  }
}

// MARK: - Preview

#Preview {
  VStack(spacing: 20) {
    // 简单的分段选择器
    SegmentedSelector(
      options: ["选项1", "选项2", "选项3"],
      selectedIndex: .constant(0),
      onSelectionChanged: { index in
        print("选择了选项: \(index)")
      }
    )

    // 交易类型选择器
    SegmentedSelector(
      options: ["支出", "收入"],
      selectedIndex: .constant(1),
      onSelectionChanged: { index in
        let type: TransactionType = index == 0 ? .expense : .income
        print("交易类型切换到: \(type)")
      }
    )
  }
  .padding()
  .background(Color.cLightBlue)
}
