//
//  DottedGridBackground.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftUI

/// 网格圆点背景组件
///
/// 创建带有圆点网格的背景装饰，常用于空状态页面、
/// 引导页面或作为视觉装饰元素。
///
/// ## 设计特点
/// - 基于 Shape 协议实现，性能高效
/// - 支持自定义圆点大小和间距
/// - 可配置颜色和背景
/// - 自适应容器大小
///
/// ## 使用示例
/// ```swift
/// // 默认样式
/// DottedGridBackground()
///
/// // 自定义样式
/// DottedGridBackground(
///     dotSize: 3,
///     spacing: 30,
///     dotColor: .blue.opacity(0.2),
///     backgroundColor: .gray.opacity(0.05)
/// )
/// ```
struct DottedGridBackground: View {
  let dotSize: CGFloat
  let spacing: CGFloat
  let dotColor: Color
  let backgroundColor: Color

  /// 初始化网格圆点背景
  /// - Parameters:
  ///   - dotSize: 圆点大小，默认为2
  ///   - spacing: 网格间距，默认为20
  ///   - dotColor: 圆点颜色，默认为黑色12%透明度
  ///   - backgroundColor: 背景颜色，默认为浅蓝色
  init(
    dotSize: CGFloat = 2,
    spacing: CGFloat = 20,
    dotColor: Color = Color.cBlack.opacity(0.12),
    backgroundColor: Color = Color.cLightBlue
  ) {
    self.dotSize = dotSize
    self.spacing = spacing
    self.dotColor = dotColor
    self.backgroundColor = backgroundColor
  }

  var body: some View {
    backgroundColor
      .overlay(
        DottedGridShape(dotSize: dotSize, spacing: spacing)
          .fill(dotColor)
      )
  }
}

/// 网格圆点形状
///
/// 通过 Shape 协议实现网格圆点的几何形状。
/// 使用路径绘制所有圆点，比使用多个视图更高效。
struct DottedGridShape: Shape {
  let dotSize: CGFloat
  let spacing: CGFloat

  init(dotSize: CGFloat = 2, spacing: CGFloat = 20) {
    self.dotSize = dotSize
    self.spacing = spacing
  }

  /// 创建圆点网格路径
  /// - Parameter rect: 绘制区域
  /// - Returns: 包含所有圆点的路径
  func path(in rect: CGRect) -> Path {
    var path = Path()

    // 计算需要的行列数
    let columns = Int(rect.width / spacing) + 1
    let rows = Int(rect.height / spacing) + 1

    // 遍历每个位置添加圆点
    for row in 0..<rows {
      for column in 0..<columns {
        let x = CGFloat(column) * spacing
        let y = CGFloat(row) * spacing

        // 创建圆点矩形
        let dotRect = CGRect(
          x: x - dotSize / 2,
          y: y - dotSize / 2,
          width: dotSize,
          height: dotSize
        )

        // 添加椭圆形
        path.addEllipse(in: dotRect)
      }
    }

    return path
  }
}

// MARK: - 预览

#Preview {
  VStack(spacing: 20) {
    // 默认样式
    DottedGridBackground()
      .frame(width: 300, height: 200)
      .cornerRadius(12)

    // 自定义大圆点
    DottedGridBackground(
      dotSize: 4,
      spacing: 30,
      dotColor: .blue.opacity(0.3),
      backgroundColor: .blue.opacity(0.05)
    )
    .frame(width: 300, height: 200)
    .cornerRadius(12)

    // 密集小圆点
    DottedGridBackground(
      dotSize: 1,
      spacing: 10,
      dotColor: .gray.opacity(0.4),
      backgroundColor: .white
    )
    .frame(width: 300, height: 200)
    .cornerRadius(12)
    .shadow(radius: 5)
  }
  .padding()
  .background(Color.gray.opacity(0.1))
}
