//
//  TitleKitVM.swift
//  CStory
//
//  Created by NZUE on 2025/7/22.
//

import SwiftUI

/// 标题套件视图模型
///
/// 负责标题组件的业务逻辑处理和状态管理，遵循新架构的MVVM模式。
/// 该类管理标题显示、按钮状态和用户交互逻辑。
///
/// ## 主要职责
/// - 标题文本管理和格式化
/// - 右侧按钮状态控制（显示/隐藏、启用/禁用）
/// - 用户交互处理（按钮点击事件）
/// - 样式配置管理（字体、颜色、间距）
///
/// ## 使用示例
/// ```swift
/// let viewModel = TitleKitVM(
///   title: "最近交易",
///   rightButtonTitle: "查看全部",
///   rightButtonAction: { ... }
/// )
/// ```
///
/// - Author: NZUE
/// - Version: 2.0 (新架构)
/// - Since: 2025.7.22
@MainActor
final class TitleKitVM: ObservableObject {

  // MARK: - Published Properties

  /// 标题文本
  @Published var title: String

  /// 右侧按钮标题
  @Published var rightButtonTitle: String?

  /// 右侧按钮是否启用
  @Published var isRightButtonEnabled: Bool = true

  /// 标题字体大小
  @Published var titleFontSize: CGFloat = 17

  /// 标题字体粗细
  @Published var titleFontWeight: Font.Weight = .medium

  /// 标题颜色
  @Published var titleColor: Color = .cBlack

  /// 水平内边距
  @Published var horizontalPadding: CGFloat = 16

  /// 顶部内边距
  @Published var topPadding: CGFloat = 24

  /// 底部内边距
  @Published var bottomPadding: CGFloat = 12

  // MARK: - Private Properties

  /// 右侧按钮点击回调
  private var rightButtonAction: (() -> Void)?

  // MARK: - Computed Properties

  /// 是否有右侧按钮
  var hasRightButton: Bool {
    rightButtonTitle != nil && rightButtonAction != nil
  }

  /// 标题字体配置
  var titleFont: Font {
    .system(size: titleFontSize, weight: titleFontWeight)
  }

  // MARK: - Initialization

  /// 标准初始化
  init(
    title: String,
    rightButtonTitle: String? = nil,
    rightButtonAction: (() -> Void)? = nil
  ) {
    self.title = title
    self.rightButtonTitle = rightButtonTitle
    self.rightButtonAction = rightButtonAction
  }

  // MARK: - Public Methods

  /// 处理右侧按钮点击
  func handleRightButtonTap() {
    guard isRightButtonEnabled else { return }
    rightButtonAction?()
  }

  /// 更新标题文本
  func updateTitle(_ newTitle: String) {
    title = newTitle
  }

  /// 更新右侧按钮
  func updateRightButton(title: String?, action: (() -> Void)?) {
    rightButtonTitle = title
    rightButtonAction = action
  }

  /// 移除右侧按钮
  func removeRightButton() {
    rightButtonTitle = nil
    rightButtonAction = nil
  }

  /// 设置右侧按钮启用状态
  func setRightButtonEnabled(_ enabled: Bool) {
    isRightButtonEnabled = enabled
  }

  /// 更新标题样式
  func updateTitleStyle(
    fontSize: CGFloat? = nil,
    fontWeight: Font.Weight? = nil,
    color: Color? = nil
  ) {
    if let fontSize = fontSize {
      self.titleFontSize = fontSize
    }
    if let fontWeight = fontWeight {
      self.titleFontWeight = fontWeight
    }
    if let color = color {
      self.titleColor = color
    }
  }

  /// 更新内边距
  func updatePadding(
    horizontal: CGFloat? = nil,
    top: CGFloat? = nil,
    bottom: CGFloat? = nil
  ) {
    if let horizontal = horizontal {
      self.horizontalPadding = horizontal
    }
    if let top = top {
      self.topPadding = top
    }
    if let bottom = bottom {
      self.bottomPadding = bottom
    }
  }

  // MARK: - Factory Methods

  /// 创建带"查看全部"按钮的标题ViewModel
  static func withViewAllButton(
    title: String,
    action: @escaping () -> Void
  ) -> TitleKitVM {
    return TitleKitVM(
      title: title,
      rightButtonTitle: "查看全部",
      rightButtonAction: action
    )
  }

  /// 创建纯标题ViewModel
  static func titleOnly(title: String) -> TitleKitVM {
    return TitleKitVM(title: title)
  }

  /// 创建自定义样式的标题ViewModel
  static func customStyle(
    title: String,
    fontSize: CGFloat = 16,
    fontWeight: Font.Weight = .medium,
    color: Color = .cBlack,
    rightButtonTitle: String? = nil,
    rightButtonAction: (() -> Void)? = nil
  ) -> TitleKitVM {
    let viewModel = TitleKitVM(
      title: title,
      rightButtonTitle: rightButtonTitle,
      rightButtonAction: rightButtonAction
    )
    viewModel.updateTitleStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color
    )
    return viewModel
  }
}
