//
//  FloatingActionButtonView.swift
//  CStory
//
//  Created by NZUE on 2025/12/19.
//

import SwiftUI

/// 浮动操作按钮视图
///
/// 采用MVVM架构的浮动操作按钮组件，支持单按钮和多按钮模式。
/// 通过ViewModel管理按钮状态、样式和交互逻辑。
///
/// ## 使用示例
/// ```swift
/// // 单按钮模式
/// FloatingActionButtonView(
///   viewModel: FloatingActionButtonViewModel(
///     title: "保存",
///     action: { ... }
///   )
/// )
///
/// // 多按钮模式
/// FloatingActionButtonView(
///   viewModel: FloatingActionButtonViewModel(
///     buttons: [button1, button2]
///   )
/// )
/// ```
///
/// - Author: NZUE
/// - Version: 2.0 (MVVM架构)
/// - Since: 2025.7.22
struct FloatingActionButtonView: View {
  @ObservedObject private var viewModel: FloatingActionButtonVM  // 改用@ObservedObject

  /// 使用ViewModel初始化
  init(viewModel: FloatingActionButtonVM) {
    self.viewModel = viewModel  // 直接赋值，不用StateObject包装
  }

  /// 单按钮便利初始化器
  init(
    title: String,
    action: @escaping () -> Void,
    style: FloatingActionButton.ButtonStyle = .primary,
    isEnabled: Bool = true,
    useBlurBackground: Bool = true
  ) {
    let vm = FloatingActionButtonVM(
      title: title,
      action: action,
      style: style,
      isEnabled: isEnabled,
      useBlurBackground: useBlurBackground
    )
    self.viewModel = vm  // 直接赋值
  }

  /// 图标按钮便利初始化器
  init(
    iconName: String,
    action: @escaping () -> Void,
    style: FloatingActionButton.ButtonStyle = .icon,
    isEnabled: Bool = true,
    useBlurBackground: Bool = true
  ) {
    let vm = FloatingActionButtonVM(
      iconName: iconName,
      action: action,
      style: style,
      isEnabled: isEnabled,
      useBlurBackground: useBlurBackground
    )
    self.viewModel = vm  // 直接赋值
  }

  /// 多按钮便利初始化器
  init(
    buttons: [FloatingActionButton],
    spacing: CGFloat = 32,
    useBlurBackground: Bool = true
  ) {
    let vm = FloatingActionButtonVM(
      buttons: buttons,
      spacing: spacing,
      useBlurBackground: useBlurBackground
    )
    self.viewModel = vm  // 直接赋值，每次都创建新的ViewModel
  }

  var body: some View {
    VStack {
      Spacer()
      ZStack {
        if viewModel.useBlurBackground {
          VariableBlurView(maxBlurRadius: 15, direction: .blurredBottomClearTop, startOffset: -0.05)
            .ignoresSafeArea()
            .frame(height: 64)
        }

        HStack(spacing: viewModel.spacing) {
          Spacer()

          ForEach(Array(viewModel.buttons.enumerated()), id: \.offset) { index, button in
            Button(action: {
              viewModel.handleButtonTap(at: index)
            }) {
              Group {
                if button.style == .icon, let iconName = button.iconName {
                  // 图标按钮
                  Image(iconName)
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(viewModel.getButtonForegroundColor(at: index))
                    .frame(width: 24, height: 24)
                    .padding(8)
                    .background(viewModel.getButtonBackgroundColor(at: index))
                    .cornerRadius(viewModel.getButtonCornerRadius(for: button.style))
                } else if let title = button.title {
                  // 文字按钮
                  Text(title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(viewModel.getButtonForegroundColor(at: index))
                    .padding(.horizontal, 36)
                    .frame(height: 40)
                    .background(viewModel.getButtonBackgroundColor(at: index))
                    .cornerRadius(viewModel.getButtonCornerRadius(for: button.style))
                }
              }
            }
            .disabled(!button.isEnabled)
          }

          Spacer()
        }
        .padding(.vertical, 12)
      }
    }
  }
}

// MARK: - 预览
#Preview("单按钮") {
  ZStack {
    Color.cLightBlue
      .ignoresSafeArea()

    VStack {
      Text("示例内容")
        .padding()
      Spacer()
    }

    // 单按钮示例 - 新的ViewModel方式
    FloatingActionButtonView(
      title: "保存",
      action: { print("保存") },
      style: .primary
    )
  }
}

#Preview("图标按钮") {
  ZStack {
    Color.cLightBlue
      .ignoresSafeArea()

    VStack {
      Text("示例内容")
        .padding()
      Spacer()
    }

    // 图标按钮示例
    FloatingActionButtonView(
      iconName: "plus",
      action: { print("添加") },
      style: .icon
    )
  }
}

#Preview("多按钮") {
  ZStack {
    Color.cLightBlue
      .ignoresSafeArea()

    VStack {
      Text("示例内容")
        .padding()
      Spacer()
    }

    // 多按钮示例 - 新的ViewModel方式
    FloatingActionButtonView(
      buttons: [
        FloatingActionButton(title: "取消", action: { print("取消") }, style: .secondary),
        FloatingActionButton(title: "保存", action: { print("保存") }, style: .primary),
      ]
    )
  }
}

#Preview("ViewModel方式") {
  ZStack {
    Color.cLightBlue
      .ignoresSafeArea()

    VStack {
      Text("使用ViewModel的示例")
        .padding()
      Spacer()
    }

    // 完全使用ViewModel的示例
    FloatingActionButtonView(
      viewModel: FloatingActionButtonVM(
        buttons: [
          FloatingActionButton(title: "删除", action: { print("删除") }, style: .destructive),
          FloatingActionButton(title: "编辑", action: { print("编辑") }, style: .secondary),
          FloatingActionButton(title: "完成", action: { print("完成") }, style: .primary),
        ],
        spacing: 20,
        useBlurBackground: true
      )
    )
  }
}
