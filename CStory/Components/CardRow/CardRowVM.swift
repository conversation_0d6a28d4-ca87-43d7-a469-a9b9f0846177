//
//  CardRowVM.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/18.
//

import Combine
import SwiftUI

/// 银行卡信息行视图模型
///
/// 管理银行卡行组件的业务逻辑和数据处理，遵循MVVM架构模式。
/// 负责卡片数据的格式化、状态管理和交互逻辑。
///
/// 该类将CardModel数据转换为视图可直接使用的格式，
/// 包括余额颜色计算、类型标签管理、状态控制等功能。
///
/// ## 主要功能
/// - 卡片数据格式化和计算
/// - 余额颜色和显示逻辑
/// - 选中状态管理
/// - 点击事件处理
///
/// ## 使用示例
/// ```swift
/// // 从CardModel创建
/// let viewModel = CardRowVM(
///   from: cardModel,
///   showTypeTag: true,
///   showAdditionalInfo: false
/// )
///
/// // 直接创建
/// let viewModel = CardRowVM(
///   cardName: "主卡",
///   bankName: "招商银行",
///   balance: 5000.0,
///   currencySymbol: "¥",
///   isCredit: true
/// )
/// ```
///
/// - Author: 咩咩
/// - Since: 2025.7.18
/// - Note: 该类支持动态数据更新，视图会自动响应属性变化
class CardRowVM: ObservableObject {

  // MARK: - Output Properties (for the View to use)
  // 这些是已经计算好的、可以直接在 View 中使用的最终数据

  @Published var cardName: String
  @Published var bankName: String
  @Published var bankLogo: Data?

  @Published var balance: Double
  @Published var currencySymbol: String
  @Published var balanceColor: Color

  @Published var isCredit: Bool
  @Published var hasTypeTag: Bool
  @Published var cardTypeText: String

  @Published var isSelected: Bool
  @Published var additionalInfo: String?

  // 回调函数
  var onTap: (() -> Void)?

  // MARK: - 初始化方法

  init(
    cardName: String,
    bankName: String,
    bankLogo: Data? = nil,
    balance: Double,
    currencySymbol: String,
    isCredit: Bool,
    hasTypeTag: Bool = false,
    isSelected: Bool = false,
    additionalInfo: String? = nil,
    onTap: (() -> Void)? = nil
  ) {
    self.cardName = cardName
    self.bankName = bankName
    self.bankLogo = bankLogo
    self.balance = balance
    self.currencySymbol = currencySymbol
    self.isCredit = isCredit
    self.hasTypeTag = hasTypeTag
    self.isSelected = isSelected
    self.additionalInfo = additionalInfo
    self.onTap = onTap

    // 计算派生属性
    self.cardTypeText = isCredit ? "信用卡" : "储蓄卡"
    self.balanceColor = Self.calculateBalanceColor(balance: balance, isCredit: isCredit)
  }

  // MARK: - 便利初始化方法 (从CardModel创建)

  /// 从CardModel创建CardRowVM
  /// - Parameters:
  ///   - card: 卡片模型
  ///   - isSelected: 是否选中状态
  ///   - showTypeTag: 是否显示类型标签
  ///   - showAdditionalInfo: 是否显示附加信息
  ///   - onTap: 点击回调
  convenience init(
    from card: CardModel,
    isSelected: Bool = false,
    showTypeTag: Bool = true,
    showAdditionalInfo: Bool = false,
    onTap: (() -> Void)? = nil
  ) {
    var additionalInfo: String?

    if showAdditionalInfo {
      if card.isCredit {
        // 信用卡显示额度信息
        additionalInfo =
          "额度: \(CurrencyService.shared.formatCurrency(card.credit, symbol: card.symbol, showDecimals: true))"
      } else {
        // 储蓄卡可以显示其他信息，如外币标识
        if card.currency != "CNY" {
          additionalInfo = "外币账户"
        }
      }
    }

    self.init(
      cardName: card.name,
      bankName: card.bankName.isEmpty ? (card.isCredit ? "信用卡" : "储蓄卡") : card.bankName,
      bankLogo: card.bankLogo,
      balance: card.balance,
      currencySymbol: card.symbol,
      isCredit: card.isCredit,
      hasTypeTag: showTypeTag,
      isSelected: isSelected,
      additionalInfo: additionalInfo,
      onTap: onTap
    )
  }

  // MARK: - 私有辅助方法

  /// 计算余额显示颜色
  /// - Parameters:
  ///   - balance: 余额
  ///   - isCredit: 是否为信用卡
  /// - Returns: 显示颜色
  private static func calculateBalanceColor(balance: Double, isCredit: Bool) -> Color {
    if isCredit {
      // 信用卡：负数表示欠款，正数表示溢缴款
      return balance < 0 ? .cBlack : .cAccentGreen
    } else {
      // 储蓄卡：负数表示透支，正数表示余额
      return balance < 0 ? .cAccentRed : .cBlack
    }
  }

  // MARK: - 公共方法

  /// 更新选中状态
  /// - Parameter selected: 新的选中状态
  func updateSelection(_ selected: Bool) {
    isSelected = selected
  }

  /// 更新余额
  /// - Parameter newBalance: 新余额
  func updateBalance(_ newBalance: Double) {
    balance = newBalance
    balanceColor = Self.calculateBalanceColor(balance: newBalance, isCredit: isCredit)
  }
}

// MARK: - 扩展方法

extension CardRowVM {

  /// 获取格式化的余额文本（用于其他用途）
  var formattedBalance: String {
    return CurrencyService.shared.formatCurrency(
      balance, symbol: currencySymbol, showDecimals: true)
  }

  /// 获取卡片状态描述
  var statusDescription: String {
    if isCredit {
      return balance < 0 ? "欠款" : "溢缴"
    } else {
      return balance < 0 ? "透支" : "余额"
    }
  }
}
