# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 在此代码库中工作时提供指导。

## 项目概述

CStory 是一个基于 SwiftUI 和 SwiftData 构建的 iOS 财务管理应用，专注于信用卡和交易管理，并支持多货币功能和AI辅助记账。

### 核心特性
- 信用卡账单周期管理和智能提醒
- 多货币汇率转换和历史记录追踪
- AI智能交易识别和自动记录
- 层次化交易分类和可视化统计
- iCloud数据同步和多设备支持
- 丰富的资产可视化和洞察分析

## 架构设计

### 核心组件
- **SwiftData 模型**: `CardModel`、`TransactionModel`、`TransactionCategoryModel`、`CurrencyModel`、`ChatModel`
- **响应式数据管理**: `DataManagement` 中心化的数据管理器，提供实时数据同步
- **MVVM 架构**: 严格的视图-模型分离，每个组件都有对应的ViewModel
- **导航系统**: 使用 `PathManagerHelper` 和 `NavigationDestination` 枚举进行类型安全的导航
- **服务层**: `Service/` 中的专业化业务服务（货币转换、余额计算、交易查询、AI处理）
- **AI智能服务**: `AIService`、`AIPromptService`、`ChatService` 提供多模态AI记账功能
- **辅助系统**: `Helpers/` 中的系统级工具（权限管理、触觉反馈、图像处理、语音识别）

### 关键目录结构
```
CStory/
├── Models/                    # SwiftData 模型定义
│   ├── Card/                 # 卡片相关模型
│   ├── Transaction/          # 交易相关模型
│   │   ├── TransactionModel.swift
│   │   ├── TransactionCategory/  # 分类模型
│   │   └── Currency/         # 货币模型
│   ├── ChatModel.swift       # AI聊天模型
│   └── Enums/               # 核心枚举定义
├── Page/                     # 页面层（原Views）
│   ├── Card/                # 信用卡管理
│   │   ├── CardBag/         # 卡包视图
│   │   └── CardCategory/    # 卡片分类
│   ├── Transaction/         # 交易管理
│   │   ├── CreatTransaction/ # 创建交易
│   │   │   ├── AITransaction/    # AI智能记账
│   │   │   └── ManualTransaction/ # 手动记账
│   │   ├── TransactionDetail/    # 交易详情
│   │   ├── TransactionRecord/    # 交易记录
│   │   └── TransactionRefund/    # 退款处理
│   ├── Home/               # 主页和仪表板
│   ├── Setting/            # 设置和配置
│   │   ├── CategoryForm/   # 分类表单
│   │   └── CurrencyRate/   # 汇率设置
│   ├── Sheet/              # 弹窗和操作表单
│   │   ├── ActionSheet/    # 操作表单
│   │   ├── SelectSheet/    # 选择表单
│   │   └── Components/     # 通用组件
│   └── SelectBank/         # 银行选择
├── Components/              # 可重用UI组件库
│   ├── ActionButton/       # 标准操作按钮
│   ├── NavigationBarKit/   # 自定义导航栏
│   ├── NumericKeypad/      # 数字键盘
│   ├── FloatingActionButtonView/ # 悬浮按钮
│   └── [其他组件...]
├── Service/                # 核心业务服务
│   ├── CurrencyService.swift      # 货币服务
│   ├── TransactionQueryService.swift # 交易查询
│   ├── BalanceRecalculationService.swift # 余额重算
│   ├── AIService.swift            # AI服务
│   ├── AIPromptService.swift      # AI提示词
│   └── ChatService.swift          # 聊天服务
├── Helpers/                # 系统级辅助工具
│   ├── HapticFeedbackManager.swift # 触觉反馈
│   ├── SpeechRecognitionHelper.swift # 语音识别
│   ├── ImageLoaderHelper.swift    # 图像处理
│   └── PathManagerHelper.swift    # 导航管理
├── Utils/                  # 应用级工具类
├── Extensions/             # 扩展
├── Resources/              # 资源文件
│   ├── BankList.json      # 银行列表
│   ├── TransactionCategoryDefaults.json # 交易分类
│   ├── CurrencyMappings.json # 货币映射
│   └── exchange_rates.json   # 汇率数据
└── DataManagement.swift    # 中心化数据管理
```

## 核心功能详述

### 多货币系统
- **历史汇率机制**: 每笔交易存储创建时的汇率，确保历史数据准确性
- **智能汇率转换**: 支持190+种货币，常用货币优先显示
- **本位币设置**: 灵活的基础货币切换，自动重新计算资产
- **汇率数据源**: 基于 `exchange_rates.json` 的实时汇率更新

### 信用卡智能管理
- **账单周期计算**: 支持月末账单日和固定日期两种模式
- **还款模式**: 固定还款日期 vs 账单日后X天
- **自动日期计算**: 智能计算下一个账单日和还款日
- **余额追踪**: 区分信用卡欠款和溢缴款，储蓄卡正负余额

### AI智能记账
- **多模态输入**: 支持文本描述、图像识别、语音转录
- **智能提取**: 自动识别交易金额、商家名称、交易分类
- **上下文理解**: 基于用户历史数据生成个性化提示词
- **图像处理**: HEIF/JPEG格式支持，压缩优化存储

### 交易分类系统
- **统一分类模型**: 使用 `TransactionCategoryModel` 实现层次化分类
- **图标系统**: 支持emoji和自定义图片图标，通过 `IconType` 枚举管理
- **JSON驱动**: 基于 `TransactionCategoryDefaults.json` 的灵活配置
- **动态管理**: 完整的CRUD操作，支持分类创建、编辑、删除
- **智能匹配**: AI系统基于历史数据智能推荐分类

### 响应式数据管理
- **中心化管理**: `DataManagement` 作为单一数据源，实现响应式架构
- **SwiftData集成**: 现代化的数据持久化方案，完全替代Core Data
- **iCloud同步**: 无缝的多设备数据同步，支持离线操作
- **实时更新**: 所有数据变更自动传播到相关组件
- **初始化保护**: 智能的重复初始化检测和保护机制
- **JSON种子数据**: 银行列表、交易分类、货币映射、汇率数据
- **线程安全**: 完善的多线程数据访问保护

## AI功能实现

### 服务架构
- **AIService**: 与豆包AI API通信，处理多模态输入
- **AIPromptService**: 动态生成上下文相关的系统提示词
- **智能解析**: 结构化JSON响应解析，自动填充交易表单

### AI组件生态
- **ChatMessageView**: 对话式AI交互界面
- **AIMenuView**: AI功能入口和菜单
- **ImageGalleryView**: 图片选择、预览和管理
- **AudioWaveformView**: 语音输入波形可视化
- **CameraPicker/ImagePicker**: 相机拍照和相册选择

### 隐私和安全
- **本地处理**: 图像压缩和预处理在设备端完成
- **数据最小化**: 仅传输必要的交易相关信息
- **临时存储**: AI交互数据仅作会话使用，不长期存储

## 视图层架构

### UI组件设计
- **组合式架构**: 小型、可重用的组件构建复杂界面
- **主题一致性**: 统一的颜色、字体、间距设计系统
- **响应式布局**: 适配不同屏幕尺寸和方向

### 核心组件库
```swift
// 基础UI组件
- ActionButton: 标准化的操作按钮（支持MVVM）
- NavigationBarKit: 自定义导航栏组件
- TitleKit: 统一的标题组件
- IconView: 智能图标显示组件

// 数据展示组件
- DisplayCurrencyView: 多货币金额显示
- HomeStatisticCard: 统计数据卡片
- HomeMultiCircularView: 环形进度图表
- TransactionRow: 交易列表行组件
- CardRow: 卡片列表行组件

// 交互控制组件
- NumericKeypad: 智能数字键盘（支持表达式计算）
- FloatingActionButtonView: 悬浮操作按钮
- TimeControl: 时间选择控制器
- VariableBlurView: 动态模糊效果

// 数据输入组件
- SetRow: 设置行组件
- IncomeExpenseCard: 收支卡片
- TransactionListContent: 交易列表内容
```

### 表单和选择器系统
- **SelectSheet系列**: 标准化的选择界面
  - `SelectCardSheet`: 卡片选择
  - `SelectCategorySheet`: 分类选择
  - `SelectCurrencySheet`: 货币选择
  - `SelectTimeSheet`: 时间选择
  - `SelectCoverSheet`: 封面选择
- **ActionSheet系列**: 操作确认和快捷功能
  - `CardActionSheet`: 卡片操作
  - `CategoryActionSheet`: 分类操作
  - `CardFilterSheet`: 卡片筛选
- **智能表单**: 完整的表单验证和自动填充
  - 实时输入验证和错误提示
  - AI辅助的智能表单填充
  - 表达式计算支持

## 开发工具和命令

### 构建系统
```bash
# 标准构建
xcodebuild -project CStory.xcodeproj -scheme CStory -configuration Debug build

# 指定模拟器构建
xcodebuild -project CStory.xcodeproj -scheme CStory -configuration Debug build -destination 'platform=iOS Simulator,name=iPhone 16'

# 清理构建缓存
xcodebuild -project CStory.xcodeproj -scheme CStory clean
```

### 测试执行
```bash
# 单元测试
xcodebuild test -project CStory.xcodeproj -scheme CStory -destination 'platform=iOS Simulator,name=iPhone 16'

# UI自动化测试
xcodebuild test -project CStory.xcodeproj -scheme CStory -destination 'platform=iOS Simulator,name=iPhone 16' -only-testing:CStoryUITests
```

### 支持的设备
- **iPhone**: 16, 16 Plus, 16 Pro, 16 Pro Max
- **iPad**: Air 11/13-inch (M3), Pro 11/13-inch (M4)

## 数据模型深度解析

### 关系设计
```
CardModel (1) ←→ (N) TransactionModel
TransactionModel (N) ←→ (1) TransactionCategoryModel
CurrencyModel (独立) - 通过currency字段关联
ChatModel (独立) - 通过transactionIds关联

// 新增服务层关系
DataManagement ← 所有ViewModels（响应式数据注入）
CurrencyService ← 多货币相关组件
TransactionQueryService ← 所有交易查询操作
BalanceRecalculationService ← 余额计算操作
```

### 核心业务逻辑
- **余额计算**: 基于交易历史的精确余额重算机制
- **汇率转换**: 四重汇率存储（支出/收入 × 卡片/本位币）
- **时间戳管理**: 统一的 `updateTimestamp()` 更新机制

### JSON配置系统
- **BankList.json**: 全球银行列表和logo资源
- **PopularBanks.json**: 热门银行快速访问配置
- **CardsCategoryDefaults.json**: 卡片类型和分类预设
- **TransactionCategoryDefaults.json**: 预定义交易分类体系（层次化结构）
- **CurrencyMappings.json**: 190+种货币的完整映射（代码、名称、符号、区域）
- **exchange_rates.json**: 实时汇率数据源（支持历史汇率保护）

## 开发最佳实践

### 代码组织原则
- **单一职责**: 每个类、方法都有明确的单一职责
- **依赖注入**: 服务通过参数传递，提高可测试性
- **状态管理**: 使用 @State, @StateObject, @ObservedObject 进行状态管理
- **错误处理**: 完善的错误处理和用户反馈机制
- **文档规范**: 遵循 [Swift 文档注释规范](./Swift-Documentation-Guidelines.md)，使用 `///` 编写简洁的文档注释

### SwiftUI最佳实践
- **视图分解**: 复杂视图拆分为小型、可重用的组件
- **性能优化**: 适当使用 @ViewBuilder, onAppear/onDisappear
- **内存管理**: 图像处理注意内存释放，大数据集使用懒加载
- **动画设计**: 使用 withAnimation 创建流畅的用户体验

### AI集成指南
- **网络处理**: 实现重试机制和超时处理
- **错误恢复**: AI服务失败时的优雅降级策略
- **性能考虑**: 图像压缩、批量处理、异步调用
- **用户体验**: 加载状态、进度指示、结果验证

### 数据安全
- **敏感数据**: 避免在日志中记录用户敏感信息
- **API密钥**: 使用环境变量或配置文件管理API密钥
- **数据验证**: 对外部数据输入进行验证和清理
- **备份策略**: 依赖iCloud同步，但提供本地备份选项

## Git工作流程

### 提交规范
遵循 `COMMIT_CONVENTION.md` 中的约定：
- 格式：`<type>(<scope>): <中文主题>`
- 类型：feat、fix、docs、style、refactor、perf、test、chore、ci
- `<type>(<scope>)` 使用英文，主题和正文使用中文
- **禁止添加**: 自动生成标识或Co-Authored-By标记

### 分支策略
- `main`: 稳定的发布分支
- `develop`: 开发集成分支
- `feature/*`: 功能开发分支
- `fix/*`: 问题修复分支

### 质量检查
- 代码提交前运行完整构建和测试
- 确保新功能不破坏现有功能
- AI功能需要在真机上验证
- 多货币功能需要汇率准确性测试

## 故障排查指南

### 常见问题
1. **构建失败**: 检查Xcode版本兼容性，清理DerivedData
2. **SwiftData同步**: 确认iCloud Drive启用，检查网络连接
3. **AI服务异常**: 验证API密钥和网络连接，检查请求格式
4. **汇率计算错误**: 验证JSON数据格式，检查除零保护

### 调试技巧
- 使用Xcode内置的SwiftData查看器检查数据模型
- 通过Network Link Conditioner测试弱网络环境
- 使用Instruments分析内存使用和性能瓶颈

这个架构文档为CStory应用提供了全面的开发指导，涵盖了从核心架构到具体实现细节的各个层面，是一个功能丰富、设计精良的现代iOS财务管理应用的典型案例。