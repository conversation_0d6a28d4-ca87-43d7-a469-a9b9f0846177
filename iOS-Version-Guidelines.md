# iOS 版本号规范指南

## 概述

本文档为 CStory 应用制定了完整的版本号管理规范，确保符合 Apple 要求和行业最佳实践。

## 核心概念

### 版本号字段说明

iOS 应用中有两个关键的版本号字段：

1. **CFBundleShortVersionString** (Marketing Version)
   - 面向用户的版本号，在 App Store 和设置中显示
   - 对应 Xcode 中的 `Marketing Version`
   - 对应构建设置中的 `MARKETING_VERSION`

2. **CFBundleVersion** (Build Number)
   - 系统内部版本号，用于版本比较和更新检测
   - 对应 Xcode 中的 `Current Project Version`
   - 对应构建设置中的 `CURRENT_PROJECT_VERSION`

## 版本号格式规范

### CFBundleShortVersionString（用户版本）

**格式**: `MAJOR.MINOR.PATCH`

- **MAJOR**: 主版本号（重大功能更新、界面重设计）
- **MINOR**: 次版本号（新功能、改进）
- **PATCH**: 修订版本号（错误修复、小改进）

**示例**:
- `1.0.0` - 首次发布
- `1.1.0` - 添加新功能
- `1.1.1` - 错误修复
- `2.0.0` - 重大版本更新

**规则**:
- 必须是三个用点分隔的非负整数
- 不支持预发布标识符（如 `-alpha`, `-beta`, `-rc`）
- 字符长度没有严格限制，但建议保持简洁

### CFBundleVersion（构建版本）

**格式**: 递增整数或三段式版本号

**方案一：简单递增整数**
```
1, 2, 3, 4, 5, ...
```

**方案二：三段式版本号**
```
1.0.0, 1.0.1, 1.1.0, 2.0.0, ...
```

**方案三：时间戳方式**
```
2025012501, 2025012502, ...
```

**规则**:
- 最多 18 个字符
- 只能包含数字和点号（最多两个点）
- 第一个数字必须大于 0
- 每次提交必须递增
- App Store 使用语义化版本比较

## 推荐的版本策略

### 建议采用方案

**CFBundleShortVersionString**: 语义化版本 `MAJOR.MINOR.PATCH`
**CFBundleVersion**: 简单递增整数

### 版本更新规则

#### 主版本号（MAJOR）更新时机：
- 应用架构重大重构
- 用户界面全面重设计
- 核心功能重大变更
- 不兼容的 API 变更

#### 次版本号（MINOR）更新时机：
- 添加新功能模块
- 现有功能重要改进
- 添加新的用户交互方式
- 向后兼容的功能性变更

#### 修订版本号（PATCH）更新时机：
- 错误修复
- 性能优化
- 用户体验小改进
- 文案修正

### 构建版本号管理

每次构建都应该递增 CFBundleVersion：
- 开发构建：+1
- 测试构建：+1
- 发布构建：+1

## 实际应用示例

### CStory 应用版本规划

```
1.0.0 (Build 1)   - 首次发布版本
1.0.1 (Build 5)   - 修复关键错误
1.1.0 (Build 12)  - 添加AI记账功能
1.1.1 (Build 15)  - AI功能错误修复
1.2.0 (Build 23)  - 添加多货币支持
2.0.0 (Build 45)  - 全新UI设计和架构
```

## 在代码中获取版本信息

### Swift 代码示例

```swift
extension Bundle {
    /// 获取应用版本号（CFBundleShortVersionString）
    var appVersion: String {
        return infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0"
    }
    
    /// 获取构建版本号（CFBundleVersion）
    var buildVersion: String {
        return infoDictionary?["CFBundleVersion"] as? String ?? "1"
    }
    
    /// 获取完整版本信息
    var fullVersion: String {
        return "\(appVersion) (\(buildVersion))"
    }
}
```

### SwiftUI 中显示版本信息

```swift
struct VersionView: View {
    var body: some View {
        VStack {
            Text("版本 \(Bundle.main.appVersion)")
                .font(.system(size: 14))
                .foregroundColor(.secondary)
            
            Text("构建 \(Bundle.main.buildVersion)")
                .font(.system(size: 12))
                .foregroundColor(.tertiary)
        }
    }
}
```

## App Store 发布注意事项

### 版本号要求
- CFBundleShortVersionString 必须高于当前 App Store 版本
- CFBundleVersion 必须高于所有历史版本（包括被拒绝的版本）
- 不能使用已经提交过的版本号组合

### 版本回退
- App Store 不允许版本号回退
- 如果版本号设置错误，只能继续递增
- 建议在 CI/CD 中自动化版本号管理

## 自动化版本管理

### Xcode Build Script 示例

```bash
#!/bin/bash

# 获取当前构建版本号
CURRENT_BUILD=$(xcodebuild -showBuildSettings | grep CURRENT_PROJECT_VERSION | awk '{print $3}')

# 递增构建版本号
NEW_BUILD=$((CURRENT_BUILD + 1))

# 更新构建版本号
xcrun agvtool new-version -all $NEW_BUILD

echo "Build version updated to: $NEW_BUILD"
```

### Git Hook 自动化

```bash
#!/bin/bash
# pre-commit hook
# 自动递增构建版本号

BUILD_NUM=$(xcodebuild -showBuildSettings | grep CURRENT_PROJECT_VERSION | awk '{print $3}')
NEW_BUILD=$((BUILD_NUM + 1))
xcrun agvtool new-version -all $NEW_BUILD

git add "$(find . -name 'Info.plist')"
```

## 错误处理和故障排除

### 常见问题
1. **版本号冲突**: 确保每次提交都递增版本号
2. **格式错误**: 检查版本号格式是否符合规范
3. **字符限制**: CFBundleVersion 不能超过 18 个字符
4. **比较逻辑**: 理解 Apple 的版本比较规则

### 调试工具
```bash
# 查看当前版本设置
xcodebuild -showBuildSettings | grep -E "(MARKETING_VERSION|CURRENT_PROJECT_VERSION)"

# 验证 Info.plist 中的版本信息
/usr/libexec/PlistBuddy -c "Print CFBundleShortVersionString" Info.plist
/usr/libexec/PlistBuddy -c "Print CFBundleVersion" Info.plist
```

## 最佳实践总结

1. **保持一致性**: 在整个项目生命周期中使用统一的版本策略
2. **自动化管理**: 使用脚本或 CI/CD 自动递增版本号
3. **文档记录**: 在发布说明中清楚记录每个版本的变更
4. **测试验证**: 在发布前验证版本号设置正确
5. **备份策略**: 保留版本历史记录，便于问题追踪

## 相关资源

- [Apple Developer Documentation - CFBundleVersion](https://developer.apple.com/documentation/bundleresources/information-property-list/cfbundleversion)
- [Semantic Versioning 2.0.0](https://semver.org/)
- [App Store Connect Guidelines](https://developer.apple.com/app-store-connect/)

---

*本文档最后更新：2025-01-25*
*适用版本：iOS 17+, Xcode 15+*