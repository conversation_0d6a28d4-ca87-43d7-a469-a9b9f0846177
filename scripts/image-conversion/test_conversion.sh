#!/bin/bash

#===============================================================================
# PNG to HEIF 转换测试脚本
#===============================================================================
#
# 用途: 测试PNG到HEIF转换功能，验证环境配置和转换效果
# 作者: Claude Code Assistant  
# 版本: v1.0.0
# 日期: 2025-07-31
#
# 功能特性:
# - 安全的单文件转换测试
# - 验证Python依赖环境
# - 对比转换前后文件大小和质量
# - 不影响项目原始文件
#
# 使用方法:
#   1. 确保已安装Python依赖: pip3 install Pillow pillow-heif
#   2. 修改下方配置部分的路径设置  
#   3. 执行脚本: ./test_conversion.sh
#
# 注意事项:
# - 此脚本仅用于测试，不会永久修改项目文件
# - 测试完成后会自动清理临时文件
#
#===============================================================================

# 错误处理: 遇到错误时退出脚本
set -e

#===============================================================================
# 配置区域 - 请根据实际情况修改以下路径
#===============================================================================

# 项目Assets.xcassets路径
ASSETS_PATH="/Users/<USER>/代码/CStory/CStory/Assets.xcassets"

# 临时测试备份目录（自动添加时间戳）
TEST_BACKUP_PATH="/Users/<USER>/代码/CStory/test_backup_$(date +%Y%m%d_%H%M%S)"

# 转换质量设置（与主脚本保持一致）
HEIF_QUALITY=95

# 测试目录优先级（按顺序查找第一个可用的PNG文件）
TEST_DIRECTORIES=(
    "CardBackground"
    "CardCategoryIcons"
    "bank_icon"
)

#===============================================================================
# 初始化和环境检查
#===============================================================================

echo "=========================================="
echo "PNG to HEIF 转换测试工具 v1.0.0"
echo "=========================================="
echo ""

# 创建临时测试备份目录
echo "📁 创建测试备份目录: $TEST_BACKUP_PATH"
mkdir -p "$TEST_BACKUP_PATH"

#===============================================================================
# 环境依赖检查
#===============================================================================

echo "🔍 检查环境依赖..."

# 检查Python3
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到python3，请先安装Python 3"
    exit 1
fi

# 检查PIL和pillow-heif依赖
if ! python3 -c "
try:
    from PIL import Image
    import pillow_heif
    print('✅ Python依赖检查通过')
except ImportError as e:
    print(f'❌ 缺少Python依赖: {e}')
    print('请安装: pip3 install Pillow pillow-heif')
    exit(1)
" 2>/dev/null; then
    echo "❌ Python依赖检查失败"
    echo "💡 解决方案: pip3 install Pillow pillow-heif"
    exit 1
fi

#===============================================================================
# 查找测试文件
#===============================================================================

echo ""
echo "🔍 查找测试文件..."

test_file=""

# 按优先级查找第一个可用的PNG文件
for dir in "${TEST_DIRECTORIES[@]}"; do
    search_dir="$ASSETS_PATH/$dir"
    
    if [[ -d "$search_dir" ]]; then
        # 查找该目录下的第一个PNG文件
        found_file=$(find "$search_dir" -name "*.png" -type f | head -1)
        
        if [[ -n "$found_file" ]]; then
            test_file="$found_file"
            echo "✅ 找到测试文件: $test_file"
            break
        fi
    fi
done

# 检查是否找到测试文件
if [[ -z "$test_file" ]]; then
    echo "❌ 错误: 在指定目录中未找到PNG文件进行测试"
    echo "📂 搜索目录: ${TEST_DIRECTORIES[*]}"
    echo "💡 请检查路径配置或确保目录中包含PNG文件"
    
    # 清理临时目录
    rm -rf "$TEST_BACKUP_PATH"
    exit 1
fi

#===============================================================================
# 文件信息分析
#===============================================================================

echo ""
echo "📊 原文件信息分析:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# 显示文件基本信息
echo "📄 文件路径: $test_file"
echo "📏 文件大小: $(ls -lh "$test_file" | awk '{print $5}')"
echo "🎨 文件类型: $(file "$test_file" | cut -d: -f2 | xargs)"

# 获取文件大小（字节）
original_size=$(stat -f%z "$test_file")
echo "💾 精确大小: $original_size bytes"

#===============================================================================
# 执行转换测试
#===============================================================================

echo ""
echo "🔄 开始转换测试..."
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# 备份原文件
echo "📋 备份原文件..."
cp "$test_file" "$TEST_BACKUP_PATH/"

# 生成HEIF文件路径
heif_file="${test_file%.png}.heic"
echo "🎯 目标文件: $heif_file"

# 执行转换
echo "⚙️  执行转换 (质量: $HEIF_QUALITY%)..."

if python3 -c "
import sys
from PIL import Image
import pillow_heif

# 注册HEIF格式支持
pillow_heif.register_heif_opener()

try:
    # 打开PNG文件
    img = Image.open('$test_file')
    
    # 显示图片信息
    print(f'📐 图片尺寸: {img.size[0]}x{img.size[1]}')
    print(f'🎨 颜色模式: {img.mode}')
    
    # 处理RGBA透明背景（HEIF不完全支持透明度）
    if img.mode == 'RGBA':
        print('🔧 检测到透明背景，转换为白色背景')
        # 创建白色背景
        background = Image.new('RGB', img.size, (255, 255, 255))
        background.paste(img, mask=img.split()[-1])
        img = background
    
    # 转换为RGB模式（确保兼容性）
    if img.mode != 'RGB':
        print(f'🔧 转换颜色模式: {img.mode} -> RGB')
        img = img.convert('RGB')
    
    # 保存为HEIF格式
    img.save('$heif_file', 'HEIF', quality=$HEIF_QUALITY)
    print('✅ 转换成功完成')
    
except Exception as e:
    print(f'❌ 转换失败: {e}', file=sys.stderr)
    sys.exit(1)
" 2>/dev/null; then

    echo ""
    echo "🎉 转换成功！"
    
    #===========================================================================
    # 转换结果分析
    #===========================================================================
    
    echo ""
    echo "📊 转换结果分析:"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    # 显示新文件信息
    echo "📄 HEIF文件: $heif_file"
    echo "📏 文件大小: $(ls -lh "$heif_file" | awk '{print $5}')"
    echo "🎨 文件类型: $(file "$heif_file" | cut -d: -f2 | xargs)"
    
    # 获取新文件大小并计算节省空间
    new_size=$(stat -f%z "$heif_file")
    echo "💾 精确大小: $new_size bytes"
    
    if [[ $new_size -lt $original_size ]]; then
        savings=$((original_size - new_size))
        percentage=$(( (savings * 100) / original_size ))
        echo ""
        echo "💰 空间节省分析:"
        echo "   节省空间: $savings bytes"
        echo "   节省比例: $percentage%"
        echo "   压缩效果: $(echo "scale=1; $new_size * 100 / $original_size" | bc)% 的原始大小"
    elif [[ $new_size -gt $original_size ]]; then
        increase=$((new_size - original_size))
        percentage=$(( (increase * 100) / original_size ))
        echo ""
        echo "⚠️  文件大小增加:"
        echo "   增加空间: $increase bytes"
        echo "   增加比例: $percentage%"
        echo "💡 某些简单图片转换为HEIF可能增大，这是正常现象"
    else
        echo ""
        echo "ℹ️  文件大小相同"
    fi
    
    #===========================================================================
    # 图片质量验证提示
    #===========================================================================
    
    echo ""
    echo "🔍 质量验证建议:"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "1. 使用图片查看器对比原文件和转换后文件"
    echo "2. 检查颜色是否有明显差异"
    echo "3. 验证透明背景处理是否符合预期"
    echo "4. 在实际设备上测试显示效果"
    
    #===========================================================================
    # 清理测试文件
    #===========================================================================
    
    echo ""
    echo "🧹 清理测试文件..."
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    # 删除生成的HEIF文件（恢复原状态）
    rm "$heif_file"
    echo "✅ 已删除测试生成的HEIF文件"
    
    # 显示备份信息（虽然没有实际使用，但已创建了备份）
    echo "📁 测试备份已保存: $TEST_BACKUP_PATH"
    echo "   原文件备份: $(basename "$test_file")"
    
else
    echo ""
    echo "❌ 转换测试失败"
    echo ""
    echo "🔧 故障排除建议:"
    echo "1. 检查Python依赖安装: pip3 install --upgrade Pillow pillow-heif"
    echo "2. 确认文件权限正常"
    echo "3. 检查磁盘空间是否充足"
    echo "4. 尝试转换其他PNG文件"
    
    # 清理临时目录
    rm -rf "$TEST_BACKUP_PATH"
    exit 1
fi

#===============================================================================
# 测试总结和下一步建议
#===============================================================================

echo ""
echo "=========================================="
echo "🎊 测试完成！"
echo "=========================================="
echo ""
echo "✅ 环境配置正常"
echo "✅ 转换功能正常"
echo "✅ 文件处理安全"
echo ""
echo "🚀 下一步操作:"
echo "   1. 如果测试结果满意，可运行完整转换:"
echo "      ./scripts/image-conversion/convert_png_to_heif.sh"
echo ""
echo "   2. 如需调整设置，请编辑主转换脚本中的配置"
echo ""
echo "   3. 建议在转换前提交当前代码到版本控制"

#===============================================================================
# 清理测试备份
#===============================================================================

echo ""
echo "🧹 清理测试备份目录..."
rm -rf "$TEST_BACKUP_PATH"
echo "✅ 测试环境清理完成"

echo ""
echo "感谢使用PNG to HEIF转换测试工具！🎉"