# PNG to HEIF 图片转换工具

这是一个专为 iOS 项目设计的批量图片格式转换工具，可以将 Assets.xcassets 中的 PNG 图片转换为 HEIF 格式，以减少应用包体积。

## 📊 转换效果

- **文件大小减少**: 平均节省 40-50% 的存储空间
- **图片质量**: 保持 95% 的高质量
- **支持透明度**: 自动处理 RGBA 透明背景（转为白色背景）
- **iOS 兼容性**: iOS 11+ 原生支持 HEIF 格式

## 🛠 工具介绍

### 主要脚本

1. **`convert_png_to_heif.sh`** - 主转换脚本
   - 批量转换指定目录的 PNG 文件
   - 自动备份原文件
   - 更新 Contents.json 文件引用
   - 生成详细转换日志

2. **`test_conversion.sh`** - 测试脚本
   - 转换单个文件进行测试
   - 验证转换质量和文件大小
   - 安全测试，不影响项目文件

## 🚀 使用方法

### 前置要求

1. **安装依赖**（需要 Python 3 和 pip）:
   ```bash
   pip3 install Pillow pillow-heif
   ```

2. **验证环境**:
   ```bash
   python3 -c "from PIL import Image; import pillow_heif; print('环境配置成功')"
   ```

### 基本使用

1. **先运行测试**（推荐）:
   ```bash
   cd /path/to/your/project
   ./scripts/image-conversion/test_conversion.sh
   ```

2. **执行完整转换**:
   ```bash
   cd /path/to/your/project
   ./scripts/image-conversion/convert_png_to_heif.sh
   ```

### 转换范围配置

默认转换以下目录：
- `CardBackground` - 卡片背景图片
- `CardCategoryIcons` - 卡片分类图标
- `bank_icon` - 银行图标

如需修改转换范围，编辑脚本中的 `DIRECTORIES_TO_CONVERT` 数组：

```bash
DIRECTORIES_TO_CONVERT=(
    "CardBackground"
    "CardCategoryIcons" 
    "bank_icon"
    # "your_custom_directory"  # 添加自定义目录
)
```

## 📋 转换流程

### 自动化处理流程

1. **创建备份** - 将所有原 PNG 文件备份到带时间戳的目录
2. **批量转换** - 使用 Python PIL 库进行高质量转换
3. **更新引用** - 自动修改 Contents.json 文件中的文件引用
4. **生成日志** - 详细记录转换结果和统计信息
5. **验证完整性** - 检查转换成功率和文件完整性

### 安全特性

- ✅ **自动备份**: 转换前自动备份所有原文件
- ✅ **原子操作**: 单个文件转换失败不影响其他文件
- ✅ **回滚支持**: 可通过备份快速恢复原状态
- ✅ **Git 友好**: 转换结果不自动提交，需手动检查

## 📁 文件结构

转换后的项目结构：
```
Project/
├── scripts/
│   └── image-conversion/
│       ├── README.md                    # 本文档
│       ├── convert_png_to_heif.sh      # 主转换脚本
│       └── test_conversion.sh          # 测试脚本
├── backup_png_YYYYMMDD_HHMMSS/         # PNG备份目录
├── conversion_log.txt                   # 转换日志
└── CStory/
    └── Assets.xcassets/
        ├── CardBackground/              # 已转换为 HEIF
        ├── CardCategoryIcons/           # 已转换为 HEIF
        └── bank_icon/                   # 已转换为 HEIF
```

## 🔧 高级配置

### 自定义转换参数

编辑 `convert_png_to_heif.sh` 中的转换参数：

```bash
# 调整图片质量 (1-100)
img.save('$heif_file', 'HEIF', quality=95)

# 修改备份路径
BACKUP_PATH="/custom/backup/path/backup_png_$(date +%Y%m%d_%H%M%S)"

# 自定义日志位置
LOG_FILE="/custom/log/path/conversion_log.txt"
```

### 批量处理多个项目

如需处理多个项目，可修改脚本开头的路径配置：

```bash
# 项目根目录
PROJECT_ROOT="/path/to/your/project"
ASSETS_PATH="$PROJECT_ROOT/YourApp/Assets.xcassets"
```

## 📊 转换统计示例

实际转换效果（基于本项目数据）：

```
转换统计:
总文件数: 1086
成功转换: 1086
转换失败: 0
节省空间: ~6MB (约43%的空间节省)
转换时间: ~2分钟
```

## ⚠️ 注意事项

### 重要提醒

1. **备份检查**: 转换前务必确认项目已提交到版本控制
2. **测试优先**: 大规模转换前先运行测试脚本
3. **质量验证**: 转换后检查关键图片的显示效果
4. **兼容性**: 确保目标iOS版本支持HEIF格式（iOS 11+）

### 已知限制

- **透明背景**: HEIF不完全支持透明度，会转换为白色背景
- **动图支持**: 当前版本不支持动画图片转换
- **颜色空间**: 可能存在轻微的颜色偏差（通常不可察觉）

### 故障排除

#### 常见问题

**Q: 提示 "Success" 但文件未转换？**
```bash
# 检查Python依赖
python3 -c "import pillow_heif; print('HEIF支持正常')"

# 重新安装依赖
pip3 install --upgrade Pillow pillow-heif
```

**Q: 转换后图片显示异常？**
```bash
# 检查图片完整性
file path/to/converted/image.heic

# 查看转换日志
cat conversion_log.txt | grep "转换失败"
```

**Q: 如何恢复原PNG文件？**
```bash
# 找到备份目录
ls backup_png_*

# 恢复特定文件
cp backup_png_YYYYMMDD_HHMMSS/filename.png CStory/Assets.xcassets/path/

# 批量恢复（谨慎使用）
# cp -r backup_png_YYYYMMDD_HHMMSS/* CStory/Assets.xcassets/
```

## 🔄 版本历史

### v1.0.0 (2025-07-31)
- ✨ 初始版本发布
- ✅ 支持PNG到HEIF批量转换
- ✅ 自动备份和日志记录
- ✅ Contents.json自动更新
- ✅ 测试脚本支持

## 📞 技术支持

如遇到问题或需要功能扩展，可以：

1. 检查转换日志文件 `conversion_log.txt`
2. 运行测试脚本验证环境配置
3. 查看备份目录确认文件安全
4. 使用 `git status` 检查文件变更状态

---

**⚡ 快速开始**: `./scripts/image-conversion/test_conversion.sh` → `./scripts/image-conversion/convert_png_to_heif.sh`