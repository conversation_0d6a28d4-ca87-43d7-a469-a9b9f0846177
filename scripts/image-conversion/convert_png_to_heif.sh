#!/bin/bash

#===============================================================================
# PNG to HEIF 图片转换脚本
#===============================================================================
#
# 用途: 批量转换iOS项目Assets.xcassets中的PNG图片为HEIF格式
# 作者: Claude Code Assistant
# 版本: v1.0.0
# 日期: 2025-07-31
#
# 功能特性:
# - 批量转换指定目录的PNG文件为HEIF格式
# - 自动备份原文件确保数据安全
# - 自动更新Contents.json文件引用
# - 生成详细的转换日志和统计信息
# - 支持透明背景处理（转为白色背景）
#
# 使用方法:
#   1. 确保已安装Python依赖: pip3 install Pillow pillow-heif  
#   2. 修改下方配置部分的路径设置
#   3. 执行脚本: ./convert_png_to_heif.sh
#
# 注意事项:
# - 转换前请确保项目已提交到版本控制
# - 建议先运行test_conversion.sh进行测试
# - HEIF格式需要iOS 11+支持
#
#===============================================================================

# 错误处理: 遇到错误时退出脚本
set -e

#===============================================================================
# 配置区域 - 请根据实际情况修改以下路径
#===============================================================================

# 项目Assets.xcassets路径
ASSETS_PATH="/Users/<USER>/代码/CStory/CStory/Assets.xcassets"

# 备份目录路径（自动添加时间戳）
BACKUP_PATH="/Users/<USER>/代码/CStory/backup_png_$(date +%Y%m%d_%H%M%S)"

# 转换日志文件路径
LOG_FILE="/Users/<USER>/代码/CStory/conversion_log.txt"

# 要转换的目录列表（可根据需要修改）
DIRECTORIES_TO_CONVERT=(
    "CardBackground"      # 卡片背景图片
    "CardCategoryIcons"   # 卡片分类图标
    "bank_icon"          # 银行图标
    # "your_directory"    # 添加自定义目录
)

# 转换质量设置（1-100，95为推荐值）
HEIF_QUALITY=95

#===============================================================================
# 初始化
#===============================================================================

echo "=========================================="
echo "PNG to HEIF 图片转换工具 v1.0.0"
echo "=========================================="
echo ""

# 创建备份目录
echo "📁 创建备份目录: $BACKUP_PATH"
mkdir -p "$BACKUP_PATH"

# 初始化日志文件
cat > "$LOG_FILE" << EOF
PNG到HEIF转换日志 - $(date)
=======================================
项目路径: $ASSETS_PATH
备份路径: $BACKUP_PATH
转换质量: $HEIF_QUALITY%
目标目录: ${DIRECTORIES_TO_CONVERT[*]}
=======================================

EOF

#===============================================================================
# 统计变量
#===============================================================================

total_files=0        # 总文件数
converted_files=0    # 成功转换文件数
failed_files=0       # 转换失败文件数

#===============================================================================
# 函数定义
#===============================================================================

# PNG转HEIF转换函数
# 参数: $1 - PNG文件路径
convert_png_to_heif() {
    local png_file="$1"
    local heif_file="${png_file%.png}.heic"
    local backup_file="$BACKUP_PATH/$(basename "$png_file")"
    
    echo "🔄 转换: $(basename "$png_file") -> $(basename "$heif_file")"
    
    # 备份原文件
    cp "$png_file" "$backup_file"
    
    # 使用Python PIL转换
    if python3 -c "
import sys
from PIL import Image
import pillow_heif

# 注册HEIF格式支持
pillow_heif.register_heif_opener()

try:
    # 打开PNG文件
    img = Image.open('$png_file')
    
    # 处理RGBA透明背景（HEIF不完全支持透明度）
    if img.mode == 'RGBA':
        # 创建白色背景
        background = Image.new('RGB', img.size, (255, 255, 255))
        background.paste(img, mask=img.split()[-1])
        img = background
    
    # 转换为RGB模式（确保兼容性）
    if img.mode != 'RGB':
        img = img.convert('RGB')
    
    # 保存为HEIF格式
    img.save('$heif_file', 'HEIF', quality=$HEIF_QUALITY)
    print('转换成功')
    
except Exception as e:
    print(f'转换失败: {e}', file=sys.stderr)
    sys.exit(1)
" 2>/dev/null; then
        # 转换成功，删除原PNG文件
        rm "$png_file"
        echo "  ✅ 成功转换: $(basename "$png_file")" >> "$LOG_FILE"
        ((converted_files++))
        return 0
    else
        echo "  ❌ 转换失败: $(basename "$png_file")" >> "$LOG_FILE"
        ((failed_files++))
        return 1
    fi
}

# 更新Contents.json文件函数
# 参数: $1 - Contents.json文件路径
update_contents_json() {
    local contents_file="$1"
    
    if [[ -f "$contents_file" ]]; then
        echo "📝 更新配置文件: $(basename "$(dirname "$contents_file")")/Contents.json"
        
        # 备份Contents.json文件
        local backup_name="$(basename "$(dirname "$contents_file")")_Contents.json"
        cp "$contents_file" "$BACKUP_PATH/$backup_name"
        
        # 将.png引用替换为.heic
        sed -i '' 's/\.png/\.heic/g' "$contents_file"
        echo "  ✅ 更新配置文件: $(dirname "$contents_file")" >> "$LOG_FILE"
    fi
}

# 显示进度信息函数
# 参数: $1 - 当前处理数量, $2 - 总数量, $3 - 描述
show_progress() {
    local current=$1
    local total=$2
    local desc=$3
    local percentage=$((current * 100 / total))
    
    echo "📊 进度: $current/$total ($percentage%) - $desc"
}

#===============================================================================
# 主要转换流程
#===============================================================================

echo "🚀 开始PNG到HEIF转换..."
echo "📂 目标目录: ${DIRECTORIES_TO_CONVERT[*]}"
echo ""

# 遍历每个指定目录
for dir in "${DIRECTORIES_TO_CONVERT[@]}"; do
    target_dir="$ASSETS_PATH/$dir"
    
    # 检查目录是否存在
    if [[ ! -d "$target_dir" ]]; then
        echo "⚠️  警告: 目录不存在 - $dir"
        echo "⚠️  跳过目录: $dir (不存在)" >> "$LOG_FILE"
        continue
    fi
    
    echo ""
    echo "📁 处理目录: $dir"
    echo "==================="
    
    # 统计当前目录的PNG文件数量
    local dir_png_count=$(find "$target_dir" -name "*.png" -type f | wc -l | tr -d ' ')
    echo "📊 发现PNG文件: $dir_png_count 个"
    
    if [[ $dir_png_count -eq 0 ]]; then
        echo "ℹ️  该目录无PNG文件，跳过"
        continue
    fi
    
    # 转换PNG文件
    local current_file=0
    while IFS= read -r -d '' png_file; do
        ((total_files++))
        ((current_file++))
        
        show_progress $current_file $dir_png_count "$(basename "$png_file")"
        convert_png_to_heif "$png_file"
        
    done < <(find "$target_dir" -name "*.png" -type f -print0)
    
    # 更新该目录下所有Contents.json文件
    echo ""
    echo "📝 更新配置文件..."
    while IFS= read -r -d '' contents_file; do
        update_contents_json "$contents_file"
    done < <(find "$target_dir" -name "Contents.json" -type f -print0)
    
    echo "✅ 目录 $dir 处理完成"
done

#===============================================================================
# 转换结果统计和报告
#===============================================================================

echo ""
echo "=========================================="
echo "🎉 转换完成！"
echo "=========================================="
echo "📊 转换统计:"
echo "   总文件数: $total_files"
echo "   成功转换: $converted_files"
echo "   转换失败: $failed_files"
echo "   成功率: $(( converted_files * 100 / (total_files > 0 ? total_files : 1) ))%"
echo ""
echo "📁 备份位置: $BACKUP_PATH"
echo "📝 日志文件: $LOG_FILE"

# 将统计信息写入日志
cat >> "$LOG_FILE" << EOF

=======================================
转换统计报告:
=======================================
总文件数: $total_files
成功转换: $converted_files  
转换失败: $failed_files
成功率: $(( converted_files * 100 / (total_files > 0 ? total_files : 1) ))%
完成时间: $(date)
EOF

#===============================================================================
# 文件大小对比分析
#===============================================================================

if [[ $converted_files -gt 0 ]]; then
    echo ""
    echo "📈 存储空间分析..."
    
    # 计算备份和当前目录大小
    backup_size=$(du -sh "$BACKUP_PATH" 2>/dev/null | cut -f1 || echo "未知")
    current_size=$(du -sh "$ASSETS_PATH" 2>/dev/null | cut -f1 || echo "未知")
    
    echo "   原文件大小 (备份): $backup_size"  
    echo "   转换后大小: $current_size"
    
    # 写入日志
    echo "" >> "$LOG_FILE"
    echo "存储空间对比:" >> "$LOG_FILE"
    echo "原文件大小: $backup_size" >> "$LOG_FILE"
    echo "转换后大小: $current_size" >> "$LOG_FILE"
fi

#===============================================================================
# 安全提醒和后续操作指引
#===============================================================================

echo ""
echo "🔐 安全提醒:"
echo "   ✅ 原文件已安全备份"
echo "   ⚠️  转换结果未自动提交到git"
echo "   📋 请检查图片质量后再决定是否提交"
echo ""
echo "🛠 后续操作:"
echo "   1. 检查关键图片显示效果"
echo "   2. 运行项目确认无异常"  
echo "   3. 使用 git status 查看变更"
echo "   4. 满意后提交: git add . && git commit"
echo ""
echo "🗑 清理备份（确认无问题后）:"
echo "   rm -rf \"$BACKUP_PATH\""
echo ""
echo "🔄 恢复原文件（如有问题）:"
echo "   # 查看备份内容"
echo "   ls \"$BACKUP_PATH\""
echo "   # 恢复特定文件"  
echo "   # cp \"$BACKUP_PATH/filename.png\" \"target/path/\""

echo ""
echo "转换完成！感谢使用PNG to HEIF转换工具 🎊"