#!/bin/bash

#===============================================================================
# 单文件 PNG to HEIF 转换脚本
#===============================================================================
#
# 用途: 转换指定的单个PNG文件为HEIF格式
# 作者: Claude Code Assistant
# 版本: v1.0.0
# 日期: 2025-07-31
#
# 功能特性:
# - 转换指定的单个PNG文件为HEIF格式
# - 自动备份原文件确保数据安全
# - 自动更新对应的Contents.json文件引用
# - 生成转换日志
# - 支持透明背景处理（转为白色背景）
#
# 使用方法:
#   ./convert_single_file.sh "relative/path/to/image.png"
#   
# 示例:
#   ./convert_single_file.sh "Images/question.imageset/question.png"
#   ./convert_single_file.sh "Images/question.imageset/<EMAIL>"
#   ./convert_single_file.sh "Images/question.imageset/<EMAIL>"
#
# 注意事项:
# - 路径相对于 Assets.xcassets 目录
# - 转换前请确保项目已提交到版本控制
# - HEIF格式需要iOS 11+支持
#
#===============================================================================

# 错误处理: 遇到错误时退出脚本
set -e

#===============================================================================
# 配置区域
#===============================================================================

# 项目Assets.xcassets路径
ASSETS_PATH="/Users/<USER>/代码/CStory/CStory/Assets.xcassets"

# 备份目录路径（自动添加时间戳）
BACKUP_PATH="/Users/<USER>/代码/CStory/backup_single_png_$(date +%Y%m%d_%H%M%S)"

# 转换日志文件路径
LOG_FILE="/Users/<USER>/代码/CStory/single_conversion_log.txt"

# 转换质量设置（1-100，95为推荐值）
HEIF_QUALITY=95

#===============================================================================
# 参数检查
#===============================================================================

echo "=========================================="
echo "单文件 PNG to HEIF 转换工具 v1.0.0"
echo "=========================================="
echo ""

# 检查是否提供了文件路径参数
if [[ $# -eq 0 ]]; then
    echo "❌ 错误: 请提供要转换的PNG文件路径"
    echo ""
    echo "📖 使用方法:"
    echo "   $0 \"relative/path/to/image.png\""
    echo ""
    echo "💡 示例:"
    echo "   $0 \"Images/question.imageset/question.png\""
    echo "   $0 \"Images/question.imageset/<EMAIL>\""
    echo "   $0 \"Images/question.imageset/<EMAIL>\""
    echo ""
    echo "📁 路径说明: 相对于 Assets.xcassets 目录"
    exit 1
fi

# 获取相对路径参数
RELATIVE_PATH="$1"
FULL_PATH="$ASSETS_PATH/$RELATIVE_PATH"

# 检查文件是否存在
if [[ ! -f "$FULL_PATH" ]]; then
    echo "❌ 错误: 文件不存在"
    echo "📂 查找路径: $FULL_PATH"
    echo ""
    echo "💡 请检查:"
    echo "   1. 文件路径是否正确"
    echo "   2. 文件是否存在于 Assets.xcassets 目录中"
    echo "   3. 路径是否使用正确的相对路径格式"
    exit 1
fi

# 检查是否为PNG文件
if [[ "${FULL_PATH##*.}" != "png" ]]; then
    echo "❌ 错误: 指定的文件不是PNG格式"
    echo "📄 文件: $RELATIVE_PATH"
    echo "💡 此脚本仅支持PNG到HEIF的转换"
    exit 1
fi

echo "✅ 找到目标文件: $RELATIVE_PATH"

#===============================================================================
# 环境检查
#===============================================================================

echo ""
echo "🔍 检查环境依赖..."

# 检查Python3
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到python3，请先安装Python 3"
    exit 1
fi

# 检查PIL和pillow-heif依赖
if ! python3 -c "
try:
    from PIL import Image
    import pillow_heif
    print('✅ Python依赖检查通过')
except ImportError as e:
    print(f'❌ 缺少Python依赖: {e}')
    print('请安装: pip3 install Pillow pillow-heif')
    exit(1)
" 2>/dev/null; then
    echo "❌ Python依赖检查失败"
    echo "💡 解决方案: pip3 install Pillow pillow-heif"
    exit 1
fi

#===============================================================================
# 初始化
#===============================================================================

# 创建备份目录
echo "📁 创建备份目录: $BACKUP_PATH"
mkdir -p "$BACKUP_PATH"

# 初始化日志文件
cat > "$LOG_FILE" << EOF
单文件PNG到HEIF转换日志 - $(date)
=======================================
目标文件: $RELATIVE_PATH
完整路径: $FULL_PATH
备份路径: $BACKUP_PATH
转换质量: $HEIF_QUALITY%
=======================================

EOF

#===============================================================================
# 文件信息分析
#===============================================================================

echo ""
echo "📊 文件信息分析:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# 显示文件基本信息
echo "📄 文件路径: $RELATIVE_PATH"
echo "📏 文件大小: $(ls -lh "$FULL_PATH" | awk '{print $5}')"
echo "🎨 文件类型: $(file "$FULL_PATH" | cut -d: -f2 | xargs)"

# 获取文件大小（字节）
original_size=$(stat -f%z "$FULL_PATH")
echo "💾 精确大小: $original_size bytes"

#===============================================================================
# 转换函数定义
#===============================================================================

# PNG转HEIF转换函数
convert_png_to_heif() {
    local png_file="$1"
    local heif_file="${png_file%.png}.heic"
    local backup_file="$BACKUP_PATH/$(basename "$png_file")"
    
    echo ""
    echo "🔄 开始转换..."
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "📋 原文件: $(basename "$png_file")"
    echo "🎯 目标文件: $(basename "$heif_file")"
    
    # 备份原文件
    echo "📁 备份原文件..."
    cp "$png_file" "$backup_file"
    
    # 使用Python PIL转换
    if python3 -c "
import sys
from PIL import Image
import pillow_heif

# 注册HEIF格式支持
pillow_heif.register_heif_opener()

try:
    # 打开PNG文件
    img = Image.open('$png_file')
    
    # 显示图片信息
    print(f'📐 图片尺寸: {img.size[0]}x{img.size[1]}')
    print(f'🎨 颜色模式: {img.mode}')
    
    # HEIF支持透明度，保持原始颜色模式
    if img.mode == 'RGBA':
        print('🔧 保持RGBA透明背景')
    elif img.mode == 'RGB':
        print('🔧 保持RGB颜色模式')
    else:
        print(f'🔧 转换颜色模式: {img.mode} -> RGBA')
        img = img.convert('RGBA')
    
    # 保存为HEIF格式
    img.save('$heif_file', 'HEIF', quality=$HEIF_QUALITY)
    print('✅ 转换成功完成')
    
except Exception as e:
    print(f'❌ 转换失败: {e}', file=sys.stderr)
    sys.exit(1)
" 2>/dev/null; then
        # 转换成功，删除原PNG文件
        rm "$png_file"
        echo "🗑️  已删除原PNG文件"
        echo "✅ 成功转换: $(basename "$png_file") -> $(basename "$heif_file")" >> "$LOG_FILE"
        return 0
    else
        echo "❌ 转换失败: $(basename "$png_file")" >> "$LOG_FILE"
        return 1
    fi
}

# 更新Contents.json文件函数
update_contents_json() {
    local imageset_dir="$(dirname "$1")"
    local contents_file="$imageset_dir/Contents.json"
    
    if [[ -f "$contents_file" ]]; then
        echo "📝 更新配置文件: $(basename "$imageset_dir")/Contents.json"
        
        # 备份Contents.json文件
        local backup_name="$(basename "$imageset_dir")_Contents.json"
        cp "$contents_file" "$BACKUP_PATH/$backup_name"
        
        # 将.png引用替换为.heic
        sed -i '' 's/\.png/\.heic/g' "$contents_file"
        echo "✅ 更新配置文件: $contents_file" >> "$LOG_FILE"
    else
        echo "⚠️  警告: 未找到Contents.json文件: $contents_file"
        echo "⚠️  未找到Contents.json: $contents_file" >> "$LOG_FILE"
    fi
}

#===============================================================================
# 执行转换
#===============================================================================

# 执行转换
if convert_png_to_heif "$FULL_PATH"; then
    # 更新Contents.json文件
    echo ""
    echo "📝 更新配置文件..."
    update_contents_json "$FULL_PATH"
    
    # 转换结果分析
    heif_file="${FULL_PATH%.png}.heic"
    
    echo ""
    echo "📊 转换结果分析:"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    # 显示新文件信息
    echo "📄 HEIF文件: $(basename "$heif_file")"
    echo "📏 文件大小: $(ls -lh "$heif_file" | awk '{print $5}')"
    echo "🎨 文件类型: $(file "$heif_file" | cut -d: -f2 | xargs)"
    
    # 获取新文件大小并计算节省空间
    new_size=$(stat -f%z "$heif_file")
    echo "💾 精确大小: $new_size bytes"
    
    if [[ $new_size -lt $original_size ]]; then
        savings=$((original_size - new_size))
        percentage=$(( (savings * 100) / original_size ))
        echo ""
        echo "💰 空间节省分析:"
        echo "   节省空间: $savings bytes"
        echo "   节省比例: $percentage%"
        echo "   压缩效果: $(echo "scale=1; $new_size * 100 / $original_size" | bc)% 的原始大小"
    elif [[ $new_size -gt $original_size ]]; then
        increase=$((new_size - original_size))
        percentage=$(( (increase * 100) / original_size ))
        echo ""
        echo "⚠️  文件大小增加:"
        echo "   增加空间: $increase bytes"
        echo "   增加比例: $percentage%"
        echo "💡 某些简单图片转换为HEIF可能增大，这是正常现象"
    else
        echo ""
        echo "ℹ️  文件大小相同"
    fi
    
    # 写入转换结果到日志
    cat >> "$LOG_FILE" << EOF

转换结果统计:
=======================================
原文件大小: $original_size bytes
转换后大小: $new_size bytes
EOF
    
    if [[ $new_size -lt $original_size ]]; then
        cat >> "$LOG_FILE" << EOF
节省空间: $((original_size - new_size)) bytes ($((($original_size - $new_size) * 100 / $original_size))%)
EOF
    fi
    
    echo "完成时间: $(date)" >> "$LOG_FILE"
    
    echo ""
    echo "=========================================="
    echo "🎉 转换完成！"
    echo "=========================================="
    echo "✅ 文件转换成功"
    echo "✅ 配置文件已更新"
    echo "✅ 原文件已备份"
    echo ""
    echo "📁 备份位置: $BACKUP_PATH"
    echo "📝 日志文件: $LOG_FILE"
    echo ""
    echo "🔐 安全提醒:"
    echo "   ✅ 原文件已安全备份"
    echo "   ⚠️  转换结果未自动提交到git"
    echo "   📋 请检查图片质量后再决定是否提交"
    echo ""
    echo "🛠 后续操作:"
    echo "   1. 检查转换后图片显示效果"
    echo "   2. 运行项目确认无异常"  
    echo "   3. 使用 git status 查看变更"
    echo "   4. 满意后提交: git add . && git commit"
    echo ""
    echo "🔄 恢复原文件（如有问题）:"
    echo "   cp \"$BACKUP_PATH/$(basename "$FULL_PATH")\" \"$FULL_PATH\""
    echo "   # 然后手动恢复Contents.json文件"
    
else
    echo ""
    echo "❌ 转换失败"
    echo ""
    echo "🔧 故障排除建议:"
    echo "1. 检查Python依赖安装: pip3 install --upgrade Pillow pillow-heif"
    echo "2. 确认文件权限正常"
    echo "3. 检查磁盘空间是否充足"
    echo "4. 查看转换日志: cat \"$LOG_FILE\""
    
    # 清理备份目录（因为转换失败）
    rm -rf "$BACKUP_PATH"
    exit 1
fi

echo ""
echo "感谢使用单文件PNG to HEIF转换工具！🎊"